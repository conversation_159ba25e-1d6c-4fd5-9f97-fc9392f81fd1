<template>
  <el-dialog
    :model-value="visible"
    :title="isEdit ? '编辑发送渠道' : '新增发送渠道'"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="渠道编码" prop="channelCode">
            <el-input v-model="formData.channelCode" placeholder="请输入渠道编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="渠道名称" prop="channelName">
            <el-input v-model="formData.channelName" placeholder="请输入渠道名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="渠道类型" prop="channelType">
            <el-select v-model="formData.channelType" placeholder="请选择渠道类型">
              <el-option label="短信" :value="1" />
              <el-option label="邮件" :value="2" />
              <el-option label="推送" :value="3" />
              <el-option label="语音" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务商类型" prop="providerType">
            <el-select v-model="formData.providerType" placeholder="请选择服务商类型">
              <el-option label="移动" :value="1" />
              <el-option label="联通" :value="2" />
              <el-option label="电信" :value="3" />
              <el-option label="阿里云" :value="4" />
              <el-option label="腾讯云" :value="5" />
              <el-option label="华为云" :value="6" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="服务商名称" prop="providerName">
        <el-input v-model="formData.providerName" placeholder="请输入服务商名称" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="优先级" prop="priority">
            <el-input-number v-model="formData.priority" :min="1" :max="100" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="权重" prop="weight">
            <el-input-number v-model="formData.weight" :min="1" :max="1000" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
              <el-option label="维护" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="日发送限制">
            <el-input-number v-model="formData.dailyLimit" :min="0" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="分钟发送限制">
            <el-input-number v-model="formData.minuteLimit" :min="0" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="成功率阈值">
            <el-input-number v-model="formData.successRateThreshold" :min="0" :max="100" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="支持回调">
            <el-select v-model="formData.supportCallback" placeholder="请选择">
              <el-option label="支持" :value="1" />
              <el-option label="不支持" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="回调地址" v-if="formData.supportCallback === 1">
        <el-input v-model="formData.callbackUrl" placeholder="请输入回调地址" />
      </el-form-item>

      <el-form-item label="渠道描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入渠道描述"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { createSendChannel, updateSendChannel } from '@/api/channel';

const props = defineProps({
  visible: Boolean,
  isEdit: Boolean,
  data: Object
});

const emit = defineEmits(['close', 'success']);

const formRef = ref();
const loading = ref(false);

const formData = reactive({
  channelCode: '',
  channelName: '',
  channelType: null,
  providerType: null,
  providerName: '',
  priority: 1,
  weight: 100,
  dailyLimit: 10000,
  minuteLimit: 100,
  successRateThreshold: 95,
  supportCallback: 0,
  callbackUrl: '',
  description: '',
  status: 1,
  sortOrder: 1
});

const rules = {
  channelCode: [
    { required: true, message: '请输入渠道编码', trigger: 'blur' },
    { max: 50, message: '渠道编码长度不能超过50个字符', trigger: 'blur' }
  ],
  channelName: [
    { required: true, message: '请输入渠道名称', trigger: 'blur' },
    { max: 100, message: '渠道名称长度不能超过100个字符', trigger: 'blur' }
  ],
  channelType: [
    { required: true, message: '请选择渠道类型', trigger: 'change' }
  ],
  providerType: [
    { required: true, message: '请选择服务商类型', trigger: 'change' }
  ],
  providerName: [
    { required: true, message: '请输入服务商名称', trigger: 'blur' },
    { max: 100, message: '服务商名称长度不能超过100个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

watch(() => props.visible, (newVal) => {
  if (newVal && props.isEdit && props.data) {
    Object.assign(formData, props.data);
  } else if (newVal && !props.isEdit) {
    resetForm();
  }
});

const resetForm = () => {
  Object.assign(formData, {
    channelCode: '',
    channelName: '',
    channelType: null,
    providerType: null,
    providerName: '',
    priority: 1,
    weight: 100,
    dailyLimit: 10000,
    minuteLimit: 100,
    successRateThreshold: 95,
    supportCallback: 0,
    callbackUrl: '',
    description: '',
    status: 1,
    sortOrder: 1
  });
};

const handleClose = () => {
  formRef.value?.resetFields();
  emit('close');
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    loading.value = true;

    const apiCall = props.isEdit 
      ? updateSendChannel(props.data.id, formData)
      : createSendChannel(formData);

    const response = await apiCall;
    
    if (response.code === 200) {
      ElMessage.success(props.isEdit ? '更新成功' : '创建成功');
      emit('success');
      handleClose();
    } else {
      ElMessage.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('提交失败:', error);
    ElMessage.error('操作失败');
  } finally {
    loading.value = false;
  }
};
</script>