import request from '@/utils/request'

// 查询用户列表
export function getUserList(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}

// 查询用户详情
export function getUserById(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  // 设置默认机构ID为1，因为不需要机构管理
  const userData = {
    ...data,
    orgId: 1
  };
  return request({
    url: '/api/users',
    method: 'post',
    data: userData
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/api/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  })
}

// 分配角色
export function assignRoles(id, roleIds) {
  return request({
    url: `/api/users/${id}/roles`,
    method: 'post',
    data: roleIds
  })
}

// 查询用户角色
export function getUserRoles(id) {
  return request({
    url: `/api/users/${id}/roles`,
    method: 'get'
  })
}

// 重置密码
export function resetPassword(id) {
  return request({
    url: `/api/users/${id}/reset-password`,
    method: 'post'
  })
}

// 更新用户状态
export function updateUserStatus(id, status) {
  return request({
    url: `/api/users/${id}/status/${status}`,
    method: 'post'
  })
}

// 检查登录名
export function checkLoginName(loginName, excludeId) {
  return request({
    url: '/api/users/check-login-name',
    method: 'get',
    params: { loginName, excludeId }
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/auth/change-password',
    method: 'post',
    data
  })
}