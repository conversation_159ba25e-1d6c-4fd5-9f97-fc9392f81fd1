const path = require('path');
const fs = require('fs');

class ScreenshotHelper {
  constructor(page, testId) {
    this.page = page;
    this.testId = testId;
    this.screenshotCount = 0;
    this.screenshots = [];
    this.screenshotDir = './test-results/screenshots';
    
    // 确保截图目录存在
    this.ensureDirectoryExists();
  }

  /**
   * 确保截图目录存在
   */
  ensureDirectoryExists() {
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true });
    }
  }

  /**
   * 生成截图文件名
   * @param {string} description - 截图描述
   */
  generateFileName(description) {
    this.screenshotCount++;
    const stepNumber = this.screenshotCount.toString().padStart(2, '0');
    const cleanDescription = description.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-');
    return `${this.testId}-${stepNumber}-${cleanDescription}.png`;
  }

  /**
   * 截取页面截图
   * @param {string} description - 截图描述
   * @param {Object} options - 截图选项
   */
  async takeScreenshot(description, options = {}) {
    const fileName = this.generateFileName(description);
    const filePath = path.join(this.screenshotDir, fileName);

    const defaultOptions = {
      path: filePath,
      fullPage: true,
      ...options
    };

    await this.page.screenshot(defaultOptions);

    // 保存截图信息
    const screenshotInfo = {
      filename: fileName,
      description: description,
      timestamp: new Date().toISOString(),
      size: 'Unknown' // 可以后续获取文件大小
    };
    this.screenshots.push(screenshotInfo);

    console.log(`📸 截图已保存: ${fileName} - ${description}`);
    return fileName;
  }

  /**
   * 截取元素截图
   * @param {string} selector - 元素选择器
   * @param {string} description - 截图描述
   */
  async takeElementScreenshot(selector, description) {
    const fileName = this.generateFileName(description);
    const filePath = path.join(this.screenshotDir, fileName);
    
    await this.page.waitForSelector(selector);
    const element = this.page.locator(selector);
    
    await element.screenshot({ path: filePath });
    
    console.log(`📸 元素截图已保存: ${fileName} - ${description}`);
    return fileName;
  }

  // 预定义的截图方法
  async takeInitialScreenshot() {
    return await this.takeScreenshot('页面初始状态');
  }

  async takeAfterLoginScreenshot() {
    return await this.takeScreenshot('登录后状态');
  }

  async takeFormFilledScreenshot() {
    return await this.takeScreenshot('表单填写完成');
  }

  async takeBeforeSubmitScreenshot() {
    return await this.takeScreenshot('提交前状态');
  }

  async takeAfterSubmitScreenshot() {
    return await this.takeScreenshot('提交后状态');
  }

  async takeSuccessScreenshot() {
    return await this.takeScreenshot('操作成功状态');
  }

  async takeErrorScreenshot() {
    return await this.takeScreenshot('错误状态');
  }

  async takeValidationScreenshot() {
    return await this.takeScreenshot('验证结果');
  }

  async takeSearchResultScreenshot() {
    return await this.takeScreenshot('搜索结果');
  }

  async takeListPageScreenshot() {
    return await this.takeScreenshot('列表页面');
  }

  async takeDetailPageScreenshot() {
    return await this.takeScreenshot('详情页面');
  }

  async takeEditPageScreenshot() {
    return await this.takeScreenshot('编辑页面');
  }

  async takeDeleteConfirmScreenshot() {
    return await this.takeScreenshot('删除确认对话框');
  }

  async takeModalScreenshot() {
    return await this.takeScreenshot('模态对话框');
  }

  async takeDashboardScreenshot() {
    return await this.takeScreenshot('仪表板页面');
  }

  async takeNavigationScreenshot() {
    return await this.takeScreenshot('导航菜单');
  }

  async takeTableScreenshot() {
    return await this.takeElementScreenshot('.el-table', '数据表格');
  }

  async takeFormScreenshot() {
    return await this.takeElementScreenshot('.el-form', '表单区域');
  }

  async takeMessageScreenshot() {
    return await this.takeElementScreenshot('.el-message', '消息提示');
  }

  async takePaginationScreenshot() {
    return await this.takeElementScreenshot('.el-pagination', '分页组件');
  }

  async takeChartScreenshot() {
    return await this.takeElementScreenshot('.chart-container', '图表');
  }

  async takeFileUploadScreenshot() {
    return await this.takeElementScreenshot('.el-upload', '文件上传');
  }

  async takeDatePickerScreenshot() {
    return await this.takeElementScreenshot('.el-date-picker', '日期选择器');
  }

  async takeSelectScreenshot() {
    return await this.takeElementScreenshot('.el-select', '下拉选择框');
  }

  async takeTabsScreenshot() {
    return await this.takeElementScreenshot('.el-tabs', '标签页');
  }

  async takeTreeScreenshot() {
    return await this.takeElementScreenshot('.el-tree', '树形组件');
  }

  async takeStepsScreenshot() {
    return await this.takeElementScreenshot('.el-steps', '步骤条');
  }

  async takeProgressScreenshot() {
    return await this.takeElementScreenshot('.el-progress', '进度条');
  }

  async takeLoadingScreenshot() {
    return await this.takeElementScreenshot('.el-loading', '加载状态');
  }

  async takeEmptyScreenshot() {
    return await this.takeElementScreenshot('.el-empty', '空状态');
  }

  /**
   * 自定义截图
   * @param {string} description - 自定义描述
   * @param {Object} options - 截图选项
   */
  async takeCustomScreenshot(description, options = {}) {
    return await this.takeScreenshot(description, options);
  }

  /**
   * 获取截图统计信息
   */
  getScreenshotStats() {
    return {
      testId: this.testId,
      totalScreenshots: this.screenshotCount,
      screenshotDir: this.screenshotDir
    };
  }

  /**
   * 获取详细截图信息
   */
  getScreenshots() {
    return this.screenshots.map(screenshot => ({
      path: `screenshots/${screenshot.filename}`,
      description: screenshot.description,
      timestamp: screenshot.timestamp,
      size: screenshot.size || 'Unknown'
    }));
  }

  /**
   * 重置截图计数器
   */
  resetCounter() {
    this.screenshotCount = 0;
  }
}

module.exports = ScreenshotHelper;
