/**
 * T231-T240: 系统日志查看功能测试
 * 基于需求文档中的系统日志管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class SystemLogTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T231: 系统日志页面加载测试
   */
  async testT231_SystemLogPageLoad() {
    const testId = 'T231';
    console.log(`\n🧪 执行测试 ${testId}: 系统日志页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到系统日志页面
      await this.testHelper.navigateTo('/system/logs');
      await this.testHelper.waitForPageLoad(selectors.systemLog.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isLogTableVisible = await this.testHelper.verifyElementVisibility(selectors.systemLog.logTable);
      const isDateRangeVisible = await this.testHelper.verifyElementVisibility(selectors.systemLog.dateRangePicker);
      const isLogLevelFilterVisible = await this.testHelper.verifyElementVisibility(selectors.systemLog.logLevelFilter);
      const isSearchInputVisible = await this.testHelper.verifyElementVisibility(selectors.systemLog.searchInput);
      const isRefreshButtonVisible = await this.testHelper.verifyElementVisibility(selectors.systemLog.refreshButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '系统日志页面加载测试',
        testContent: '验证系统日志页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的系统日志查看界面',
        testInput: '访问系统日志页面URL: /system/logs',
        expectedOutput: '页面正常加载，显示日志表格、日期范围选择器、日志级别过滤器、搜索框和刷新按钮',
        actualOutput: `日志表格: ${isLogTableVisible ? '✅显示' : '❌隐藏'}, 日期范围: ${isDateRangeVisible ? '✅显示' : '❌隐藏'}, 级别过滤: ${isLogLevelFilterVisible ? '✅显示' : '❌隐藏'}, 搜索框: ${isSearchInputVisible ? '✅显示' : '❌隐藏'}, 刷新按钮: ${isRefreshButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isLogTableVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '系统日志页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T232: 日志级别筛选测试
   */
  async testT232_LogLevelFilter() {
    const testId = 'T232';
    console.log(`\n🧪 执行测试 ${testId}: 日志级别筛选测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统日志页面
      await this.testHelper.navigateTo('/system/logs');
      await this.testHelper.waitForPageLoad(selectors.systemLog.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取筛选前的日志数据
      const beforeFilterData = await this.testHelper.getTableData(selectors.systemLog.logTable);
      
      // 按日志级别筛选
      try {
        await this.testHelper.page.click(selectors.systemLog.logLevelFilter);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('日志级别筛选器打开');
        
        // 选择ERROR级别
        await this.testHelper.page.click('.log-level-option[data-value="ERROR"]');
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('ERROR级别选择完成');
        
        // 点击查询按钮
        await this.testHelper.page.click(selectors.systemLog.queryButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取筛选后的数据
        const afterFilterData = await this.testHelper.getTableData(selectors.systemLog.logTable);
        
        const result = {
          testId: testId,
          testName: '日志级别筛选测试',
          testContent: '使用日志级别筛选系统日志',
          testPurpose: '验证日志级别筛选功能能够正常工作',
          testInput: '选择ERROR级别进行筛选',
          expectedOutput: '根据日志级别筛选显示相应的日志记录',
          actualOutput: `筛选前记录数: ${beforeFilterData.length}, 筛选后记录数: ${afterFilterData.length}`,
          result: afterFilterData.length >= 0 ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (filterError) {
        // 如果没有日志级别筛选功能，标记为跳过
        const result = {
          testId: testId,
          testName: '日志级别筛选测试',
          testContent: '使用日志级别筛选系统日志',
          testPurpose: '验证日志级别筛选功能能够正常工作',
          testInput: '查找日志级别筛选功能',
          expectedOutput: '找到级别筛选器并成功筛选',
          actualOutput: '未找到日志级别筛选功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到日志级别筛选功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '日志级别筛选测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T233: 日志关键字搜索测试
   */
  async testT233_LogKeywordSearch() {
    const testId = 'T233';
    console.log(`\n🧪 执行测试 ${testId}: 日志关键字搜索测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统日志页面
      await this.testHelper.navigateTo('/system/logs');
      await this.testHelper.waitForPageLoad(selectors.systemLog.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行关键字搜索
      const searchKeyword = 'login';
      await this.testHelper.page.fill(selectors.systemLog.searchInput, searchKeyword);
      await this.screenshotHelper.takeCustomScreenshot('关键字搜索输入');
      
      // 点击搜索按钮
      await this.testHelper.page.click(selectors.systemLog.searchButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取搜索结果
      const searchResults = await this.testHelper.getTableData(selectors.systemLog.logTable);
      
      const result = {
        testId: testId,
        testName: '日志关键字搜索测试',
        testContent: '使用关键字搜索系统日志',
        testPurpose: '验证日志关键字搜索功能能够正常工作',
        testInput: `搜索关键字: ${searchKeyword}`,
        expectedOutput: '显示包含关键字的日志记录',
        actualOutput: `搜索结果记录数: ${searchResults.length}`,
        result: searchResults.length >= 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '日志关键字搜索测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T234: 日志详情查看测试
   */
  async testT234_LogDetailView() {
    const testId = 'T234';
    console.log(`\n🧪 执行测试 ${testId}: 日志详情查看测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统日志页面
      await this.testHelper.navigateTo('/system/logs');
      await this.testHelper.waitForPageLoad(selectors.systemLog.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击第一条日志记录查看详情
      try {
        await this.testHelper.page.click(`${selectors.systemLog.logTable} tr:first-child td:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('日志详情对话框');
        
        // 验证详情对话框是否显示
        const isDetailModalVisible = await this.testHelper.verifyElementVisibility(selectors.common.modal);
        
        const result = {
          testId: testId,
          testName: '日志详情查看测试',
          testContent: '点击日志记录查看详细信息',
          testPurpose: '验证日志详情查看功能能够正常工作',
          testInput: '点击第一条日志记录',
          expectedOutput: '显示日志详情对话框',
          actualOutput: `详情对话框: ${isDetailModalVisible ? '✅显示' : '❌隐藏'}`,
          result: isDetailModalVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (detailError) {
        // 如果没有详情查看功能，标记为跳过
        const result = {
          testId: testId,
          testName: '日志详情查看测试',
          testContent: '点击日志记录查看详细信息',
          testPurpose: '验证日志详情查看功能能够正常工作',
          testInput: '查找日志详情查看功能',
          expectedOutput: '找到详情查看并成功显示',
          actualOutput: '未找到日志详情查看功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到日志详情查看功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '日志详情查看测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T235: 日志导出功能测试
   */
  async testT235_LogExport() {
    const testId = 'T235';
    console.log(`\n🧪 执行测试 ${testId}: 日志导出功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统日志页面
      await this.testHelper.navigateTo('/system/logs');
      await this.testHelper.waitForPageLoad(selectors.systemLog.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 尝试导出日志
      try {
        await this.testHelper.page.click(selectors.systemLog.exportButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('日志导出选项');
        
        // 选择导出格式（如果有选择）
        try {
          await this.testHelper.page.click('.export-format-csv');
        } catch (formatError) {
          // 如果没有格式选择，直接导出
        }
        
        // 确认导出
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 检查是否有成功消息或下载提示
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '日志导出功能测试',
          testContent: '导出系统日志数据',
          testPurpose: '验证日志导出功能能够正常工作',
          testInput: '点击导出按钮，选择CSV格式',
          expectedOutput: '日志导出成功，显示成功提示或开始下载',
          actualOutput: `导出结果: ${successMessage || '无明确提示'}`,
          result: successMessage ? 'PASSED' : 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (exportError) {
        // 如果没有导出功能，标记为跳过
        const result = {
          testId: testId,
          testName: '日志导出功能测试',
          testContent: '导出系统日志数据',
          testPurpose: '验证日志导出功能能够正常工作',
          testInput: '查找日志导出功能',
          expectedOutput: '找到导出按钮并成功导出',
          actualOutput: '未找到日志导出功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到日志导出功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '日志导出功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有系统日志测试
   */
  async runAllTests() {
    console.log('🚀 开始执行系统日志查看功能测试套件 (T231-T235)');
    
    const startTime = Date.now();
    
    await this.testT231_SystemLogPageLoad();
    await this.testT232_LogLevelFilter();
    await this.testT233_LogKeywordSearch();
    await this.testT234_LogDetailView();
    await this.testT235_LogExport();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 系统日志查看功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const systemLogTest = new SystemLogTest();
  systemLogTest.runAllTests().catch(console.error);
}

module.exports = SystemLogTest;
