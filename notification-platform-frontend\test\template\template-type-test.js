/**
 * T151-T160: 模板类型管理功能测试
 * 基于需求文档中的模板类型管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class TemplateTypeTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T151: 模板类型管理页面加载测试
   */
  async testT151_TemplateTypePageLoad() {
    const testId = 'T151';
    console.log(`\n🧪 执行测试 ${testId}: 模板类型管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到模板类型管理页面
      await this.testHelper.navigateTo('/template/type');
      await this.testHelper.waitForPageLoad(selectors.templateType.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isTypeListVisible = await this.testHelper.verifyElementVisibility(selectors.templateType.typeList);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      const isSearchFormVisible = await this.testHelper.verifyElementVisibility(selectors.templateType.searchForm);
      const isTypeTreeVisible = await this.testHelper.verifyElementVisibility(selectors.templateType.typeTree);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '模板类型管理页面加载测试',
        testContent: '验证模板类型管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的模板类型管理界面',
        testInput: '访问模板类型管理页面URL: /template/type',
        expectedOutput: '页面正常加载，显示类型列表、新增按钮、搜索表单和类型树',
        actualOutput: `类型列表: ${isTypeListVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}, 搜索表单: ${isSearchFormVisible ? '✅显示' : '❌隐藏'}, 类型树: ${isTypeTreeVisible ? '✅显示' : '❌隐藏'}`,
        result: isTypeListVisible || isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '模板类型管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T152: 新增模板类型测试
   */
  async testT152_AddTemplateType() {
    const testId = 'T152';
    console.log(`\n🧪 执行测试 ${testId}: 新增模板类型测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板类型管理页面
      await this.testHelper.navigateTo('/template/type');
      await this.testHelper.waitForPageLoad(selectors.templateType.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.templateType.addButton);
      await this.testHelper.wait(testData.timeouts.short);

      // 等待对话框加载
      await this.testHelper.waitForPageLoad('.el-dialog');
      await this.screenshotHelper.takeCustomScreenshot('新增模板类型对话框');
      
      // 生成唯一的类型信息
      const uniqueTypeCode = `TYPE_${this.testHelper.generateRandomString(4)}`;
      const uniqueTypeName = `模板类型_${this.testHelper.generateRandomString(4)}`;
      const typeData = {
        code: uniqueTypeCode,
        name: uniqueTypeName,
        description: `测试模板类型_${this.testHelper.generateRandomString(4)}`,
        sortOrder: '10',
        status: 'ACTIVE'
      };
      
      // 填写类型信息 - 使用更精确的选择器
      await this.testHelper.page.fill('.el-dialog input[placeholder="请输入类型编码"]', typeData.code);
      await this.testHelper.page.fill('.el-dialog input[placeholder="请输入类型名称"]', typeData.name);
      await this.testHelper.page.fill('.el-dialog textarea[placeholder="请输入描述"]', typeData.description);

      // 设置排序号
      await this.testHelper.page.fill('.el-dialog .el-input-number input', typeData.sortOrder);
      
      // 选择状态
      try {
        await this.testHelper.page.selectOption(selectors.templateType.statusSelect, typeData.status);
      } catch (statusError) {
        // 如果没有状态选择器，跳过
      }
      
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存模板类型
      await this.testHelper.page.click(selectors.templateType.saveButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增模板类型测试',
        testContent: '创建一个新的模板类型',
        testPurpose: '验证模板类型新增功能能够正常工作',
        testInput: `类型编码: ${typeData.code}, 类型名称: ${typeData.name}, 描述: ${typeData.description}`,
        expectedOutput: '模板类型创建成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增模板类型测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T153: 模板类型层级管理测试
   */
  async testT153_TemplateTypeHierarchy() {
    const testId = 'T153';
    console.log(`\n🧪 执行测试 ${testId}: 模板类型层级管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板类型管理页面
      await this.testHelper.navigateTo('/template/type');
      await this.testHelper.waitForPageLoad(selectors.templateType.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 创建父级类型
      try {
        await this.testHelper.page.click(selectors.security.addButton);
        await this.testHelper.wait(testData.timeouts.short);
        
        const parentTypeData = {
          code: `PARENT_${this.testHelper.generateRandomString(4)}`,
          name: `父级类型_${this.testHelper.generateRandomString(4)}`,
          description: '父级模板类型',
          level: '1'
        };
        
        await this.testHelper.page.fill(selectors.templateType.codeInput, parentTypeData.code);
        await this.testHelper.page.fill(selectors.templateType.nameInput, parentTypeData.name);
        await this.testHelper.page.fill(selectors.templateType.descriptionInput, parentTypeData.description);
        
        await this.screenshotHelper.takeCustomScreenshot('创建父级类型');
        
        // 保存父级类型
        await this.testHelper.page.click(selectors.templateType.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        
        // 创建子级类型
        await this.testHelper.page.click(selectors.security.addButton);
        await this.testHelper.wait(testData.timeouts.short);
        
        const childTypeData = {
          code: `CHILD_${this.testHelper.generateRandomString(4)}`,
          name: `子级类型_${this.testHelper.generateRandomString(4)}`,
          description: '子级模板类型',
          parentCode: parentTypeData.code
        };
        
        await this.testHelper.page.fill(selectors.templateType.codeInput, childTypeData.code);
        await this.testHelper.page.fill(selectors.templateType.nameInput, childTypeData.name);
        await this.testHelper.page.fill(selectors.templateType.descriptionInput, childTypeData.description);
        
        // 选择父级类型
        try {
          await this.testHelper.page.selectOption(selectors.templateType.parentSelect, childTypeData.parentCode);
        } catch (parentError) {
          // 如果没有父级选择器，跳过
        }
        
        await this.screenshotHelper.takeCustomScreenshot('创建子级类型');
        
        // 保存子级类型
        await this.testHelper.page.click(selectors.templateType.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '模板类型层级管理测试',
          testContent: '创建父子级模板类型层级结构',
          testPurpose: '验证模板类型层级管理功能能够正常工作',
          testInput: `父级类型: ${parentTypeData.name}, 子级类型: ${childTypeData.name}`,
          expectedOutput: '父子级模板类型创建成功，形成层级结构',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (hierarchyError) {
        // 如果没有层级管理功能，标记为跳过
        const result = {
          testId: testId,
          testName: '模板类型层级管理测试',
          testContent: '创建父子级模板类型层级结构',
          testPurpose: '验证模板类型层级管理功能能够正常工作',
          testInput: '查找模板类型层级管理功能',
          expectedOutput: '找到层级管理并成功创建',
          actualOutput: '未找到模板类型层级管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到模板类型层级管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '模板类型层级管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T154: 模板类型排序测试
   */
  async testT154_TemplateTypeSorting() {
    const testId = 'T154';
    console.log(`\n🧪 执行测试 ${testId}: 模板类型排序测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板类型管理页面
      await this.testHelper.navigateTo('/template/type');
      await this.testHelper.waitForPageLoad(selectors.templateType.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 测试排序功能
      try {
        // 获取排序前的类型列表
        const beforeSortData = await this.testHelper.getTableData(selectors.templateType.typeList);
        
        // 点击排序按钮（按名称排序）
        await this.testHelper.page.click(selectors.templateType.sortByNameButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('按名称排序');
        
        // 获取排序后的类型列表
        const afterSortData = await this.testHelper.getTableData(selectors.templateType.typeList);
        
        // 测试拖拽排序（如果支持）
        try {
          const firstItem = `${selectors.templateType.typeList} tr:first-child`;
          const secondItem = `${selectors.templateType.typeList} tr:nth-child(2)`;
          
          await this.testHelper.page.dragAndDrop(firstItem, secondItem);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('拖拽排序');
          
        } catch (dragError) {
          // 如果不支持拖拽排序，跳过
        }
        
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        const result = {
          testId: testId,
          testName: '模板类型排序测试',
          testContent: '测试模板类型的排序功能',
          testPurpose: '验证模板类型排序功能能够正常工作',
          testInput: '点击排序按钮，执行拖拽排序',
          expectedOutput: '模板类型按指定规则重新排序',
          actualOutput: `排序前记录数: ${beforeSortData.length}, 排序后记录数: ${afterSortData.length}`,
          result: afterSortData.length >= 0 ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (sortError) {
        // 如果没有排序功能，标记为跳过
        const result = {
          testId: testId,
          testName: '模板类型排序测试',
          testContent: '测试模板类型的排序功能',
          testPurpose: '验证模板类型排序功能能够正常工作',
          testInput: '查找模板类型排序功能',
          expectedOutput: '找到排序功能并成功排序',
          actualOutput: '未找到模板类型排序功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到模板类型排序功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '模板类型排序测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T155: 模板类型状态管理测试
   */
  async testT155_TemplateTypeStatusManagement() {
    const testId = 'T155';
    console.log(`\n🧪 执行测试 ${testId}: 模板类型状态管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板类型管理页面
      await this.testHelper.navigateTo('/template/type');
      await this.testHelper.waitForPageLoad(selectors.templateType.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 测试状态切换功能
      try {
        // 查找第一个启用/禁用按钮
        const enableButton = `${selectors.templateType.typeList} .enable-button:first-child`;
        const disableButton = `${selectors.templateType.typeList} .disable-button:first-child`;
        
        // 尝试禁用类型
        try {
          await this.testHelper.page.click(disableButton);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('禁用模板类型');
          
          // 确认禁用
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
          
        } catch (disableError) {
          // 如果没有禁用按钮，尝试启用
          await this.testHelper.page.click(enableButton);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('启用模板类型');
          
          // 确认启用
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
        }
        
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '模板类型状态管理测试',
          testContent: '测试模板类型的启用/禁用状态切换',
          testPurpose: '验证模板类型状态管理功能能够正常工作',
          testInput: '点击启用/禁用按钮切换状态',
          expectedOutput: '模板类型状态切换成功，显示成功提示',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (statusError) {
        // 如果没有状态管理功能，标记为跳过
        const result = {
          testId: testId,
          testName: '模板类型状态管理测试',
          testContent: '测试模板类型的启用/禁用状态切换',
          testPurpose: '验证模板类型状态管理功能能够正常工作',
          testInput: '查找模板类型状态管理功能',
          expectedOutput: '找到状态管理并成功切换',
          actualOutput: '未找到模板类型状态管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到模板类型状态管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '模板类型状态管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有模板类型管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行模板类型管理功能测试套件 (T151-T155)');
    
    const startTime = Date.now();
    
    await this.testT151_TemplateTypePageLoad();
    await this.testT152_AddTemplateType();
    await this.testT153_TemplateTypeHierarchy();
    await this.testT154_TemplateTypeSorting();
    await this.testT155_TemplateTypeStatusManagement();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 模板类型管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const templateTypeTest = new TemplateTypeTest();
  templateTypeTest.runAllTests().catch(console.error);
}

module.exports = TemplateTypeTest;
