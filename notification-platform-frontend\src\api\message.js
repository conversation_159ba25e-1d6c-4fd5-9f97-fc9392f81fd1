import request from '@/utils/request'

// ==================== 消息统计 ====================

/**
 * 获取消息统计信息
 * @param {Object} params 统计请求参数
 */
export function getMessageStatistics(params) {
  return request({
    url: '/api/message/statistics/overview',
    method: 'get',
    params
  })
}

/**
 * 获取Dashboard统计信息
 * @param {Object} params 统计请求参数
 */
export function getDashboardStatistics(params) {
  return request({
    url: '/api/message/statistics/overview',
    method: 'get',
    params
  })
}

/**
 * 分页查询消息记录
 * @param {Object} params 查询参数
 */
export function getMessageRecords(params) {
  return request({
    url: '/api/message/statistics/records',
    method: 'get',
    params
  })
}

/**
 * 根据消息ID查询记录详情
 * @param {String} messageId 消息ID
 */
export function getMessageRecordById(messageId) {
  return request({
    url: `/api/message/statistics/records/${messageId}`,
    method: 'get'
  })
}

/**
 * 获取发送失败的消息记录
 * @param {Object} params 查询参数
 */
export function getFailedMessages(params) {
  return request({
    url: '/api/message/statistics/failed-messages',
    method: 'get',
    params
  })
}

/**
 * 重发消息
 * @param {String} messageId 消息ID
 */
export function resendMessage(messageId) {
  return request({
    url: `/api/message/statistics/resend/${messageId}`,
    method: 'post'
  })
}

/**
 * 批量重发消息
 * @param {Array} messageIds 消息ID列表
 */
export function batchResendMessages(messageIds) {
  return request({
    url: '/api/message/statistics/batch-resend',
    method: 'post',
    data: messageIds
  })
}

/**
 * 更新消息状态
 * @param {String} messageId 消息ID
 * @param {Object} params 状态参数
 */
export function updateMessageStatus(messageId, params) {
  return request({
    url: `/api/message/statistics/status/${messageId}`,
    method: 'put',
    params
  })
}

/**
 * 批量刷新消息状态
 * @param {Object} params 刷新参数
 */
export function refreshMessageStatus(params) {
  return request({
    url: '/api/message/statistics/refresh-status',
    method: 'post',
    params
  })
}

// ==================== 单发短信 ====================

/**
 * 使用模板发送短信
 * @param {Object} data 发送参数
 */
export function sendSmsWithTemplate(data) {
  return request({
    url: '/api/message/sms/send-with-template',
    method: 'post',
    data
  })
}

/**
 * 直接发送短信
 * @param {Object} data 发送参数
 */
export function sendSmsWithContent(data) {
  return request({
    url: '/api/message/sms/send-with-content',
    method: 'post',
    data
  })
}

/**
 * 使用指定渠道发送短信
 * @param {Object} data 发送参数
 */
export function sendSmsWithChannel(data) {
  return request({
    url: '/api/message/sms/send-with-channel',
    method: 'post',
    data
  })
}

/**
 * 查询短信状态
 * @param {String} messageId 消息ID
 */
export function querySmsStatus(messageId) {
  return request({
    url: `/api/single-sms/status/${messageId}`,
    method: 'get'
  })
}

// ==================== 单发邮件 ====================

/**
 * 使用模板发送邮件
 * @param {Object} data 发送参数
 */
export function sendEmailWithTemplate(data) {
  return request({
    url: '/api/message/email/send-with-template',
    method: 'post',
    data
  })
}

/**
 * 直接发送邮件
 * @param {Object} data 发送参数
 */
export function sendEmailWithContent(data) {
  return request({
    url: '/api/message/email/send-with-content',
    method: 'post',
    data
  })
}

/**
 * 发送带附件的邮件
 * @param {Object} data 发送参数
 */
export function sendEmailWithAttachments(data) {
  return request({
    url: '/api/message/email/send-with-attachments',
    method: 'post',
    data
  })
}

/**
 * 使用指定渠道发送邮件
 * @param {Object} data 发送参数
 */
export function sendEmailWithChannel(data) {
  return request({
    url: '/api/message/email/send-with-channel',
    method: 'post',
    data
  })
}

/**
 * 查询邮件状态
 * @param {String} messageId 消息ID
 */
export function queryEmailStatus(messageId) {
  return request({
    url: `/api/single-email/status/${messageId}`,
    method: 'get'
  })
}

// ==================== 反欺诈通知 ====================

/**
 * 发送反欺诈失败通知
 * @param {Object} data 通知参数
 */
export function sendAntiFraudNotification(data) {
  return request({
    url: '/api/anti-fraud/send-failure-notification',
    method: 'post',
    data
  })
}

/**
 * 批量发送反欺诈失败通知
 * @param {Object} data 批量通知参数
 */
export function batchSendAntiFraudNotification(data) {
  return request({
    url: '/api/anti-fraud/batch-send-failure-notification',
    method: 'post',
    data
  })
}

/**
 * 查询反欺诈通知记录
 * @param {Object} params 查询参数
 */
export function getAntiFraudNotifications(params) {
  return request({
    url: '/api/anti-fraud/notifications',
    method: 'get',
    params
  })
}