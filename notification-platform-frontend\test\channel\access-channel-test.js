/**
 * T101-T110: 接入渠道管理功能测试
 * 基于需求文档中的渠道管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class AccessChannelTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T101: 接入渠道管理页面加载测试
   */
  async testT101_AccessChannelPageLoad() {
    const testId = 'T101';
    console.log(`\n🧪 执行测试 ${testId}: 接入渠道管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到渠道管理页面
      await this.testHelper.navigateTo('/channel/access');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isAccessChannelTabVisible = await this.testHelper.verifyElementVisibility(selectors.channel.accessChannelTab);
      const isTableVisible = await this.testHelper.verifyElementVisibility(selectors.common.table);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '接入渠道管理页面加载测试',
        testContent: '验证接入渠道管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的接入渠道管理界面',
        testInput: '访问接入渠道管理页面URL: /channel/access',
        expectedOutput: '页面正常加载，显示接入渠道标签页、渠道列表和管理按钮',
        actualOutput: `接入渠道标签: ${isAccessChannelTabVisible ? '✅显示' : '❌隐藏'}, 渠道列表: ${isTableVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isAccessChannelTabVisible && isTableVisible && isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '接入渠道管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T102: 新增接入渠道测试
   */
  async testT102_AddAccessChannel() {
    const testId = 'T102';
    console.log(`\n🧪 执行测试 ${testId}: 新增接入渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到接入渠道管理页面
      await this.testHelper.navigateTo('/channel/access');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增接入渠道对话框');
      
      // 生成唯一的渠道信息
      const uniqueChannelId = `CH_${this.testHelper.generateRandomString(6)}`;
      const channelData = {
        ...testData.channel.accessChannel,
        id: uniqueChannelId,
        name: `测试接入渠道_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写渠道信息
      const formSelectors = {
        id: selectors.channel.nameInput, // 假设使用name字段作为ID
        name: selectors.channel.nameInput
      };
      
      await this.testHelper.fillForm(channelData, formSelectors);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存渠道
      await this.testHelper.page.click(selectors.channel.saveButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增接入渠道测试',
        testContent: '创建一个新的接入渠道',
        testPurpose: '验证接入渠道新增功能能够正常工作',
        testInput: `渠道ID: ${uniqueChannelId}, 渠道名称: ${channelData.name}`,
        expectedOutput: '接入渠道创建成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增接入渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T103: 编辑接入渠道测试
   */
  async testT103_EditAccessChannel() {
    const testId = 'T103';
    console.log(`\n🧪 执行测试 ${testId}: 编辑接入渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到接入渠道管理页面
      await this.testHelper.navigateTo('/channel/access');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找第一个编辑按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.security.editButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeEditPageScreenshot();
        
        // 修改渠道名称
        const newChannelName = `修改后的接入渠道_${this.testHelper.generateRandomString(4)}`;
        await this.testHelper.page.fill(selectors.channel.nameInput, newChannelName);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存修改
        await this.testHelper.page.click(selectors.channel.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '编辑接入渠道测试',
          testContent: '编辑现有接入渠道的信息',
          testPurpose: '验证接入渠道编辑功能能够正常工作',
          testInput: `修改渠道名称为: ${newChannelName}`,
          expectedOutput: '接入渠道修改成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (editError) {
        // 如果没有可编辑的渠道，标记为跳过
        const result = {
          testId: testId,
          testName: '编辑接入渠道测试',
          testContent: '编辑现有接入渠道的信息',
          testPurpose: '验证接入渠道编辑功能能够正常工作',
          testInput: '查找可编辑的接入渠道',
          expectedOutput: '找到渠道并成功编辑',
          actualOutput: '未找到可编辑的接入渠道',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到可编辑的接入渠道`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '编辑接入渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T104: 启用/禁用接入渠道测试
   */
  async testT104_ToggleAccessChannelStatus() {
    const testId = 'T104';
    console.log(`\n🧪 执行测试 ${testId}: 启用/禁用接入渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到接入渠道管理页面
      await this.testHelper.navigateTo('/channel/access');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找启用/禁用按钮并点击
      try {
        // 先尝试禁用按钮
        let buttonFound = false;
        try {
          await this.testHelper.page.click(`${selectors.common.table} ${selectors.channel.disableButton}:first-child`);
          buttonFound = true;
          await this.screenshotHelper.takeCustomScreenshot('禁用渠道操作');
        } catch (disableError) {
          // 如果没有禁用按钮，尝试启用按钮
          try {
            await this.testHelper.page.click(`${selectors.common.table} ${selectors.channel.enableButton}:first-child`);
            buttonFound = true;
            await this.screenshotHelper.takeCustomScreenshot('启用渠道操作');
          } catch (enableError) {
            // 都没有找到
          }
        }
        
        if (buttonFound) {
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          // 获取成功消息
          const successMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '启用/禁用接入渠道测试',
            testContent: '切换接入渠道的启用/禁用状态',
            testPurpose: '验证接入渠道状态切换功能能够正常工作',
            testInput: '点击启用或禁用按钮',
            expectedOutput: '渠道状态切换成功，显示成功提示信息',
            actualOutput: `成功消息: ${successMessage || '无'}`,
            result: successMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } else {
          throw new Error('未找到启用/禁用按钮');
        }
        
      } catch (toggleError) {
        // 如果没有状态切换按钮，标记为跳过
        const result = {
          testId: testId,
          testName: '启用/禁用接入渠道测试',
          testContent: '切换接入渠道的启用/禁用状态',
          testPurpose: '验证接入渠道状态切换功能能够正常工作',
          testInput: '查找启用/禁用功能',
          expectedOutput: '找到状态切换按钮并成功操作',
          actualOutput: '未找到状态切换功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到状态切换功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '启用/禁用接入渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T105: 删除接入渠道测试
   */
  async testT105_DeleteAccessChannel() {
    const testId = 'T105';
    console.log(`\n🧪 执行测试 ${testId}: 删除接入渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到接入渠道管理页面
      await this.testHelper.navigateTo('/channel/access');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取删除前的记录数
      const beforeDeleteData = await this.testHelper.getTableData(selectors.common.table);
      
      // 查找第一个删除按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.security.deleteButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeDeleteConfirmScreenshot();
        
        // 确认删除
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        // 获取删除后的记录数
        const afterDeleteData = await this.testHelper.getTableData(selectors.common.table);
        
        const result = {
          testId: testId,
          testName: '删除接入渠道测试',
          testContent: '删除现有的接入渠道',
          testPurpose: '验证接入渠道删除功能能够正常工作',
          testInput: '选择渠道并确认删除',
          expectedOutput: '接入渠道删除成功，显示成功提示，记录数减少',
          actualOutput: `成功消息: ${successMessage || '无'}, 记录数变化: ${beforeDeleteData.length} → ${afterDeleteData.length}`,
          result: successMessage && afterDeleteData.length < beforeDeleteData.length ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (deleteError) {
        // 如果没有可删除的渠道，标记为跳过
        const result = {
          testId: testId,
          testName: '删除接入渠道测试',
          testContent: '删除现有的接入渠道',
          testPurpose: '验证接入渠道删除功能能够正常工作',
          testInput: '查找可删除的接入渠道',
          expectedOutput: '找到渠道并成功删除',
          actualOutput: '未找到可删除的接入渠道',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到可删除的接入渠道`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '删除接入渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有接入渠道管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行接入渠道管理功能测试套件 (T101-T105)');
    
    const startTime = Date.now();
    
    await this.testT101_AccessChannelPageLoad();
    await this.testT102_AddAccessChannel();
    await this.testT103_EditAccessChannel();
    await this.testT104_ToggleAccessChannelStatus();
    await this.testT105_DeleteAccessChannel();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 接入渠道管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const accessChannelTest = new AccessChannelTest();
  accessChannelTest.runAllTests().catch(console.error);
}

module.exports = AccessChannelTest;
