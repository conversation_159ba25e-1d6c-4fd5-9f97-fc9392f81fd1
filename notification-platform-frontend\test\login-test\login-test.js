const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // 访问登录页面
    await page.goto('http://localhost:3000/login');
    
    // 等待页面加载
    await page.waitForSelector('.login-container');
    
    // 截图：初始页面
    await page.screenshot({ path: 'login-initial.png', fullPage: true });
    
    // 填写用户名
    await page.fill('input[placeholder="用户名"]', 'testuser');
    await page.screenshot({ path: 'login-username-filled.png' });
    
    // 填写密码
    await page.fill('input[placeholder="密码"]', 'testpass');
    await page.screenshot({ path: 'login-form-filled.png' });
    
    // 点击登录按钮
    await page.click('button:has-text("登录")');
    
    // 等待响应
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'login-after-submit.png' });
    
    console.log('测试完成，截图已保存');
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    await browser.close();
  }
})();