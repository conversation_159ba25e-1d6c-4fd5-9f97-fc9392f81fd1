<template>
  <div class="permission-management">
    <div class="page-header">
      <h2>权限管理</h2>
      <div class="actions">
        <el-button 
          type="primary" 
          @click="handleRefresh"
          icon="Refresh"
        >
          刷新权限
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="权限名称">
          <el-input v-model="searchForm.permissionName" placeholder="请输入权限名称" />
        </el-form-item>
        <el-form-item label="权限编码">
          <el-input v-model="searchForm.permissionCode" placeholder="请输入权限编码" />
        </el-form-item>
        <el-form-item label="资源类型">
          <el-select v-model="searchForm.resourceType" placeholder="请选择资源类型">
            <el-option label="菜单" value="1" />
            <el-option label="按钮" value="2" />
            <el-option label="接口" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table 
        :data="tableData" 
        v-loading="loading" 
        row-key="id" 
        :tree-props="{ children: 'children' }"
        :expand-row-keys="expandedKeys"
        @expand-change="handleExpandChange"
      >
        <el-table-column prop="permissionName" label="权限名称" min-width="200" />
        <el-table-column prop="permissionCode" label="权限编码" min-width="180" />
        <el-table-column prop="resourceType" label="资源类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getResourceTypeColor(row.resourceType)">
              {{ getResourceTypeName(row.resourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路由路径" min-width="150">
          <template #default="{ row }">
            <code v-if="row.path">{{ row.path }}</code>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="component" label="组件路径" min-width="180">
          <template #default="{ row }">
            <code v-if="row.component">{{ row.component }}</code>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" width="80">
          <template #default="{ row }">
            <i v-if="row.icon" :class="`icon-${row.icon}`"></i>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ row.createdTime ? row.createdTime.substring(0, 19) : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getPermissionTree } from '@/api/permission';

// 数据定义
const loading = ref(false);
const tableData = ref([]);
const expandedKeys = ref([]);

// 搜索表单
const searchForm = reactive({
  permissionName: '',
  permissionCode: '',
  resourceType: ''
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getPermissionTree();
    tableData.value = response.data;
    // 默认展开第一级
    expandedKeys.value = response.data.map(item => item.id);
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getResourceTypeName = (type) => {
  const types = {
    1: '菜单',
    2: '按钮',
    3: '接口'
  };
  return types[type] || '未知';
};

const getResourceTypeColor = (type) => {
  const colors = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  };
  return colors[type] || '';
};

// 搜索功能
const filterData = (data, searchForm) => {
  if (!searchForm.permissionName && !searchForm.permissionCode && !searchForm.resourceType) {
    return data;
  }
  
  const filtered = [];
  
  const filterNode = (node) => {
    const matchName = !searchForm.permissionName || node.permissionName.includes(searchForm.permissionName);
    const matchCode = !searchForm.permissionCode || node.permissionCode.includes(searchForm.permissionCode);
    const matchType = !searchForm.resourceType || node.resourceType.toString() === searchForm.resourceType;
    
    const match = matchName && matchCode && matchType;
    
    if (node.children) {
      const filteredChildren = node.children.filter(filterNode);
      if (match || filteredChildren.length > 0) {
        return {
          ...node,
          children: filteredChildren
        };
      }
    }
    
    return match ? node : null;
  };
  
  return data.filter(filterNode).filter(Boolean);
};

const handleSearch = () => {
  const filtered = filterData(tableData.value, searchForm);
  // 这里可以更新显示的数据，但为了简化，我们重新加载
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    permissionName: '',
    permissionCode: '',
    resourceType: ''
  });
  handleSearch();
};

const handleRefresh = () => {
  ElMessage.success('权限数据已刷新');
  loadData();
};

const handleExpandChange = (row, expandedRows) => {
  expandedKeys.value = expandedRows.map(item => item.id);
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.permission-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.text-muted {
  color: #999;
}

code {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
}
</style>