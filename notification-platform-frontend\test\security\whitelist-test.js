/**
 * T201-T210: 白名单管理功能测试
 * 基于需求文档中的安全管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class WhitelistTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T201: 白名单管理页面加载测试
   */
  async testT201_WhitelistPageLoad() {
    const testId = 'T201';
    console.log(`\n🧪 执行测试 ${testId}: 白名单管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isTableVisible = await this.testHelper.verifyElementVisibility(selectors.common.table);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      const isPhoneInputVisible = await this.testHelper.verifyElementVisibility(selectors.security.phoneInput);
      const isEmailInputVisible = await this.testHelper.verifyElementVisibility(selectors.security.emailInput);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '白名单管理页面加载测试',
        testContent: '验证白名单管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的白名单管理界面',
        testInput: '访问白名单管理页面URL: /security/whitelist',
        expectedOutput: '页面正常加载，显示白名单列表、搜索框和管理按钮',
        actualOutput: `白名单列表: ${isTableVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}, 手机号搜索: ${isPhoneInputVisible ? '✅显示' : '❌隐藏'}, 邮箱搜索: ${isEmailInputVisible ? '✅显示' : '❌隐藏'}`,
        result: isTableVisible && isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '白名单管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T202: 新增手机号白名单测试
   */
  async testT202_AddPhoneWhitelist() {
    const testId = 'T202';
    console.log(`\n🧪 执行测试 ${testId}: 新增手机号白名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增白名单对话框');
      
      // 生成唯一的手机号
      const uniquePhone = this.testHelper.generateRandomPhone();
      const whitelistData = {
        phone: uniquePhone,
        reason: `测试白名单_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写白名单信息
      await this.testHelper.page.fill(selectors.security.phoneInput, whitelistData.phone);
      await this.testHelper.page.fill(selectors.security.reasonInput, whitelistData.reason);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存白名单
      await this.testHelper.page.click(selectors.common.confirmButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增手机号白名单测试',
        testContent: '添加一个手机号到白名单',
        testPurpose: '验证手机号白名单新增功能能够正常工作',
        testInput: `手机号: ${whitelistData.phone}, 原因: ${whitelistData.reason}`,
        expectedOutput: '白名单添加成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增手机号白名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T203: 新增邮箱白名单测试
   */
  async testT203_AddEmailWhitelist() {
    const testId = 'T203';
    console.log(`\n🧪 执行测试 ${testId}: 新增邮箱白名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增邮箱白名单对话框');
      
      // 生成唯一的邮箱
      const uniqueEmail = this.testHelper.generateRandomEmail();
      const whitelistData = {
        email: uniqueEmail,
        reason: `测试邮箱白名单_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写白名单信息
      await this.testHelper.page.fill(selectors.security.emailInput, whitelistData.email);
      await this.testHelper.page.fill(selectors.security.reasonInput, whitelistData.reason);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存白名单
      await this.testHelper.page.click(selectors.common.confirmButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增邮箱白名单测试',
        testContent: '添加一个邮箱到白名单',
        testPurpose: '验证邮箱白名单新增功能能够正常工作',
        testInput: `邮箱: ${whitelistData.email}, 原因: ${whitelistData.reason}`,
        expectedOutput: '邮箱白名单添加成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增邮箱白名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T204: 批量导入白名单测试
   */
  async testT204_BatchImportWhitelist() {
    const testId = 'T204';
    console.log(`\n🧪 执行测试 ${testId}: 批量导入白名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找批量导入按钮
      try {
        await this.testHelper.page.click(selectors.security.batchImportButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('批量导入对话框');
        
        // 填写批量导入数据
        const batchData = [
          '13800000001,测试手机号1',
          '13800000002,测试手机号2',
          '<EMAIL>,测试邮箱1',
          '<EMAIL>,测试邮箱2'
        ].join('\n');
        
        await this.testHelper.page.fill(selectors.security.batchDataTextarea, batchData);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 提交批量导入
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '批量导入白名单测试',
          testContent: '批量导入多个白名单记录',
          testPurpose: '验证白名单批量导入功能能够正常工作',
          testInput: `批量导入4条记录: 2个手机号, 2个邮箱`,
          expectedOutput: '批量导入成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (importError) {
        // 如果没有批量导入功能，标记为跳过
        const result = {
          testId: testId,
          testName: '批量导入白名单测试',
          testContent: '批量导入多个白名单记录',
          testPurpose: '验证白名单批量导入功能能够正常工作',
          testInput: '查找批量导入功能',
          expectedOutput: '找到批量导入按钮并成功导入',
          actualOutput: '未找到批量导入功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到批量导入功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '批量导入白名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T205: 白名单搜索功能测试
   */
  async testT205_WhitelistSearch() {
    const testId = 'T205';
    console.log(`\n🧪 执行测试 ${testId}: 白名单搜索功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取搜索前的白名单列表
      const beforeSearchData = await this.testHelper.getTableData(selectors.common.table);
      
      // 执行搜索
      const searchParams = {
        phone: '138'
      };
      
      const searchSelectors = {
        phone: selectors.security.phoneInput
      };
      
      await this.testHelper.search(searchParams, searchSelectors);
      await this.screenshotHelper.takeSearchResultScreenshot();
      
      // 获取搜索后的白名单列表
      const afterSearchData = await this.testHelper.getTableData(selectors.common.table);
      
      const result = {
        testId: testId,
        testName: '白名单搜索功能测试',
        testContent: '使用手机号进行白名单搜索',
        testPurpose: '验证白名单搜索功能能够正确过滤数据',
        testInput: `搜索手机号: ${searchParams.phone}`,
        expectedOutput: '搜索结果显示匹配的白名单数据',
        actualOutput: `搜索前记录数: ${beforeSearchData.length}, 搜索后记录数: ${afterSearchData.length}`,
        result: afterSearchData.length >= 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '白名单搜索功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T206: 白名单有效期设置测试
   */
  async testT206_WhitelistExpiration() {
    const testId = 'T206';
    console.log(`\n🧪 执行测试 ${testId}: 白名单有效期设置测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到白名单管理页面
      await this.testHelper.navigateTo('/security/whitelist');
      await this.testHelper.waitForPageLoad(selectors.security.whitelistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增带有效期的白名单');
      
      // 生成唯一的手机号
      const uniquePhone = this.testHelper.generateRandomPhone();
      
      // 填写白名单信息
      await this.testHelper.page.fill(selectors.security.phoneInput, uniquePhone);
      await this.testHelper.page.fill(selectors.security.reasonInput, '临时白名单测试');
      
      // 设置有效期（如果有此功能）
      try {
        const expirationDate = new Date();
        expirationDate.setDate(expirationDate.getDate() + 30); // 30天后过期
        const expirationStr = expirationDate.toISOString().split('T')[0];
        
        await this.testHelper.page.fill('.expiration-date-input', expirationStr);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存白名单
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '白名单有效期设置测试',
          testContent: '创建带有有效期的白名单记录',
          testPurpose: '验证白名单有效期设置功能能够正常工作',
          testInput: `手机号: ${uniquePhone}, 有效期: ${expirationStr}`,
          expectedOutput: '带有效期的白名单创建成功',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (expirationError) {
        // 如果没有有效期设置功能，标记为跳过
        const result = {
          testId: testId,
          testName: '白名单有效期设置测试',
          testContent: '创建带有有效期的白名单记录',
          testPurpose: '验证白名单有效期设置功能能够正常工作',
          testInput: '查找有效期设置功能',
          expectedOutput: '找到有效期设置并成功创建',
          actualOutput: '未找到有效期设置功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到有效期设置功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '白名单有效期设置测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有白名单管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行白名单管理功能测试套件 (T201-T206)');
    
    const startTime = Date.now();
    
    await this.testT201_WhitelistPageLoad();
    await this.testT202_AddPhoneWhitelist();
    await this.testT203_AddEmailWhitelist();
    await this.testT204_BatchImportWhitelist();
    await this.testT205_WhitelistSearch();
    await this.testT206_WhitelistExpiration();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 白名单管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const whitelistTest = new WhitelistTest();
  whitelistTest.runAllTests().catch(console.error);
}

module.exports = WhitelistTest;
