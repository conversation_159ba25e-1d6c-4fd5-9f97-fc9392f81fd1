/**
 * T051-T060: 短信批量发送功能测试
 * 基于需求文档中的批量短信发送功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class SmsBatchTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T051: 短信批量发送页面加载测试
   */
  async testT051_SmsBatchPageLoad() {
    const testId = 'T051';
    console.log(`\n🧪 执行测试 ${testId}: 短信批量发送页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到短信批量发送页面
      await this.testHelper.navigateTo('/message/sms/batch');
      await this.testHelper.waitForPageLoad(selectors.smsBatch.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isFileUploadVisible = await this.testHelper.verifyElementVisibility(selectors.smsBatch.fileUpload);
      const isPhoneListVisible = await this.testHelper.verifyElementVisibility(selectors.smsBatch.phoneListTextarea);
      const isContentTextareaVisible = await this.testHelper.verifyElementVisibility(selectors.smsBatch.contentTextarea);
      const isSendButtonVisible = await this.testHelper.verifyElementVisibility(selectors.smsBatch.sendButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '短信批量发送页面加载测试',
        testContent: '验证短信批量发送页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的批量短信发送界面',
        testInput: '访问短信批量发送页面URL: /message/sms/batch',
        expectedOutput: '页面正常加载，显示文件上传、手机号列表、短信内容和发送按钮',
        actualOutput: `文件上传: ${isFileUploadVisible ? '✅显示' : '❌隐藏'}, 手机号列表: ${isPhoneListVisible ? '✅显示' : '❌隐藏'}, 内容输入框: ${isContentTextareaVisible ? '✅显示' : '❌隐藏'}, 发送按钮: ${isSendButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isFileUploadVisible && isPhoneListVisible && isContentTextareaVisible && isSendButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '短信批量发送页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T052: 手动输入手机号批量发送测试
   */
  async testT052_ManualPhoneListSend() {
    const testId = 'T052';
    console.log(`\n🧪 执行测试 ${testId}: 手动输入手机号批量发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信批量发送页面
      await this.testHelper.navigateTo('/message/sms/batch');
      await this.testHelper.waitForPageLoad(selectors.smsBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 手动输入手机号列表
      const phoneList = testData.sms.batchPhones.join('\n');
      await this.testHelper.page.fill(selectors.smsBatch.phoneListTextarea, phoneList);
      await this.testHelper.page.fill(selectors.smsBatch.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送批量短信
      await this.testHelper.page.click(selectors.smsBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '手动输入手机号批量发送测试',
        testContent: '通过手动输入手机号列表进行批量短信发送',
        testPurpose: '验证手动输入方式的批量短信发送功能',
        testInput: `手机号列表: ${testData.sms.batchPhones.join(', ')}, 内容: ${testData.sms.content}`,
        expectedOutput: '批量发送任务提交成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '手动输入手机号批量发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T053: 预览功能测试
   */
  async testT053_PreviewFunction() {
    const testId = 'T053';
    console.log(`\n🧪 执行测试 ${testId}: 预览功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信批量发送页面
      await this.testHelper.navigateTo('/message/sms/batch');
      await this.testHelper.waitForPageLoad(selectors.smsBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写批量发送信息
      const phoneList = testData.sms.batchPhones.join('\n');
      await this.testHelper.page.fill(selectors.smsBatch.phoneListTextarea, phoneList);
      await this.testHelper.page.fill(selectors.smsBatch.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 点击预览按钮
      try {
        await this.testHelper.page.click(selectors.smsBatch.previewButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('预览对话框');
        
        // 验证预览对话框是否显示
        const isPreviewVisible = await this.testHelper.verifyElementVisibility(selectors.common.modal);
        
        const result = {
          testId: testId,
          testName: '预览功能测试',
          testContent: '测试批量短信发送的预览功能',
          testPurpose: '验证用户能够预览批量发送的内容和目标',
          testInput: `手机号数量: ${testData.sms.batchPhones.length}, 内容: ${testData.sms.content}`,
          expectedOutput: '显示预览对话框，展示发送详情',
          actualOutput: `预览对话框: ${isPreviewVisible ? '✅显示' : '❌隐藏'}`,
          result: isPreviewVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (previewError) {
        // 如果没有预览按钮，标记为跳过
        const result = {
          testId: testId,
          testName: '预览功能测试',
          testContent: '测试批量短信发送的预览功能',
          testPurpose: '验证用户能够预览批量发送的内容和目标',
          testInput: '查找预览功能',
          expectedOutput: '找到预览按钮并显示预览内容',
          actualOutput: '未找到预览功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到预览功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '预览功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T054: 无效手机号批量发送测试
   */
  async testT054_InvalidPhoneBatch() {
    const testId = 'T054';
    console.log(`\n🧪 执行测试 ${testId}: 无效手机号批量发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信批量发送页面
      await this.testHelper.navigateTo('/message/sms/batch');
      await this.testHelper.waitForPageLoad(selectors.smsBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 输入包含无效手机号的列表
      const invalidPhoneList = [
        testData.sms.validPhone,
        testData.sms.invalidPhone,
        '12345',
        'abcdefg'
      ].join('\n');
      
      await this.testHelper.page.fill(selectors.smsBatch.phoneListTextarea, invalidPhoneList);
      await this.testHelper.page.fill(selectors.smsBatch.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送批量短信
      await this.testHelper.page.click(selectors.smsBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '无效手机号批量发送测试',
        testContent: '使用包含无效手机号的列表进行批量发送',
        testPurpose: '验证系统对无效手机号的批量验证机制',
        testInput: `手机号列表包含无效号码: ${invalidPhoneList.replace(/\n/g, ', ')}`,
        expectedOutput: '发送失败或过滤无效号码，显示相应提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '无效手机号批量发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T055: 空手机号列表测试
   */
  async testT055_EmptyPhoneList() {
    const testId = 'T055';
    console.log(`\n🧪 执行测试 ${testId}: 空手机号列表测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信批量发送页面
      await this.testHelper.navigateTo('/message/sms/batch');
      await this.testHelper.waitForPageLoad(selectors.smsBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 只填写内容，不填手机号列表
      await this.testHelper.page.fill(selectors.smsBatch.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送批量短信
      await this.testHelper.page.click(selectors.smsBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空手机号列表测试',
        testContent: '在手机号列表为空的情况下尝试批量发送',
        testPurpose: '验证系统对空手机号列表的验证机制',
        testInput: `手机号列表: (空), 内容: ${testData.sms.content}`,
        expectedOutput: '发送失败，显示手机号列表不能为空的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空手机号列表测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有短信批量发送测试
   */
  async runAllTests() {
    console.log('🚀 开始执行短信批量发送功能测试套件 (T051-T055)');
    
    const startTime = Date.now();
    
    await this.testT051_SmsBatchPageLoad();
    await this.testT052_ManualPhoneListSend();
    await this.testT053_PreviewFunction();
    await this.testT054_InvalidPhoneBatch();
    await this.testT055_EmptyPhoneList();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 短信批量发送功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const smsBatchTest = new SmsBatchTest();
  smsBatchTest.runAllTests().catch(console.error);
}

module.exports = SmsBatchTest;
