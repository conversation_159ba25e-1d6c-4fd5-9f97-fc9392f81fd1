import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import JSEncrypt from 'jsencrypt';
import App from './App.vue';
import router from './router';
import './assets/styles/index.scss';
import permissionDirectives from '@/directives/permission';

// 将JSEncrypt挂载到全局
window.JSEncrypt = JSEncrypt;

const app = createApp(App);

app.use(router);
app.use(ElementPlus, { size: 'default' });

// 注册权限指令
Object.keys(permissionDirectives).forEach(key => {
  app.directive(key, permissionDirectives[key]);
});

app.mount('#app');
