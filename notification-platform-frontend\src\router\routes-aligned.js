// 与后端权限完全对齐的路由配置
export const alignedRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', isPublic: true }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  },
  // 系统管理模块 - 对应 system:manage
  {
    path: '/system',
    component: () => import('@/layout/index.vue'),
    redirect: '/system/user',
    meta: { 
      title: '系统管理', 
      icon: 'system',
      permission: 'system:manage'
    },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/user/index.vue'),
        meta: { 
          title: '用户管理',
          icon: 'user',
          permission: 'system:user'
        }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/role/index.vue'),
        meta: { 
          title: '角色管理',
          icon: 'role',
          permission: 'system:role'
        }
      },
      {
        path: 'organization',
        name: 'SystemOrganization',
        component: () => import('@/views/system/organization/index.vue'),
        meta: { 
          title: '机构管理',
          icon: 'organization',
          permission: 'system:organization'
        }
      },
      {
        path: 'permission',
        name: 'SystemPermission',
        component: () => import('@/views/system/permission/index.vue'),
        meta: { 
          title: '权限管理',
          icon: 'permission',
          permission: 'system:permission'
        }
      }
    ]
  },
  // 消息发送模块 - 对应 message:manage
  {
    path: '/message',
    component: () => import('@/layout/index.vue'),
    redirect: '/message/sms',
    meta: { 
      title: '消息发送', 
      icon: 'message',
      permission: 'message:manage'
    },
    children: [
      {
        path: 'sms',
        component: () => import('@/layout/index.vue'),
        redirect: '/message/sms/single',
        meta: { 
          title: '短信发送',
          icon: 'sms',
          permission: 'message:sms'
        },
        children: [
          {
            path: 'single',
            name: 'MessageSmsSingle',
            component: () => import('@/views/message/sms/single.vue'),
            meta: { 
              title: '短信单发',
              icon: 'single',
              permission: 'message:sms:single'
            }
          },
          {
            path: 'batch',
            name: 'MessageSmsBatch',
            component: () => import('@/views/message/sms/batch.vue'),
            meta: { 
              title: '短信群发',
              icon: 'batch',
              permission: 'message:sms:batch'
            }
          }
        ]
      },
      {
        path: 'email',
        component: () => import('@/layout/index.vue'),
        redirect: '/message/email/single',
        meta: { 
          title: '邮件发送',
          icon: 'email',
          permission: 'message:email'
        },
        children: [
          {
            path: 'single',
            name: 'MessageEmailSingle',
            component: () => import('@/views/message/email/single.vue'),
            meta: { 
              title: '邮件单发',
              icon: 'single',
              permission: 'message:email:single'
            }
          },
          {
            path: 'batch',
            name: 'MessageEmailBatch',
            component: () => import('@/views/message/email/batch.vue'),
            meta: { 
              title: '邮件群发',
              icon: 'batch',
              permission: 'message:email:batch'
            }
          },
          {
            path: 'marketing',
            name: 'MessageEmailMarketing',
            component: () => import('@/views/message/email/marketing.vue'),
            meta: { 
              title: '营销邮件',
              icon: 'marketing',
              permission: 'message:email:marketing'
            }
          },
          {
            path: 'dormant',
            name: 'MessageEmailDormant',
            component: () => import('@/views/message/email/dormant.vue'),
            meta: { 
              title: '休眠邮件',
              icon: 'dormant',
              permission: 'message:email:dormant'
            }
          }
        ]
      }
    ]
  },
  // 模板管理模块 - 对应 template:manage
  {
    path: '/template',
    component: () => import('@/layout/index.vue'),
    redirect: '/template/list',
    meta: { 
      title: '模板管理', 
      icon: 'template',
      permission: 'template:manage'
    },
    children: [
      {
        path: 'list',
        name: 'TemplateList',
        component: () => import('@/views/template/list/index.vue'),
        meta: { 
          title: '模板列表',
          icon: 'list',
          permission: 'template:list'
        }
      },
      {
        path: 'type',
        name: 'TemplateType',
        component: () => import('@/views/template/type/index.vue'),
        meta: { 
          title: '模板类型',
          icon: 'type',
          permission: 'template:type'
        }
      },
      {
        path: 'parameter',
        name: 'TemplateParameter',
        component: () => import('@/views/template/parameter/index.vue'),
        meta: { 
          title: '参数管理',
          icon: 'parameter',
          permission: 'template:parameter'
        }
      }
    ]
  },
  // 渠道管理模块 - 对应 channel:manage
  {
    path: '/channel',
    component: () => import('@/layout/index.vue'),
    redirect: '/channel/access',
    meta: { 
      title: '渠道管理', 
      icon: 'channel',
      permission: 'channel:manage'
    },
    children: [
      {
        path: 'access',
        name: 'ChannelAccess',
        component: () => import('@/views/channel/access/index.vue'),
        meta: { 
          title: '接入渠道',
          icon: 'access',
          permission: 'channel:access'
        }
      },
      {
        path: 'send',
        name: 'ChannelSend',
        component: () => import('@/views/channel/send/index.vue'),
        meta: { 
          title: '发送渠道',
          icon: 'send',
          permission: 'channel:send'
        }
      }
    ]
  },
  // 统计监控模块 - 对应 statistics:view
  {
    path: '/statistics',
    component: () => import('@/layout/index.vue'),
    redirect: '/statistics/template',
    meta: { 
      title: '统计监控', 
      icon: 'statistics',
      permission: 'statistics:view'
    },
    children: [
      {
        path: 'template',
        name: 'StatisticsTemplate',
        component: () => import('@/views/statistics/template/index.vue'),
        meta: { 
          title: '模板统计',
          icon: 'template',
          permission: 'statistics:template'
        }
      },
      {
        path: 'sms',
        name: 'StatisticsSms',
        component: () => import('@/views/statistics/sms/index.vue'),
        meta: { 
          title: '短信统计',
          icon: 'sms',
          permission: 'statistics:sms'
        }
      },
      {
        path: 'email',
        name: 'StatisticsEmail',
        component: () => import('@/views/statistics/email/index.vue'),
        meta: { 
          title: '邮件统计',
          icon: 'email',
          permission: 'statistics:email'
        }
      }
    ]
  },
  // 安全管理模块 - 对应 security:manage
  {
    path: '/security',
    component: () => import('@/layout/index.vue'),
    redirect: '/security/blacklist',
    meta: { 
      title: '安全管理', 
      icon: 'security',
      permission: 'security:manage'
    },
    children: [
      {
        path: 'blacklist',
        name: 'SecurityBlacklist',
        component: () => import('@/views/security/blacklist/index.vue'),
        meta: { 
          title: '黑名单管理',
          icon: 'blacklist',
          permission: 'security:blacklist'
        }
      },
      {
        path: 'whitelist',
        name: 'SecurityWhitelist',
        component: () => import('@/views/security/whitelist/index.vue'),
        meta: { 
          title: '白名单管理',
          icon: 'whitelist',
          permission: 'security:whitelist'
        }
      },
      {
        path: 'keyword',
        name: 'SecurityKeyword',
        component: () => import('@/views/security/keyword/index.vue'),
        meta: { 
          title: '关键字管理',
          icon: 'keyword',
          permission: 'security:keyword'
        }
      }
    ]
  },
  // 日志查看模块 - 对应 log:view
  {
    path: '/log',
    component: () => import('@/layout/index.vue'),
    redirect: '/log/operation',
    meta: { 
      title: '日志查看', 
      icon: 'log',
      permission: 'log:view'
    },
    children: [
      {
        path: 'operation',
        name: 'LogOperation',
        component: () => import('@/views/log/operation/index.vue'),
        meta: { 
          title: '操作日志',
          icon: 'operation',
          permission: 'log:operation'
        }
      },
      {
        path: 'login',
        name: 'LogLogin',
        component: () => import('@/views/log/login/index.vue'),
        meta: { 
          title: '登录日志',
          icon: 'login',
          permission: 'log:login'
        }
      }
    ]
  },
  // ESB接口模块 - 对应 esb:access (这个模块主要是API权限，不需要页面路由)
  // 但如果需要管理界面，可以添加
  {
    path: '/esb',
    component: () => import('@/layout/index.vue'),
    redirect: '/esb/interface',
    meta: { 
      title: 'ESB接口', 
      icon: 'api',
      permission: 'esb:access'
    },
    children: [
      {
        path: 'interface',
        name: 'EsbInterface',
        component: () => import('@/views/esb/interface.vue'),
        meta: { 
          title: 'ESB接口管理',
          icon: 'interface',
          permission: 'esb:access'
        }
      }
    ]
  },
  // 403页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '无权限访问', isPublic: true }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { isPublic: true }
  }
];