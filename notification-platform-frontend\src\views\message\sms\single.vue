<template>
  <div class="single-sms-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>单条短信发送</span>
        </div>
      </template>
      
      <el-form
        ref="smsFormRef"
        :model="smsForm"
        :rules="smsRules"
        label-width="120px"
        class="sms-form"
      >
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input
            v-model="smsForm.phoneNumber"
            placeholder="请输入手机号码"
            clearable
          />
        </el-form-item>

        <el-form-item label="短信内容" prop="content">
          <el-input
            v-model="smsForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入短信内容"
            show-word-limit
            maxlength="500"
          />
        </el-form-item>

        <el-form-item label="发送渠道" prop="channelCode">
          <el-select
            v-model="smsForm.channelCode"
            placeholder="请选择发送渠道（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.channelCode"
              :label="channel.channelName"
              :value="channel.channelCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendSms"
          >
            {{ sending ? '发送中...' : '发送短信' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="previewSms">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="短信预览"
      width="50%"
    >
      <div class="sms-preview">
        <div class="preview-item">
          <strong>手机号码：</strong>{{ smsForm.phoneNumber }}
        </div>
        <div class="preview-item">
          <strong>短信内容：</strong>
          <div class="content-preview">{{ smsForm.content }}</div>
        </div>
        <div class="preview-item">
          <strong>字数统计：</strong>{{ smsForm.content.length }}/500
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { sendSingleSms, sendSmsWithChannel } from '@/api/sms'
import { getSendChannelsByType } from '@/api/channel'

// 表单数据
const smsForm = reactive({
  phoneNumber: '',
  content: '',
  channelCode: ''
})

// 表单验证规则
const smsRules = {
  phoneNumber: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入短信内容', trigger: 'blur' },
    { max: 500, message: '短信内容不能超过500个字符', trigger: 'blur' }
  ]
}

// 响应式数据
const smsFormRef = ref()
const sending = ref(false)
const previewVisible = ref(false)
const channelList = ref([])

// 获取发送渠道列表
const getChannelList = async () => {
  try {
    const response = await getSendChannelsByType(1) // 1-短信
    if (response.code === 200) {
      channelList.value = response.data || []
      // 默认选择第一个渠道
      if (channelList.value.length > 0) {
        smsForm.channelCode = channelList.value[0].channelCode
      }
    }
  } catch (error) {
    console.error('获取发送渠道失败:', error)
  }
}

// 发送短信
const sendSms = async () => {
  try {
    // 表单验证
    await smsFormRef.value.validate()

    sending.value = true

    const smsData = {
      phoneNumber: smsForm.phoneNumber,
      content: smsForm.content
    }

    let response
    // 如果指定了渠道，使用指定渠道发送
    if (smsForm.channelCode) {
      const channelSmsData = {
        channelCode: smsForm.channelCode,
        phoneNumber: smsForm.phoneNumber,
        content: smsForm.content
      }
      response = await sendSmsWithChannel(channelSmsData)
    } else {
      response = await sendSingleSms(smsData)
    }

    if (response.code === 200) {
      ElMessage.success('短信发送成功')
      resetForm()
    } else {
      ElMessage.error(response.message || '短信发送失败')
    }
  } catch (error) {
    console.error('发送短信失败:', error)
    ElMessage.error('发送短信失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  smsFormRef.value.resetFields()
}

// 预览短信
const previewSms = async () => {
  try {
    await smsFormRef.value.validate()
    previewVisible.value = true
  } catch (error) {
    ElMessage.warning('请先完善短信信息')
  }
}

// 组件挂载时获取渠道列表
onMounted(() => {
  getChannelList()
})
</script>

<style scoped>
.single-sms-container {
  padding: 20px;
}

.box-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.sms-form {
  margin-top: 20px;
}

.sms-preview {
  padding: 20px;
}

.preview-item {
  margin-bottom: 15px;
  line-height: 1.6;
}

.content-preview {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 60px;
  white-space: pre-wrap;
}
</style>