/**
 * T061-T070: 邮件单发功能测试
 * 基于需求文档中的邮件发送管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class EmailSingleTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T061: 邮件单发页面加载测试
   */
  async testT061_EmailPageLoad() {
    const testId = 'T061';
    console.log(`\n🧪 执行测试 ${testId}: 邮件单发页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到邮件单发页面
      await this.testHelper.navigateTo('/message/email/single');
      await this.testHelper.waitForPageLoad(selectors.email.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isEmailInputVisible = await this.testHelper.verifyElementVisibility(selectors.email.emailInput);
      const isSubjectInputVisible = await this.testHelper.verifyElementVisibility(selectors.email.subjectInput);
      const isContentTextareaVisible = await this.testHelper.verifyElementVisibility(selectors.email.contentTextarea);
      const isSendButtonVisible = await this.testHelper.verifyElementVisibility(selectors.email.sendButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '邮件单发页面加载测试',
        testContent: '验证邮件单发页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的邮件发送界面',
        testInput: '访问邮件单发页面URL: /message/email/single',
        expectedOutput: '页面正常加载，显示邮箱输入框、主题输入框、内容输入框和发送按钮',
        actualOutput: `邮箱输入框: ${isEmailInputVisible ? '✅显示' : '❌隐藏'}, 主题输入框: ${isSubjectInputVisible ? '✅显示' : '❌隐藏'}, 内容输入框: ${isContentTextareaVisible ? '✅显示' : '❌隐藏'}, 发送按钮: ${isSendButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isEmailInputVisible && isSubjectInputVisible && isContentTextareaVisible && isSendButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '邮件单发页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T062: 有效邮件发送测试
   */
  async testT062_ValidEmailSend() {
    const testId = 'T062';
    console.log(`\n🧪 执行测试 ${testId}: 有效邮件发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录并获取发送前统计
      await this.testHelper.login();
      const beforeStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('发送前仪表板统计');
      
      // 导航到邮件单发页面
      await this.testHelper.navigateTo('/message/email/single');
      await this.testHelper.waitForPageLoad(selectors.email.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写邮件信息
      await this.testHelper.page.fill(selectors.email.emailInput, testData.email.validEmail);
      await this.testHelper.page.fill(selectors.email.subjectInput, testData.email.subject);
      await this.testHelper.page.fill(selectors.email.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送邮件
      await this.testHelper.page.click(selectors.email.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      // 返回仪表板查看统计变化
      await this.testHelper.navigateTo('/dashboard');
      const afterStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('发送后仪表板统计');
      
      const statsIncreased = afterStats.emailCount > beforeStats.emailCount;
      
      const result = {
        testId: testId,
        testName: '有效邮件发送测试',
        testContent: '使用有效的邮箱地址、主题和内容进行邮件发送',
        testPurpose: '验证邮件发送功能能够正常工作',
        testInput: `邮箱: ${testData.email.validEmail}, 主题: ${testData.email.subject}, 内容: ${testData.email.content}`,
        expectedOutput: '邮件发送成功，显示成功提示，发送量统计增加',
        actualOutput: `成功消息: ${successMessage || '无'}, 发送量变化: ${beforeStats.emailCount} → ${afterStats.emailCount}`,
        result: successMessage && statsIncreased ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '有效邮件发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T063: 无效邮箱地址测试
   */
  async testT063_InvalidEmailAddress() {
    const testId = 'T063';
    console.log(`\n🧪 执行测试 ${testId}: 无效邮箱地址测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件单发页面
      await this.testHelper.navigateTo('/message/email/single');
      await this.testHelper.waitForPageLoad(selectors.email.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写无效邮箱地址
      await this.testHelper.page.fill(selectors.email.emailInput, testData.email.invalidEmail);
      await this.testHelper.page.fill(selectors.email.subjectInput, testData.email.subject);
      await this.testHelper.page.fill(selectors.email.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送邮件
      await this.testHelper.page.click(selectors.email.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '无效邮箱地址测试',
        testContent: '使用无效的邮箱地址进行邮件发送',
        testPurpose: '验证系统对无效邮箱地址的验证机制',
        testInput: `邮箱: ${testData.email.invalidEmail}, 主题: ${testData.email.subject}, 内容: ${testData.email.content}`,
        expectedOutput: '发送失败，显示邮箱格式错误的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '无效邮箱地址测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T064: 空邮件主题测试
   */
  async testT064_EmptySubject() {
    const testId = 'T064';
    console.log(`\n🧪 执行测试 ${testId}: 空邮件主题测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件单发页面
      await this.testHelper.navigateTo('/message/email/single');
      await this.testHelper.waitForPageLoad(selectors.email.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写邮件信息但不填主题
      await this.testHelper.page.fill(selectors.email.emailInput, testData.email.validEmail);
      await this.testHelper.page.fill(selectors.email.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送邮件
      await this.testHelper.page.click(selectors.email.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空邮件主题测试',
        testContent: '在邮件主题为空的情况下尝试发送邮件',
        testPurpose: '验证系统对空邮件主题的验证机制',
        testInput: `邮箱: ${testData.email.validEmail}, 主题: (空), 内容: ${testData.email.content}`,
        expectedOutput: '发送失败，显示邮件主题不能为空的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空邮件主题测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T065: 空邮件内容测试
   */
  async testT065_EmptyContent() {
    const testId = 'T065';
    console.log(`\n🧪 执行测试 ${testId}: 空邮件内容测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件单发页面
      await this.testHelper.navigateTo('/message/email/single');
      await this.testHelper.waitForPageLoad(selectors.email.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写邮件信息但不填内容
      await this.testHelper.page.fill(selectors.email.emailInput, testData.email.validEmail);
      await this.testHelper.page.fill(selectors.email.subjectInput, testData.email.subject);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送邮件
      await this.testHelper.page.click(selectors.email.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空邮件内容测试',
        testContent: '在邮件内容为空的情况下尝试发送邮件',
        testPurpose: '验证系统对空邮件内容的验证机制',
        testInput: `邮箱: ${testData.email.validEmail}, 主题: ${testData.email.subject}, 内容: (空)`,
        expectedOutput: '发送失败，显示邮件内容不能为空的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空邮件内容测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有邮件单发测试
   */
  async runAllTests() {
    console.log('🚀 开始执行邮件单发功能测试套件 (T061-T065)');
    
    const startTime = Date.now();
    
    await this.testT061_EmailPageLoad();
    await this.testT062_ValidEmailSend();
    await this.testT063_InvalidEmailAddress();
    await this.testT064_EmptySubject();
    await this.testT065_EmptyContent();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 邮件单发功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const emailTest = new EmailSingleTest();
  emailTest.runAllTests().catch(console.error);
}

module.exports = EmailSingleTest;
