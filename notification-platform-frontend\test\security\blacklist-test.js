/**
 * T191-T200: 黑名单管理功能测试
 * 基于需求文档中的安全管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class BlacklistTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T191: 黑名单管理页面加载测试
   */
  async testT191_BlacklistPageLoad() {
    const testId = 'T191';
    console.log(`\n🧪 执行测试 ${testId}: 黑名单管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isTableVisible = await this.testHelper.verifyElementVisibility(selectors.common.table);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      const isPhoneInputVisible = await this.testHelper.verifyElementVisibility(selectors.security.phoneInput);
      const isEmailInputVisible = await this.testHelper.verifyElementVisibility(selectors.security.emailInput);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '黑名单管理页面加载测试',
        testContent: '验证黑名单管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的黑名单管理界面',
        testInput: '访问黑名单管理页面URL: /security/blacklist',
        expectedOutput: '页面正常加载，显示黑名单列表、搜索框和管理按钮',
        actualOutput: `黑名单列表: ${isTableVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}, 手机号搜索: ${isPhoneInputVisible ? '✅显示' : '❌隐藏'}, 邮箱搜索: ${isEmailInputVisible ? '✅显示' : '❌隐藏'}`,
        result: isTableVisible && isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '黑名单管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T192: 新增手机号黑名单测试
   */
  async testT192_AddPhoneBlacklist() {
    const testId = 'T192';
    console.log(`\n🧪 执行测试 ${testId}: 新增手机号黑名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增黑名单对话框');
      
      // 生成唯一的手机号
      const uniquePhone = this.testHelper.generateRandomPhone();
      const blacklistData = {
        phone: uniquePhone,
        reason: `测试黑名单_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写黑名单信息
      await this.testHelper.page.fill(selectors.security.phoneInput, blacklistData.phone);
      await this.testHelper.page.fill(selectors.security.reasonInput, blacklistData.reason);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存黑名单
      await this.testHelper.page.click(selectors.common.confirmButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增手机号黑名单测试',
        testContent: '添加一个手机号到黑名单',
        testPurpose: '验证手机号黑名单新增功能能够正常工作',
        testInput: `手机号: ${blacklistData.phone}, 原因: ${blacklistData.reason}`,
        expectedOutput: '黑名单添加成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增手机号黑名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T193: 新增邮箱黑名单测试
   */
  async testT193_AddEmailBlacklist() {
    const testId = 'T193';
    console.log(`\n🧪 执行测试 ${testId}: 新增邮箱黑名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增邮箱黑名单对话框');
      
      // 生成唯一的邮箱
      const uniqueEmail = this.testHelper.generateRandomEmail();
      const blacklistData = {
        email: uniqueEmail,
        reason: `测试邮箱黑名单_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写黑名单信息
      await this.testHelper.page.fill(selectors.security.emailInput, blacklistData.email);
      await this.testHelper.page.fill(selectors.security.reasonInput, blacklistData.reason);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存黑名单
      await this.testHelper.page.click(selectors.common.confirmButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增邮箱黑名单测试',
        testContent: '添加一个邮箱到黑名单',
        testPurpose: '验证邮箱黑名单新增功能能够正常工作',
        testInput: `邮箱: ${blacklistData.email}, 原因: ${blacklistData.reason}`,
        expectedOutput: '邮箱黑名单添加成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增邮箱黑名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T194: 黑名单搜索功能测试
   */
  async testT194_BlacklistSearch() {
    const testId = 'T194';
    console.log(`\n🧪 执行测试 ${testId}: 黑名单搜索功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取搜索前的黑名单列表
      const beforeSearchData = await this.testHelper.getTableData(selectors.common.table);
      
      // 执行搜索
      const searchParams = {
        phone: testData.security.blacklist.phone
      };
      
      const searchSelectors = {
        phone: selectors.security.phoneInput
      };
      
      await this.testHelper.search(searchParams, searchSelectors);
      await this.screenshotHelper.takeSearchResultScreenshot();
      
      // 获取搜索后的黑名单列表
      const afterSearchData = await this.testHelper.getTableData(selectors.common.table);
      
      const result = {
        testId: testId,
        testName: '黑名单搜索功能测试',
        testContent: '使用手机号进行黑名单搜索',
        testPurpose: '验证黑名单搜索功能能够正确过滤数据',
        testInput: `搜索手机号: ${searchParams.phone}`,
        expectedOutput: '搜索结果显示匹配的黑名单数据',
        actualOutput: `搜索前记录数: ${beforeSearchData.length}, 搜索后记录数: ${afterSearchData.length}`,
        result: afterSearchData.length >= 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '黑名单搜索功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T195: 编辑黑名单测试
   */
  async testT195_EditBlacklist() {
    const testId = 'T195';
    console.log(`\n🧪 执行测试 ${testId}: 编辑黑名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找第一个编辑按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.security.editButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeEditPageScreenshot();
        
        // 修改黑名单原因
        const newReason = `修改后的原因_${this.testHelper.generateRandomString(4)}`;
        await this.testHelper.page.fill(selectors.security.reasonInput, newReason);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存修改
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '编辑黑名单测试',
          testContent: '编辑现有黑名单记录的信息',
          testPurpose: '验证黑名单编辑功能能够正常工作',
          testInput: `修改原因为: ${newReason}`,
          expectedOutput: '黑名单修改成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (editError) {
        // 如果没有可编辑的黑名单，标记为跳过
        const result = {
          testId: testId,
          testName: '编辑黑名单测试',
          testContent: '编辑现有黑名单记录的信息',
          testPurpose: '验证黑名单编辑功能能够正常工作',
          testInput: '查找可编辑的黑名单记录',
          expectedOutput: '找到记录并成功编辑',
          actualOutput: '未找到可编辑的黑名单记录',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到可编辑的黑名单记录`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '编辑黑名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T196: 删除黑名单测试
   */
  async testT196_DeleteBlacklist() {
    const testId = 'T196';
    console.log(`\n🧪 执行测试 ${testId}: 删除黑名单测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到黑名单管理页面
      await this.testHelper.navigateTo('/security/blacklist');
      await this.testHelper.waitForPageLoad(selectors.security.blacklistContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取删除前的记录数
      const beforeDeleteData = await this.testHelper.getTableData(selectors.common.table);
      
      // 查找第一个删除按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.security.deleteButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeDeleteConfirmScreenshot();
        
        // 确认删除
        await this.testHelper.page.click(selectors.common.confirmButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        // 获取删除后的记录数
        const afterDeleteData = await this.testHelper.getTableData(selectors.common.table);
        
        const result = {
          testId: testId,
          testName: '删除黑名单测试',
          testContent: '删除现有的黑名单记录',
          testPurpose: '验证黑名单删除功能能够正常工作',
          testInput: '选择黑名单记录并确认删除',
          expectedOutput: '黑名单删除成功，显示成功提示，记录数减少',
          actualOutput: `成功消息: ${successMessage || '无'}, 记录数变化: ${beforeDeleteData.length} → ${afterDeleteData.length}`,
          result: successMessage && afterDeleteData.length < beforeDeleteData.length ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (deleteError) {
        // 如果没有可删除的黑名单，标记为跳过
        const result = {
          testId: testId,
          testName: '删除黑名单测试',
          testContent: '删除现有的黑名单记录',
          testPurpose: '验证黑名单删除功能能够正常工作',
          testInput: '查找可删除的黑名单记录',
          expectedOutput: '找到记录并成功删除',
          actualOutput: '未找到可删除的黑名单记录',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到可删除的黑名单记录`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '删除黑名单测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有黑名单管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行黑名单管理功能测试套件 (T191-T196)');
    
    const startTime = Date.now();
    
    await this.testT191_BlacklistPageLoad();
    await this.testT192_AddPhoneBlacklist();
    await this.testT193_AddEmailBlacklist();
    await this.testT194_BlacklistSearch();
    await this.testT195_EditBlacklist();
    await this.testT196_DeleteBlacklist();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 黑名单管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const blacklistTest = new BlacklistTest();
  blacklistTest.runAllTests().catch(console.error);
}

module.exports = BlacklistTest;
