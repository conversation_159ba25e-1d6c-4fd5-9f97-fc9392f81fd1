<template>
  <div class="blacklist-container">
    <!-- 搜索区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="黑名单类型">
          <el-select v-model="searchForm.blacklistType" placeholder="请选择类型" clearable style="width: 150px;">
            <el-option label="手机号" :value="1" />
            <el-option label="邮箱" :value="2" />
            <el-option label="IP地址" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="黑名单值">
          <el-input v-model="searchForm.blacklistValue" placeholder="请输入黑名单值" clearable style="width: 200px;" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 120px;">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作区域 -->
    <el-card class="operation-card">
      <div class="operation-bar">
        <div class="left-operations">
          <el-button type="primary" @click="handleAdd">新增黑名单</el-button>
          <el-button type="danger" :disabled="selectedRows.length === 0" @click="handleBatchDelete">
            批量删除
          </el-button>
          <el-button type="success" @click="handleImport">批量导入</el-button>
        </div>
        <div class="right-operations">
          <el-button type="info" @click="handleStatistics">统计信息</el-button>
          <el-button @click="handleRefresh">刷新</el-button>
        </div>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        stripe
        border
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="blacklistTypeName" label="类型" width="100" />
        <el-table-column prop="blacklistValue" label="黑名单值" min-width="150" />
        <el-table-column prop="reason" label="原因" min-width="120" show-overflow-tooltip />
        <el-table-column prop="statusName" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.statusName }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="effectiveTime" label="生效时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.effectiveTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="expiryTime" label="失效时间" width="160">
          <template #default="{ row }">
            {{ row.expiryTime ? formatDateTime(row.expiryTime) : '永久' }}
          </template>
        </el-table-column>
        <el-table-column prop="expired" label="是否过期" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.expired" type="warning">已过期</el-tag>
            <el-tag v-else type="success">有效</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
            <el-button type="text" size="small" @click="handleDelete(row)" style="color: #f56c6c;">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item label="黑名单类型" prop="blacklistType">
          <el-select v-model="formData.blacklistType" placeholder="请选择类型" style="width: 100%">
            <el-option label="手机号" :value="1" />
            <el-option label="邮箱" :value="2" />
            <el-option label="IP地址" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="黑名单值" prop="blacklistValue">
          <el-input v-model="formData.blacklistValue" placeholder="请输入黑名单值" />
        </el-form-item>
        <el-form-item label="原因" prop="reason">
          <el-input v-model="formData.reason" type="textarea" :rows="3" placeholder="请输入黑名单原因" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="生效时间" prop="effectiveTime">
          <el-date-picker
            v-model="formData.effectiveTime"
            type="datetime"
            placeholder="选择生效时间"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="失效时间" prop="expiryTime">
          <el-date-picker
            v-model="formData.expiryTime"
            type="datetime"
            placeholder="选择失效时间（可选）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :rows="2" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入黑名单"
      width="600px"
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        >
          <template #default>
            <p>1. 请按照模板格式准备数据</p>
            <p>2. 支持的类型：1-手机号，2-邮箱，3-IP地址</p>
            <p>3. 每次最多导入1000条记录</p>
          </template>
        </el-alert>
        
        <el-upload
          ref="uploadRef"
          :file-list="fileList"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xlsx/xls文件，且不超过5MB
              <el-link type="primary" @click="downloadTemplate">下载模板</el-link>
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="importing" @click="handleImportSubmit">开始导入</el-button>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog
      v-model="statisticsDialogVisible"
      title="黑名单统计信息"
      width="500px"
    >
      <div class="statistics-content">
        <div class="stat-item">
          <span class="stat-label">总数量：</span>
          <span class="stat-value">{{ statistics.totalCount || 0 }}</span>
        </div>
        <div class="stat-section">
          <h4>按类型统计</h4>
          <div class="stat-item">
            <span class="stat-label">手机号：</span>
            <span class="stat-value">{{ statistics.typeCount?.phone || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">邮箱：</span>
            <span class="stat-value">{{ statistics.typeCount?.email || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">IP地址：</span>
            <span class="stat-value">{{ statistics.typeCount?.ip || 0 }}</span>
          </div>
        </div>
        <div class="stat-section">
          <h4>按状态统计</h4>
          <div class="stat-item">
            <span class="stat-label">启用：</span>
            <span class="stat-value success">{{ statistics.statusCount?.active || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">禁用：</span>
            <span class="stat-value danger">{{ statistics.statusCount?.inactive || 0 }}</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import { 
  getBlacklistPage, 
  createBlacklist, 
  updateBlacklist, 
  deleteBlacklist, 
  batchDeleteBlacklist,
  updateBlacklistStatus,
  batchImportBlacklist,
  getBlacklistStatistics
} from '@/api/security'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const importing = ref(false)
const dialogVisible = ref(false)
const importDialogVisible = ref(false)
const statisticsDialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const selectedRows = ref([])
const tableData = ref([])
const fileList = ref([])
const statistics = ref({})

// 表单引用
const formRef = ref()
const uploadRef = ref()

// 搜索表单
const searchForm = reactive({
  blacklistType: '',
  blacklistValue: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 表单数据
const formData = reactive({
  id: null,
  blacklistType: '',
  blacklistValue: '',
  reason: '',
  status: 1,
  effectiveTime: null,
  expiryTime: null,
  remark: ''
})

// 表单验证规则
const formRules = {
  blacklistType: [
    { required: true, message: '请选择黑名单类型', trigger: 'change' }
  ],
  blacklistValue: [
    { required: true, message: '请输入黑名单值', trigger: 'blur' },
    { 
      validator: (rule, value, callback) => {
        if (!value) {
          callback()
          return
        }
        
        const type = formData.blacklistType
        let pattern
        let message
        
        switch (type) {
          case 1: // 手机号
            pattern = /^1[3-9]\d{9}$/
            message = '请输入正确的手机号格式'
            break
          case 2: // 邮箱
            pattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            message = '请输入正确的邮箱格式'
            break
          case 3: // IP地址
            pattern = /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/
            message = '请输入正确的IP地址格式'
            break
          default:
            callback()
            return
        }
        
        if (!pattern.test(value)) {
          callback(new Error(message))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取黑名单列表
const getBlacklistList = async () => {
  loading.value = true
  try {
    const response = await getBlacklistPage({
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    })
    
    if (response.code === 200) {
      tableData.value = response.data.records
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '获取黑名单列表失败')
    }
  } catch (error) {
    console.error('获取黑名单列表失败:', error)
    ElMessage.error('获取黑名单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  getBlacklistList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    blacklistType: '',
    blacklistValue: '',
    status: ''
  })
  pagination.current = 1
  getBlacklistList()
}

// 刷新
const handleRefresh = () => {
  getBlacklistList()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  dialogTitle.value = '新增黑名单'
  resetFormData()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  isEdit.value = true
  dialogTitle.value = '编辑黑名单'
  Object.assign(formData, {
    ...row,
    effectiveTime: row.effectiveTime ? new Date(row.effectiveTime) : null,
    expiryTime: row.expiryTime ? new Date(row.expiryTime) : null
  })
  dialogVisible.value = true
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条黑名单记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteBlacklist(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      getBlacklistList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除黑名单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedRows.value.map(row => row.id)
    const response = await batchDeleteBlacklist(ids)
    
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      selectedRows.value = []
      getBlacklistList()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 切换状态
const handleToggleStatus = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(`确定要${statusText}这条黑名单记录吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await updateBlacklistStatus(row.id, newStatus)
    if (response.code === 200) {
      ElMessage.success(`${statusText}成功`)
      getBlacklistList()
    } else {
      ElMessage.error(response.message || `${statusText}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新状态失败:', error)
      ElMessage.error(`${statusText}失败`)
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.current = 1
  getBlacklistList()
}

// 当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current
  getBlacklistList()
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const submitData = {
      ...formData,
      effectiveTime: formData.effectiveTime ? formData.effectiveTime.toISOString() : null,
      expiryTime: formData.expiryTime ? formData.expiryTime.toISOString() : null
    }
    
    let response
    if (isEdit.value) {
      response = await updateBlacklist(submitData)
    } else {
      response = await createBlacklist(submitData)
    }
    
    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '新增成功')
      dialogVisible.value = false
      getBlacklistList()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '新增失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

// 对话框关闭
const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetFormData()
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    blacklistType: '',
    blacklistValue: '',
    reason: '',
    status: 1,
    effectiveTime: null,
    expiryTime: null,
    remark: ''
  })
}

// 批量导入
const handleImport = () => {
  fileList.value = []
  importDialogVisible.value = true
}

// 文件变化
const handleFileChange = (file, files) => {
  fileList.value = files
}

// 文件移除
const handleFileRemove = (file, files) => {
  fileList.value = files
}

// 下载模板
const downloadTemplate = () => {
  const data = [
    ['黑名单类型', '黑名单值', '原因', '状态', '备注'],
    ['1', '13800138000', '测试手机号', '1', '测试数据'],
    ['2', '<EMAIL>', '测试邮箱', '1', '测试数据'],
    ['3', '*************', '测试IP', '1', '测试数据']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '黑名单模板')
  XLSX.writeFile(workbook, '黑名单导入模板.xlsx')
}

// 导入提交
const handleImportSubmit = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要导入的文件')
    return
  }
  
  importing.value = true
  
  try {
    const file = fileList.value[0].raw
    const data = await parseExcelFile(file)
    
    if (data.length === 0) {
      ElMessage.warning('文件中没有有效数据')
      return
    }
    
    const response = await batchImportBlacklist(data)
    if (response.code === 200) {
      ElMessage.success('导入完成')
      ElMessageBox.alert(response.data.result, '导入结果', {
        confirmButtonText: '确定'
      })
      importDialogVisible.value = false
      getBlacklistList()
    } else {
      ElMessage.error(response.message || '导入失败')
    }
  } catch (error) {
    console.error('导入失败:', error)
    ElMessage.error('导入失败')
  } finally {
    importing.value = false
  }
}

// 解析Excel文件
const parseExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        const result = []
        // 跳过表头
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (row && row.length >= 2) {
            result.push({
              blacklistType: parseInt(row[0]) || 1,
              blacklistValue: String(row[1] || '').trim(),
              reason: String(row[2] || '').trim(),
              status: parseInt(row[3]) || 1,
              remark: String(row[4] || '').trim()
            })
          }
        }
        
        resolve(result)
      } catch (error) {
        reject(error)
      }
    }
    reader.readAsArrayBuffer(file)
  })
}

// 统计信息
const handleStatistics = async () => {
  try {
    const response = await getBlacklistStatistics()
    if (response.code === 200) {
      statistics.value = response.data
      statisticsDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取统计信息失败')
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  getBlacklistList()
})
</script>

<style scoped>
.blacklist-container {
  padding: 20px;
}

.search-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 0;
}

.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.import-content {
  padding: 20px 0;
}

.statistics-content {
  padding: 20px 0;
}

.stat-section {
  margin-bottom: 20px;
}

.stat-section h4 {
  margin-bottom: 10px;
  color: #303133;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.danger {
  color: #f56c6c;
}
</style>