import request from '@/utils/request'

/**
 * 获取ESB接口统计数据
 */
export function getEsbStats() {
  return request({
    url: '/api/esb/monitor/stats',
    method: 'get'
  })
}

/**
 * 获取ESB接口列表统计
 */
export function getEsbInterfaces() {
  return request({
    url: '/api/esb/monitor/interfaces',
    method: 'get'
  })
}

/**
 * 获取ESB接口日志
 */
export function getEsbLogs(params) {
  return request({
    url: '/api/esb/monitor/logs',
    method: 'get',
    params
  })
}

/**
 * 分页查询ESB消息日志
 */
export function getEsbMessageLogPage(params) {
  return request({
    url: '/api/esb/message-log/page',
    method: 'get',
    params
  })
}

/**
 * 根据全局流水号查询详情
 */
export function getEsbMessageLogDetail(globalSeq) {
  return request({
    url: `/api/esb/message-log/detail/${globalSeq}`,
    method: 'get'
  })
}

/**
 * 获取发送状态统计
 */
export function getEsbStatistics(params) {
  return request({
    url: '/api/esb/message-log/statistics',
    method: 'get',
    params
  })
}

/**
 * 重置监控数据
 */
export function resetEsbMetrics() {
  return request({
    url: '/api/esb/monitor/reset',
    method: 'post'
  })
}