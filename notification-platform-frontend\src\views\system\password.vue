<template>
  <div class="password-container">
    <div class="page-header">
      <h2>修改密码</h2>
    </div>
    
    <div class="password-form">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        style="max-width: 500px"
      >
        <el-form-item label="原密码" prop="oldPassword">
          <el-input
            v-model="form.oldPassword"
            type="password"
            placeholder="请输入原密码"
            show-password
            maxlength="20"
          />
        </el-form-item>
        
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="form.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
            maxlength="20"
          />
          <div class="password-tips">
            <p>密码要求：</p>
            <ul>
              <li>长度6-20个字符</li>
              <li>必须包含大写字母、小写字母、数字和特殊字符</li>
              <li>不能与最近5次使用的密码相同</li>
            </ul>
          </div>
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="form.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
            maxlength="20"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            修改密码
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { changePassword, getPublicKey } from '@/api/auth'
import { clearAuth } from '@/utils/auth'
import rsaUtil from '@/utils/rsa'

const router = useRouter()
const loading = ref(false)
const formRef = ref(null)

const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const rules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        // 密码复杂度验证：包含大小写字母、数字和特殊字符
        const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
        if (value && !regex.test(value)) {
          callback(new Error('密码必须包含大小写字母、数字和特殊字符'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== form.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 初始化RSA公钥
const initRSAKey = async (retryCount = 0) => {
  const maxRetries = 3
  
  try {
    console.log('正在获取RSA公钥...')
    const response = await getPublicKey()
    console.log('原始响应:', response)
    
    // 处理响应格式 - request拦截器已经处理了嵌套结构
    let publicKeyData
    if (response.code === 200 && response.data) {
      // 响应拦截器返回的格式：{code: 200, data: "公钥字符串", message: "获取公钥成功"}
      publicKeyData = response.data
    } else if (typeof response === 'string') {
      // 直接是公钥字符串
      publicKeyData = response
    } else {
      throw new Error('无法解析公钥数据')
    }
    
    console.log('提取的公钥:', publicKeyData)
    
    if (publicKeyData && typeof publicKeyData === 'string') {
      console.log('开始设置公钥...')
      rsaUtil.setPublicKey(publicKeyData)
      console.log('RSA公钥初始化成功')
      return true
    } else {
      console.log('公钥数据验证失败')
      throw new Error('公钥数据格式错误')
    }
  } catch (error) {
    console.error(`获取RSA公钥失败 (尝试 ${retryCount + 1}/${maxRetries}):`, error)
    
    if (retryCount < maxRetries - 1) {
      // 延迟重试
      setTimeout(() => {
        initRSAKey(retryCount + 1)
      }, 1000 * (retryCount + 1))
    } else {
      ElMessage.error('系统初始化失败，请刷新页面重试')
    }
    return false
  }
}

// 提交表单
const handleSubmit = () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      
      try {
        // 检查RSA是否已初始化
        if (!rsaUtil.checkInitialized()) {
          ElMessage.warning('系统正在初始化，请稍后重试')
          // 尝试重新初始化
          await initRSAKey()
          if (!rsaUtil.checkInitialized()) {
            throw new Error('系统初始化失败')
          }
        }
        
        // 加密密码
        const encryptedOldPassword = rsaUtil.encryptPassword(form.oldPassword)
        const encryptedNewPassword = rsaUtil.encryptPassword(form.newPassword)
        const encryptedConfirmPassword = rsaUtil.encryptPassword(form.confirmPassword)
        
        const response = await changePassword({
          oldPassword: encryptedOldPassword,
          newPassword: encryptedNewPassword,
          confirmPassword: encryptedConfirmPassword
        })
        
        if (response.data && response.data.code === 200) {
          await ElMessageBox.confirm(
            '密码修改成功，需要重新登录。点击确定将跳转到登录页面。',
            '修改成功',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'success'
            }
          )
          
          // 清除认证信息并跳转到登录页
          clearAuth()
          router.push('/login')
        } else {
          ElMessage.error(response.data?.message || '修改密码失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error(error.message || '修改密码失败')
      } finally {
        loading.value = false
      }
    }
  })
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  Object.assign(form, {
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
}

// 组件挂载时初始化RSA公钥
onMounted(() => {
  initRSAKey()
})
</script>

<style lang="scss" scoped>
.password-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 30px;
    
    h2 {
      color: #303133;
      font-size: 20px;
      font-weight: 500;
      margin: 0;
    }
  }
  
  .password-form {
    background: #fff;
    padding: 30px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .password-tips {
      margin-top: 8px;
      font-size: 12px;
      color: #909399;
      
      p {
        margin: 0 0 4px 0;
        font-weight: 500;
      }
      
      ul {
        margin: 0;
        padding-left: 16px;
        
        li {
          margin-bottom: 2px;
        }
      }
    }
  }
}
</style>