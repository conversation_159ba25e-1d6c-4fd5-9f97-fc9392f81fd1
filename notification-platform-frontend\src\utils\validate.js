/**
 * 判断是否为外部链接
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * 验证手机号
 * @param {string} phone
 * @returns {Boolean}
 */
export function validatePhone(phone) {
  // 境内手机号正则
  const domesticPattern = /^1(3[0-9]|4[5,7]|5[0-3,5-9]|6[6]|7[0,3,5-8]|8[0-9]|9[8,9])[0-9]{8}$/;
  // 境外手机号判断
  const isInternational = phone.length > 11 && phone.startsWith('+') && !phone.startsWith('+86');
  
  return domesticPattern.test(phone) || isInternational;
}

/**
 * 验证邮箱
 * @param {string} email
 * @returns {Boolean}
 */
export function validateEmail(email) {
  const pattern = /^[a-zA-Z0-9]([a-zA-Z0-9_\-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9\-]*[a-zA-Z0-9])?(\.[a-zA-Z]{2,})+$/;
  return pattern.test(email);
}

/**
 * 验证密码复杂度
 * @param {string} password
 * @returns {Boolean}
 */
export function validatePasswordComplexity(password) {
  // 至少包含大小写字母、数字和特殊字符中的三种，长度至少8位
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
  
  const typesCount = [hasUpperCase, hasLowerCase, hasNumber, hasSpecialChar].filter(Boolean).length;
  
  return password.length >= 8 && typesCount >= 3;
}