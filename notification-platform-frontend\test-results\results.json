{"config": {"configFile": "D:\\浦银金科\\notification_prototype\\notification-platform-frontend\\playwright.config.js", "rootDir": "D:/浦银金科/notification_prototype/notification-platform-frontend/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report", "open": "never"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/浦银金科/notification_prototype/notification-platform-frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "D:/浦银金科/notification_prototype/notification-platform-frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 1, "webServer": {"command": "npm run dev", "port": 3000, "reuseExistingServer": true}}, "suites": [{"title": "login.spec.js", "file": "login.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "登录功能测试", "file": "login.spec.js", "line": 3, "column": 6, "specs": [{"title": "T001-登录页面加载测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1741, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:12.404Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "badc1754af1944f68b2b-15f7238ca22853a00cf5", "file": "login.spec.js", "line": 9, "column": 3}, {"title": "T002-用户名密码填写测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1438, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:14.546Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "badc1754af1944f68b2b-c0b041419da70468db64", "file": "login.spec.js", "line": 58, "column": 3}, {"title": "T003-登录提交测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3290, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:15.988Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "badc1754af1944f68b2b-e706423bde36e0e93210", "file": "login.spec.js", "line": 83, "column": 3}, {"title": "T004-空用户名登录测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1415, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:19.282Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "badc1754af1944f68b2b-6873d5d0d02a6b9fbde5", "file": "login.spec.js", "line": 112, "column": 3}, {"title": "T005-空密码登录测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3449, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:20.701Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "badc1754af1944f68b2b-df6bc83dee422e2202fe", "file": "login.spec.js", "line": 136, "column": 3}]}]}, {"title": "sms.spec.js", "file": "sms.spec.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "短信功能测试", "file": "sms.spec.js", "line": 3, "column": 6, "specs": [{"title": "T006-短信发送功能完整流程测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 7577, "errors": [], "stdout": [{"text": "发送前今日短信发送量: 15\n"}, {"text": "发送后今日短信发送量: 16\n"}, {"text": "发送量对比: 15 -> 16\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:24.159Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "55402eff475a61deba59-a805edc4b8c1304c7c2c", "file": "sms.spec.js", "line": 21, "column": 3}, {"title": "T007-短信页面元素验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1913, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:31.738Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "55402eff475a61deba59-190553d4be2c6f6287da", "file": "sms.spec.js", "line": 111, "column": 3}, {"title": "T008-无效手机号码测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3812, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:33.655Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "55402eff475a61deba59-ff4bea035ac80271993a", "file": "sms.spec.js", "line": 133, "column": 3}, {"title": "T009-空短信内容测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 4018, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-29T01:41:37.470Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "55402eff475a61deba59-f9dd5e890850de9ba83f", "file": "sms.spec.js", "line": 157, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-29T01:41:11.781Z", "duration": 29937.553, "expected": 9, "skipped": 0, "unexpected": 0, "flaky": 0}}