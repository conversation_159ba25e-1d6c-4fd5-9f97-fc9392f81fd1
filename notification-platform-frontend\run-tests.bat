@echo off
echo ========================================
echo    通知平台前端自动化测试脚本
echo ========================================
echo.

echo 正在检查依赖...
if not exist node_modules (
    echo 安装依赖中...
    npm install
)

echo.
echo 正在启动测试...
echo.

REM 运行所有测试并生成报告
npm run test:all

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 查看测试报告：
echo   - 详细HTML报告: test-results\detailed-report.html
echo   - Playwright报告: test-results\html-report\index.html
echo   - JSON汇总: test-results\test-summary.json
echo.

REM 询问是否打开报告
set /p choice="是否打开测试报告？(y/n): "
if /i "%choice%"=="y" (
    start test-results\detailed-report.html
)

pause
