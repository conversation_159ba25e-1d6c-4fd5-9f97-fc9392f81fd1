/**
 * T121-T130: ESB接口管理功能测试
 * 基于需求文档中的ESB接口管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class ESBInterfaceTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T121: ESB接口管理页面加载测试
   */
  async testT121_ESBInterfacePageLoad() {
    const testId = 'T121';
    console.log(`\n🧪 执行测试 ${testId}: ESB接口管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到ESB接口管理页面
      await this.testHelper.navigateTo('/channel/esb-interface');
      await this.testHelper.waitForPageLoad(selectors.esbInterface.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isInterfaceListVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.interfaceList);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      const isTestConnectionButtonVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.testConnectionButton);
      const isConfigFormVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.configForm);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: 'ESB接口管理页面加载测试',
        testContent: '验证ESB接口管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的ESB接口管理界面',
        testInput: '访问ESB接口管理页面URL: /channel/esb-interface',
        expectedOutput: '页面正常加载，显示接口列表、新增按钮、测试连接按钮和配置表单',
        actualOutput: `接口列表: ${isInterfaceListVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}, 测试连接: ${isTestConnectionButtonVisible ? '✅显示' : '❌隐藏'}, 配置表单: ${isConfigFormVisible ? '✅显示' : '❌隐藏'}`,
        result: isInterfaceListVisible || isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'ESB接口管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T122: 新增ESB接口配置测试
   */
  async testT122_AddESBInterface() {
    const testId = 'T122';
    console.log(`\n🧪 执行测试 ${testId}: 新增ESB接口配置测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到ESB接口管理页面
      await this.testHelper.navigateTo('/channel/esb-interface');
      await this.testHelper.waitForPageLoad(selectors.esbInterface.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增ESB接口对话框');
      
      // 生成唯一的接口信息
      const uniqueInterfaceName = `ESB接口_${this.testHelper.generateRandomString(6)}`;
      const interfaceData = {
        name: uniqueInterfaceName,
        serviceCode: `SVC_${this.testHelper.generateRandomString(4)}`,
        endpoint: 'http://esb.test.com/api/notification',
        method: 'POST',
        timeout: '30000',
        description: `测试ESB接口_${this.testHelper.generateRandomString(4)}`
      };
      
      // 填写接口信息
      await this.testHelper.page.fill(selectors.esbInterface.nameInput, interfaceData.name);
      await this.testHelper.page.fill(selectors.esbInterface.serviceCodeInput, interfaceData.serviceCode);
      await this.testHelper.page.fill(selectors.esbInterface.endpointInput, interfaceData.endpoint);
      await this.testHelper.page.fill(selectors.esbInterface.timeoutInput, interfaceData.timeout);
      await this.testHelper.page.fill(selectors.esbInterface.descriptionInput, interfaceData.description);
      
      // 选择请求方法
      try {
        await this.testHelper.page.selectOption(selectors.esbInterface.methodSelect, interfaceData.method);
      } catch (methodError) {
        // 如果没有方法选择器，跳过
      }
      
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存接口配置
      await this.testHelper.page.click(selectors.esbInterface.saveButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增ESB接口配置测试',
        testContent: '创建一个新的ESB接口配置',
        testPurpose: '验证ESB接口配置新增功能能够正常工作',
        testInput: `接口名称: ${uniqueInterfaceName}, 服务编码: ${interfaceData.serviceCode}, 端点: ${interfaceData.endpoint}`,
        expectedOutput: 'ESB接口配置创建成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增ESB接口配置测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T123: ESB接口连接测试
   */
  async testT123_ESBConnectionTest() {
    const testId = 'T123';
    console.log(`\n🧪 执行测试 ${testId}: ESB接口连接测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到ESB接口管理页面
      await this.testHelper.navigateTo('/channel/esb-interface');
      await this.testHelper.waitForPageLoad(selectors.esbInterface.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 测试第一个接口的连接
      try {
        await this.testHelper.page.click(`${selectors.esbInterface.interfaceList} ${selectors.esbInterface.testConnectionButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeCustomScreenshot('ESB接口连接测试执行中');
        
        // 等待测试结果
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取测试结果消息
        const successMessage = await this.testHelper.getSuccessMessage();
        const errorMessage = await this.testHelper.getErrorMessage();
        
        const result = {
          testId: testId,
          testName: 'ESB接口连接测试',
          testContent: '测试ESB接口的连接状态',
          testPurpose: '验证ESB接口连接测试功能能够正常工作',
          testInput: '点击测试连接按钮',
          expectedOutput: '显示ESB接口连接测试结果',
          actualOutput: `成功消息: ${successMessage || '无'}, 错误消息: ${errorMessage || '无'}`,
          result: successMessage || errorMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (testError) {
        // 如果没有测试连接功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'ESB接口连接测试',
          testContent: '测试ESB接口的连接状态',
          testPurpose: '验证ESB接口连接测试功能能够正常工作',
          testInput: '查找ESB接口连接测试功能',
          expectedOutput: '找到测试按钮并执行连接测试',
          actualOutput: '未找到ESB接口连接测试功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到ESB接口连接测试功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'ESB接口连接测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T124: ESB接口参数配置测试
   */
  async testT124_ESBParameterConfig() {
    const testId = 'T124';
    console.log(`\n🧪 执行测试 ${testId}: ESB接口参数配置测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到ESB接口管理页面
      await this.testHelper.navigateTo('/channel/esb-interface');
      await this.testHelper.waitForPageLoad(selectors.esbInterface.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 编辑第一个接口的参数配置
      try {
        await this.testHelper.page.click(`${selectors.esbInterface.interfaceList} ${selectors.security.editButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeEditPageScreenshot();
        
        // 配置请求参数
        const requestParams = JSON.stringify({
          messageType: 'SMS',
          priority: 'HIGH',
          timeout: 30000,
          retryCount: 3
        });
        
        await this.testHelper.page.fill(selectors.esbInterface.requestParamsTextarea, requestParams);
        
        // 配置响应映射
        const responseMapping = JSON.stringify({
          successCode: '0000',
          errorCodeField: 'errorCode',
          messageField: 'message',
          dataField: 'data'
        });
        
        await this.testHelper.page.fill(selectors.esbInterface.responseMappingTextarea, responseMapping);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存参数配置
        await this.testHelper.page.click(selectors.esbInterface.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: 'ESB接口参数配置测试',
          testContent: '配置ESB接口的请求参数和响应映射',
          testPurpose: '验证ESB接口参数配置功能能够正常工作',
          testInput: `请求参数: ${requestParams}, 响应映射: ${responseMapping}`,
          expectedOutput: 'ESB接口参数配置保存成功',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (configError) {
        // 如果没有参数配置功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'ESB接口参数配置测试',
          testContent: '配置ESB接口的请求参数和响应映射',
          testPurpose: '验证ESB接口参数配置功能能够正常工作',
          testInput: '查找ESB接口参数配置功能',
          expectedOutput: '找到参数配置并成功保存',
          actualOutput: '未找到ESB接口参数配置功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到ESB接口参数配置功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'ESB接口参数配置测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T125: ESB接口调用日志查看测试
   */
  async testT125_ESBCallLogView() {
    const testId = 'T125';
    console.log(`\n🧪 执行测试 ${testId}: ESB接口调用日志查看测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到ESB接口调用日志页面
      await this.testHelper.navigateTo('/channel/esb-interface/logs');
      await this.testHelper.waitForPageLoad(selectors.esbInterface.logContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查看调用日志
      try {
        const isLogTableVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.logTable);
        const isDateFilterVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.dateFilter);
        const isInterfaceFilterVisible = await this.testHelper.verifyElementVisibility(selectors.esbInterface.interfaceFilter);
        
        await this.screenshotHelper.takeCustomScreenshot('ESB接口调用日志页面');
        
        // 获取日志数据
        const logData = await this.testHelper.getTableData(selectors.esbInterface.logTable);
        
        const result = {
          testId: testId,
          testName: 'ESB接口调用日志查看测试',
          testContent: '查看ESB接口的调用日志',
          testPurpose: '验证ESB接口调用日志查看功能能够正常工作',
          testInput: '访问ESB接口调用日志页面',
          expectedOutput: '显示ESB接口调用日志列表和筛选功能',
          actualOutput: `日志表格: ${isLogTableVisible ? '✅显示' : '❌隐藏'}, 日期筛选: ${isDateFilterVisible ? '✅显示' : '❌隐藏'}, 接口筛选: ${isInterfaceFilterVisible ? '✅显示' : '❌隐藏'}, 日志记录数: ${logData.length}`,
          result: isLogTableVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (logError) {
        // 如果没有调用日志功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'ESB接口调用日志查看测试',
          testContent: '查看ESB接口的调用日志',
          testPurpose: '验证ESB接口调用日志查看功能能够正常工作',
          testInput: '查找ESB接口调用日志功能',
          expectedOutput: '找到调用日志页面并显示日志',
          actualOutput: '未找到ESB接口调用日志功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到ESB接口调用日志功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'ESB接口调用日志查看测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有ESB接口管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行ESB接口管理功能测试套件 (T121-T125)');
    
    const startTime = Date.now();
    
    await this.testT121_ESBInterfacePageLoad();
    await this.testT122_AddESBInterface();
    await this.testT123_ESBConnectionTest();
    await this.testT124_ESBParameterConfig();
    await this.testT125_ESBCallLogView();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 ESB接口管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const esbInterfaceTest = new ESBInterfaceTest();
  esbInterfaceTest.runAllTests().catch(console.error);
}

module.exports = ESBInterfaceTest;
