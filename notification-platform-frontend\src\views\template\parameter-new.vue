<template>
  <div class="parameter-manage-container">
    <!-- 查询表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="参数代码">
          <el-input v-model="searchForm.paramCode" placeholder="请输入参数代码" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="参数名称">
          <el-input v-model="searchForm.paramName" placeholder="请输入参数名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="参数分类">
          <el-select v-model="searchForm.category" placeholder="请选择参数分类" clearable style="width: 150px">
            <el-option label="基础参数" value="BASIC" />
            <el-option label="业务参数" value="BUSINESS" />
            <el-option label="系统参数" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="operation-card">
      <el-button type="primary" @click="handleAdd">新增参数</el-button>
      <el-button type="danger" @click="handleBatchDelete" :disabled="selectedRows.length === 0">批量删除</el-button>
      <el-button type="info" @click="handleViewUsageStats">使用统计</el-button>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="paramCode" label="参数代码" width="150" />
        <el-table-column prop="paramName" label="参数名称" width="150" />
        <el-table-column prop="paramType" label="参数类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ getParameterTypeText(row.paramType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paramDescription" label="参数描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="defaultValue" label="默认值" width="120" show-overflow-tooltip />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSearch"
          @current-change="handleSearch"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="handleDialogClose">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="参数代码" prop="paramCode">
          <el-input v-model="formData.paramCode" placeholder="请输入参数代码，如 receive_name" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="参数名称" prop="paramName">
          <el-input v-model="formData.paramName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数类型" prop="paramType">
          <el-select v-model="formData.paramType" placeholder="请选择参数类型">
            <el-option label="字符串" value="STRING" />
            <el-option label="整数" value="INTEGER" />
            <el-option label="小数" value="DECIMAL" />
            <el-option label="日期" value="DATE" />
            <el-option label="布尔值" value="BOOLEAN" />
            <el-option label="长文本" value="TEXT" />
          </el-select>
        </el-form-item>
        <el-form-item label="参数描述" prop="paramDescription">
          <el-input v-model="formData.paramDescription" type="textarea" :rows="3" placeholder="请输入参数描述" />
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="formData.defaultValue" placeholder="请输入默认值（可选）" />
        </el-form-item>
        <el-form-item label="验证规则">
          <el-input v-model="formData.validationRule" placeholder="请输入验证规则（JSON格式，可选）" />
        </el-form-item>
        <el-form-item label="参数分类" prop="category">
          <el-select v-model="formData.category" placeholder="请选择参数分类">
            <el-option label="基础参数" value="BASIC" />
            <el-option label="业务参数" value="BUSINESS" />
            <el-option label="系统参数" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="排序号">
          <el-input-number v-model="formData.sortOrder" :min="0" placeholder="排序号" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 使用统计对话框 -->
    <el-dialog v-model="usageStatsVisible" title="参数使用统计" width="800px">
      <el-table :data="usageStatsData" v-loading="usageStatsLoading" border>
        <el-table-column prop="paramCode" label="参数代码" width="150" />
        <el-table-column prop="paramName" label="参数名称" width="150" />
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag type="info">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="templateCount" label="使用模板数" width="120" />
        <el-table-column prop="paramDescription" label="描述" min-width="200" show-overflow-tooltip />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  getParameterPage, 
  createParameter, 
  updateParameter, 
  deleteParameter, 
  batchDeleteParameters,
  updateParameterStatus,
  getParameterUsageStats
} from '@/api/parameter'

const loading = ref(false)
const dialogVisible = ref(false)
const usageStatsVisible = ref(false)
const usageStatsLoading = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const selectedRows = ref([])
const tableData = ref([])
const usageStatsData = ref([])
const formRef = ref()

const searchForm = reactive({
  paramCode: '',
  paramName: '',
  category: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const formData = reactive({
  id: null,
  paramCode: '',
  paramName: '',
  paramType: 'STRING',
  paramDescription: '',
  category: 'BUSINESS',
  defaultValue: '',
  validationRule: '',
  sortOrder: 0,
  status: 1
})

const formRules = {
  paramCode: [{ required: true, message: '请输入参数代码', trigger: 'blur' }],
  paramName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
  paramType: [{ required: true, message: '请选择参数类型', trigger: 'change' }],
  paramDescription: [{ required: true, message: '请输入参数描述', trigger: 'blur' }],
  category: [{ required: true, message: '请选择参数分类', trigger: 'change' }]
}

const getParameterTypeText = (type) => {
  const typeMap = {
    'STRING': '字符串',
    'INTEGER': '整数',
    'DECIMAL': '小数',
    'DATE': '日期',
    'BOOLEAN': '布尔值',
    'TEXT': '长文本'
  }
  return typeMap[type] || type
}

const getCategoryText = (category) => {
  const categoryMap = {
    'BASIC': '基础参数',
    'BUSINESS': '业务参数',
    'SYSTEM': '系统参数'
  }
  return categoryMap[category] || category
}

// 查询数据
const handleSearch = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getParameterPage(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    console.error('查询参数失败:', error)
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

// 重置查询
const handleReset = () => {
  Object.assign(searchForm, {
    paramCode: '',
    paramName: '',
    category: ''
  })
  pagination.current = 1
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 状态变化
const handleStatusChange = async (row) => {
  try {
    const response = await updateParameterStatus(row.id, row.status)
    if (response.code === 200) {
      ElMessage.success('状态更新成功')
    } else {
      ElMessage.error(response.message || '状态更新失败')
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 删除参数
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除参数"${row.paramName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteParameter(row.id)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      handleSearch()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除参数失败:', error)
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的参数')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个参数吗？`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedRows.value.map(row => row.id)
    const response = await batchDeleteParameters(ids)
    if (response.code === 200) {
      ElMessage.success('批量删除成功')
      handleSearch()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    console.error('批量删除参数失败:', error)
  }
}

// 查看使用统计
const handleViewUsageStats = async () => {
  usageStatsLoading.value = true
  usageStatsVisible.value = true
  try {
    const response = await getParameterUsageStats()
    if (response.code === 200) {
      usageStatsData.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取使用统计失败')
    }
  } catch (error) {
    console.error('获取使用统计失败:', error)
    ElMessage.error('获取使用统计失败')
  } finally {
    usageStatsLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const apiCall = isEdit.value ? updateParameter : createParameter
    const params = isEdit.value ? [formData.id, formData] : [formData]
    const response = await apiCall(...params)
    
    if (response.code === 200) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      handleSearch()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: null,
    paramCode: '',
    paramName: '',
    paramType: 'STRING',
    paramDescription: '',
    category: 'BUSINESS',
    defaultValue: '',
    validationRule: '',
    sortOrder: 0,
    status: 1
  })
}

// 新增参数
const handleAdd = () => {
  resetForm()
  isEdit.value = false
  dialogTitle.value = '新增参数'
  dialogVisible.value = true
}

// 编辑参数
const handleEdit = (row) => {
  Object.assign(formData, { ...row })
  isEdit.value = true
  dialogTitle.value = '编辑参数'
  dialogVisible.value = true
}

// 关闭对话框
const handleDialogClose = () => {
  resetForm()
}

onMounted(() => {
  handleSearch()
})
</script>

<style scoped>
.parameter-manage-container {
  height: 100%;
  padding: 20px;
}

.search-card,
.operation-card,
.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>