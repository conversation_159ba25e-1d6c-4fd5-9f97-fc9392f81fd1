/**
 * T031-T040: API接口功能测试
 * 基于需求文档中的API接口功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class APIInterfaceTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T031: API接口文档页面加载测试
   */
  async testT031_APIDocumentationPageLoad() {
    const testId = 'T031';
    console.log(`\n🧪 执行测试 ${testId}: API接口文档页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到API文档页面
      await this.testHelper.navigateTo('/api/documentation');
      await this.testHelper.waitForPageLoad(selectors.api.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isApiListVisible = await this.testHelper.verifyElementVisibility(selectors.api.apiList);
      const isSearchBoxVisible = await this.testHelper.verifyElementVisibility(selectors.api.searchBox);
      const isApiDetailVisible = await this.testHelper.verifyElementVisibility(selectors.api.apiDetail);
      const isTryItButtonVisible = await this.testHelper.verifyElementVisibility(selectors.api.tryItButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: 'API接口文档页面加载测试',
        testContent: '验证API接口文档页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保开发者能够看到完整的API文档界面',
        testInput: '访问API文档页面URL: /api/documentation',
        expectedOutput: '页面正常加载，显示API列表、搜索框、API详情和试用按钮',
        actualOutput: `API列表: ${isApiListVisible ? '✅显示' : '❌隐藏'}, 搜索框: ${isSearchBoxVisible ? '✅显示' : '❌隐藏'}, API详情: ${isApiDetailVisible ? '✅显示' : '❌隐藏'}, 试用按钮: ${isTryItButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isApiListVisible || isApiDetailVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'API接口文档页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T032: API接口搜索测试
   */
  async testT032_APISearch() {
    const testId = 'T032';
    console.log(`\n🧪 执行测试 ${testId}: API接口搜索测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到API文档页面
      await this.testHelper.navigateTo('/api/documentation');
      await this.testHelper.waitForPageLoad(selectors.api.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行API搜索
      const searchKeyword = 'send';
      await this.testHelper.page.fill(selectors.api.searchBox, searchKeyword);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('API搜索输入');
      
      // 点击搜索按钮或按回车
      try {
        await this.testHelper.page.click(selectors.api.searchButton);
      } catch (searchButtonError) {
        await this.testHelper.page.press(selectors.api.searchBox, 'Enter');
      }
      
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取搜索结果
      const searchResults = await this.testHelper.getListData(selectors.api.apiList);
      
      const result = {
        testId: testId,
        testName: 'API接口搜索测试',
        testContent: '使用关键字搜索API接口',
        testPurpose: '验证API接口搜索功能能够正常工作',
        testInput: `搜索关键字: ${searchKeyword}`,
        expectedOutput: '显示包含关键字的API接口列表',
        actualOutput: `搜索结果数量: ${searchResults.length}`,
        result: searchResults.length >= 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'API接口搜索测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T033: API接口详情查看测试
   */
  async testT033_APIDetailView() {
    const testId = 'T033';
    console.log(`\n🧪 执行测试 ${testId}: API接口详情查看测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到API文档页面
      await this.testHelper.navigateTo('/api/documentation');
      await this.testHelper.waitForPageLoad(selectors.api.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击第一个API接口查看详情
      try {
        await this.testHelper.page.click(`${selectors.api.apiList} .api-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('API详情展开');
        
        // 验证详情内容
        const isMethodVisible = await this.testHelper.verifyElementVisibility(selectors.api.methodInfo);
        const isParametersVisible = await this.testHelper.verifyElementVisibility(selectors.api.parametersInfo);
        const isResponseVisible = await this.testHelper.verifyElementVisibility(selectors.api.responseInfo);
        const isExampleVisible = await this.testHelper.verifyElementVisibility(selectors.api.exampleCode);
        
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        const result = {
          testId: testId,
          testName: 'API接口详情查看测试',
          testContent: '查看API接口的详细信息',
          testPurpose: '验证API接口详情查看功能能够正常工作',
          testInput: '点击API接口项查看详情',
          expectedOutput: '显示API接口的方法、参数、响应和示例代码',
          actualOutput: `方法信息: ${isMethodVisible ? '✅显示' : '❌隐藏'}, 参数信息: ${isParametersVisible ? '✅显示' : '❌隐藏'}, 响应信息: ${isResponseVisible ? '✅显示' : '❌隐藏'}, 示例代码: ${isExampleVisible ? '✅显示' : '❌隐藏'}`,
          result: isMethodVisible || isParametersVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (detailError) {
        // 如果没有详情查看功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'API接口详情查看测试',
          testContent: '查看API接口的详细信息',
          testPurpose: '验证API接口详情查看功能能够正常工作',
          testInput: '查找API接口详情功能',
          expectedOutput: '找到详情查看并成功显示',
          actualOutput: '未找到API接口详情功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到API接口详情功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'API接口详情查看测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T034: API接口在线测试功能
   */
  async testT034_APIOnlineTest() {
    const testId = 'T034';
    console.log(`\n🧪 执行测试 ${testId}: API接口在线测试功能`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到API文档页面
      await this.testHelper.navigateTo('/api/documentation');
      await this.testHelper.waitForPageLoad(selectors.api.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 尝试在线测试API
      try {
        // 选择一个API接口
        await this.testHelper.page.click(`${selectors.api.apiList} .api-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        
        // 点击试用按钮
        await this.testHelper.page.click(selectors.api.tryItButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('API在线测试界面');
        
        // 填写测试参数
        const testParams = {
          phone: testData.sms.validPhone,
          content: '这是API测试消息'
        };
        
        try {
          await this.testHelper.page.fill(selectors.api.parameterInput.replace('{name}', 'phone'), testParams.phone);
          await this.testHelper.page.fill(selectors.api.parameterInput.replace('{name}', 'content'), testParams.content);
        } catch (paramError) {
          // 如果没有参数输入框，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 执行API测试
        await this.testHelper.page.click(selectors.api.executeButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 检查测试结果
        const isResponseVisible = await this.testHelper.verifyElementVisibility(selectors.api.responseResult);
        const responseContent = await this.testHelper.page.textContent(selectors.api.responseResult);
        
        const result = {
          testId: testId,
          testName: 'API接口在线测试功能',
          testContent: '使用在线工具测试API接口',
          testPurpose: '验证API接口在线测试功能能够正常工作',
          testInput: `测试参数: 手机号=${testParams.phone}, 内容=${testParams.content}`,
          expectedOutput: 'API测试执行成功，显示响应结果',
          actualOutput: `响应结果: ${isResponseVisible ? '✅显示' : '❌隐藏'}, 内容长度: ${responseContent ? responseContent.length : 0}`,
          result: isResponseVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (testError) {
        // 如果没有在线测试功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'API接口在线测试功能',
          testContent: '使用在线工具测试API接口',
          testPurpose: '验证API接口在线测试功能能够正常工作',
          testInput: '查找API在线测试功能',
          expectedOutput: '找到在线测试并成功执行',
          actualOutput: '未找到API在线测试功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到API在线测试功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'API接口在线测试功能',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T035: API密钥管理测试
   */
  async testT035_APIKeyManagement() {
    const testId = 'T035';
    console.log(`\n🧪 执行测试 ${testId}: API密钥管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到API密钥管理页面
      await this.testHelper.navigateTo('/api/keys');
      await this.testHelper.waitForPageLoad(selectors.api.keyContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 生成新的API密钥
      try {
        await this.testHelper.page.click(selectors.api.generateKeyButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('生成API密钥对话框');
        
        // 填写密钥信息
        const keyName = `测试密钥_${this.testHelper.generateRandomString(4)}`;
        const keyDescription = `API密钥测试_${this.testHelper.generateRandomString(4)}`;
        
        await this.testHelper.page.fill(selectors.api.keyNameInput, keyName);
        await this.testHelper.page.fill(selectors.api.keyDescriptionInput, keyDescription);
        
        // 选择权限
        try {
          await this.testHelper.page.check('.permission-checkbox[data-permission="message:send"]');
        } catch (permissionError) {
          // 如果没有权限选择，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 生成密钥
        await this.testHelper.page.click(selectors.api.confirmGenerateButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 检查生成结果
        const isKeyDisplayed = await this.testHelper.verifyElementVisibility(selectors.api.generatedKey);
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: 'API密钥管理测试',
          testContent: '生成新的API密钥',
          testPurpose: '验证API密钥管理功能能够正常工作',
          testInput: `密钥名称: ${keyName}, 描述: ${keyDescription}`,
          expectedOutput: 'API密钥生成成功，显示新密钥',
          actualOutput: `密钥显示: ${isKeyDisplayed ? '✅显示' : '❌隐藏'}, 成功消息: ${successMessage || '无'}`,
          result: isKeyDisplayed || successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (keyError) {
        // 如果没有密钥管理功能，标记为跳过
        const result = {
          testId: testId,
          testName: 'API密钥管理测试',
          testContent: '生成新的API密钥',
          testPurpose: '验证API密钥管理功能能够正常工作',
          testInput: '查找API密钥管理功能',
          expectedOutput: '找到密钥管理并成功生成',
          actualOutput: '未找到API密钥管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到API密钥管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'API密钥管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有API接口测试
   */
  async runAllTests() {
    console.log('🚀 开始执行API接口功能测试套件 (T031-T035)');
    
    const startTime = Date.now();
    
    await this.testT031_APIDocumentationPageLoad();
    await this.testT032_APISearch();
    await this.testT033_APIDetailView();
    await this.testT034_APIOnlineTest();
    await this.testT035_APIKeyManagement();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 API接口功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const apiInterfaceTest = new APIInterfaceTest();
  apiInterfaceTest.runAllTests().catch(console.error);
}

module.exports = APIInterfaceTest;
