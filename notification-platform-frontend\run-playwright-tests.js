const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class PlaywrightTestRunner {
  constructor() {
    this.setupDirectories();
  }

  setupDirectories() {
    // 创建测试结果目录
    const dirs = [
      'test-results',
      'test-results/screenshots',
      'test-results/html-report'
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  async runTests() {
    console.log('🚀 开始执行Playwright测试案例...\n');
    
    try {
      // 检查前端服务是否运行
      console.log('🔍 检查前端服务状态...');
      
      // 运行Playwright测试
      console.log('📋 执行Playwright测试...');
      const command = 'npx playwright test --reporter=html,json,list';
      
      try {
        const output = execSync(command, { 
          encoding: 'utf8',
          stdio: 'inherit'  // 显示实时输出
        });
        console.log('✅ 测试执行完成');
      } catch (error) {
        console.log('⚠️ 测试执行完成（可能有失败的测试）');
        console.log('错误信息:', error.message);
      }
      
      // 显示报告位置
      console.log('\n📊 测试报告生成完成！');
      console.log('📁 查看报告：');
      console.log('   - Playwright HTML报告: test-results/html-report/index.html');
      console.log('   - 测试截图目录: test-results/screenshots/');
      
      // 检查是否有测试结果文件
      if (fs.existsSync('test-results/results.json')) {
        console.log('   - JSON结果文件: test-results/results.json');
      }
      
      // 统计截图数量
      const screenshotDir = 'test-results/screenshots';
      if (fs.existsSync(screenshotDir)) {
        const screenshots = fs.readdirSync(screenshotDir).filter(f => f.endsWith('.png'));
        console.log(`   - 生成截图: ${screenshots.length} 张`);
      }
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
      throw error;
    }
  }
}

// 主执行函数
async function main() {
  const runner = new PlaywrightTestRunner();
  
  try {
    await runner.runTests();
    
    console.log('\n🎉 测试完成！');
    console.log('\n💡 提示：');
    console.log('   - 使用 "npm run test:report" 查看详细报告');
    console.log('   - 使用 "npx playwright test --ui" 进入交互模式');
    console.log('   - 使用 "npx playwright test --headed" 显示浏览器界面');
    
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = PlaywrightTestRunner;
