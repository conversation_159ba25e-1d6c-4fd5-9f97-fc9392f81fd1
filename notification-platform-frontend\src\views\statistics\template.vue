<template>
  <div class="template-statistics-container">
    <!-- 查询条件 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="消息类型">
          <el-select v-model="queryParams.messageType" placeholder="请选择消息类型" style="width: 150px">
            <el-option label="短信" :value="1" />
            <el-option label="邮件" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="exportData">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 总体统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">总发送数</div>
                <div class="card-value">{{ overallStats.totalCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">成功数</div>
                <div class="card-value">{{ overallStats.successCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">失败数</div>
                <div class="card-value">{{ overallStats.failedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">成功率</div>
                <div class="card-value">{{ (overallStats.successRate || 0).toFixed(2) }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>



    <!-- 模板统计表格 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <span>按模板统计</span>
      </template>
      <el-table
        v-loading="loading"
        :data="templateStats"
        style="width: 100%"
      >
        <el-table-column prop="templateCode" label="模板编号" width="150" />
        <el-table-column prop="templateName" label="模板名称" width="200" show-overflow-tooltip />
        <el-table-column prop="totalCount" label="总发送数" width="120" align="right" />
        <el-table-column prop="successCount" label="成功数" width="120" align="right" />
        <el-table-column prop="failedCount" label="失败数" width="120" align="right" />
        <el-table-column prop="sendingCount" label="发送中" width="120" align="right" />
        <el-table-column prop="unknownCount" label="不明状态" width="120" align="right" />
        <el-table-column label="成功率" width="120" align="right">
          <template #default="scope">
            <el-tag :type="getSuccessRateType(scope.row.successRate)">
              {{ (scope.row.successRate || 0).toFixed(2) }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="viewDetails(scope.row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      title="消息发送详情"
      v-model="detailDialogVisible"
      width="80%"
      :before-close="handleClose"
    >
      <el-table
        v-loading="detailLoading"
        :data="detailList"
        style="width: 100%"
      >
        <el-table-column prop="messageId" label="消息ID" width="200" />
        <el-table-column prop="recipient" label="接收人" width="150" />
        <el-table-column prop="subject" label="主题" width="200" show-overflow-tooltip />
        <el-table-column prop="sendStatus" label="发送状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.sendStatus)">
              {{ getStatusName(scope.row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" width="180" />
        <el-table-column prop="completeTime" label="完成时间" width="180" />
        <el-table-column prop="errorMessage" label="错误信息" show-overflow-tooltip />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button 
              v-if="scope.row.sendStatus === 2" 
              type="warning" 
              size="small" 
              @click="resendMessage(scope.row.messageId)"
            >
              重发
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="detailTotal > 0"
        :total="detailTotal"
        v-model:page="detailQuery.current"
        v-model:limit="detailQuery.size"
        @pagination="getDetailList"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Check, Close, TrendCharts } from '@element-plus/icons-vue'

import { getMessageStatistics, getMessageRecords, resendMessage as resendMessageApi } from '@/api/statistics'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const detailLoading = ref(false)
const detailDialogVisible = ref(false)
const dateRange = ref([])
const overallStats = ref({})
const templateStats = ref([])

const detailList = ref([])
const detailTotal = ref(0)



// 查询参数
const queryParams = reactive({
  messageType: 1,
  startTime: '',
  endTime: ''
})

// 详情查询参数
const detailQuery = reactive({
  current: 1,
  size: 10,
  messageType: 1,
  templateCode: '',
  startTime: '',
  endTime: ''
})

// 生命周期
onMounted(() => {
  // 设置默认时间范围（最近7天）
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)
  
  dateRange.value = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]
  
  handleQuery()
  

})

// 方法
const handleQuery = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择时间范围')
    return
  }
  
  queryParams.startTime = dateRange.value[0]
  queryParams.endTime = dateRange.value[1]
  
  await getStatistics()
}

const resetQuery = () => {
  queryParams.messageType = 1
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)
  
  dateRange.value = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]
  
  handleQuery()
}

const getStatistics = async () => {
  loading.value = true
  try {
    const response = await getMessageStatistics(queryParams)
    const data = response.data
    
    overallStats.value = data.overall || {}
    templateStats.value = data.templateStats || []
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}



const getSuccessRateType = (rate) => {
  if (rate >= 95) return 'success'
  if (rate >= 80) return 'warning'
  return 'danger'
}

const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'
    case 2: return 'danger'
    case 3: return 'warning'
    case 4: return 'info'
    default: return 'info'
  }
}

const getStatusName = (status) => {
  switch (status) {
    case 1: return '发送成功'
    case 2: return '发送失败'
    case 3: return '正在发送'
    case 4: return '不明状态'
    default: return '未知'
  }
}

const viewDetails = async (row) => {
  detailQuery.messageType = queryParams.messageType
  detailQuery.templateCode = row.templateCode
  detailQuery.startTime = queryParams.startTime
  detailQuery.endTime = queryParams.endTime
  detailQuery.current = 1
  
  detailDialogVisible.value = true
  await getDetailList()
}

const getDetailList = async () => {
  detailLoading.value = true
  try {
    const response = await getMessageRecords(detailQuery)
    detailList.value = response.data.records
    detailTotal.value = response.data.total
  } catch (error) {
    console.error('获取详情列表失败:', error)
    ElMessage.error('获取详情列表失败')
  } finally {
    detailLoading.value = false
  }
}

const resendMessage = async (messageId) => {
  try {
    await ElMessageBox.confirm('确定要重发这条消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await resendMessageApi(messageId)
    ElMessage.success('重发成功')
    getDetailList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重发消息失败:', error)
      ElMessage.error('重发失败')
    }
  }
}

const handleClose = () => {
  detailDialogVisible.value = false
}

const exportData = () => {
  // TODO: 实现数据导出功能
  ElMessage.info('导出功能开发中')
}
</script>

<style scoped>
.template-statistics-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.statistics-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.failed {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-icon.rate {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}


</style>