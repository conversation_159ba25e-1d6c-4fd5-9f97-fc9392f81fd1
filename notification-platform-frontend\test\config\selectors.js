// 页面元素选择器配置文件
module.exports = {
  // 登录页面选择器
  login: {
    container: '.login-container',
    usernameInput: 'input[placeholder="用户名"]',
    passwordInput: 'input[placeholder="密码"]',
    loginButton: 'button:has-text("登录")',
    errorMessage: '.error-message',
    forgotPasswordLink: 'a:has-text("忘记密码")',
    rememberCheckbox: '.remember-checkbox'
  },

  // 仪表板页面选择器
  dashboard: {
    container: '.dashboard-container',
    smsCard: '.dashboard-card:has-text("今日短信发送量")',
    emailCard: '.dashboard-card:has-text("今日邮件发送量")',
    templateCard: '.dashboard-card:has-text("模板总数")',
    userCard: '.dashboard-card:has-text("用户总数")',
    cardValue: '.card-value',
    chartContainer: '.chart-container',
    statisticsTable: '.statistics-table'
  },

  // 导航菜单选择器
  navigation: {
    sidebar: '.sidebar',
    menuItem: '.el-menu-item',
    submenu: '.el-submenu',
    breadcrumb: '.el-breadcrumb',
    userAvatar: '.user-avatar',
    logoutButton: 'text=退出登录'
  },

  // 短信单发页面选择器
  sms: {
    container: '.single-sms-container',
    phoneInput: 'input[placeholder="请输入手机号码"]',
    contentTextarea: 'textarea[placeholder="请输入短信内容"]',
    templateSelect: '.template-select',
    sendButton: 'button:has-text("发送短信")',
    clearButton: 'button:has-text("清空")',
    successMessage: '.success-message',
    errorMessage: '.error-message',
    characterCount: '.character-count'
  },

  // 短信批量发送页面选择器
  smsBatch: {
    container: '.batch-sms-container',
    fileUpload: '.file-upload',
    phoneListTextarea: 'textarea[placeholder="请输入手机号码列表"]',
    contentTextarea: 'textarea[placeholder="请输入短信内容"]',
    templateSelect: '.template-select',
    sendButton: 'button:has-text("批量发送")',
    previewButton: 'button:has-text("预览")',
    downloadTemplate: 'button:has-text("下载模板")'
  },

  // 邮件单发页面选择器
  email: {
    container: '.single-email-container',
    emailInput: 'input[placeholder="请输入邮箱地址"]',
    subjectInput: 'input[placeholder="请输入邮件主题"]',
    contentTextarea: 'textarea[placeholder="请输入邮件内容"]',
    htmlEditor: '.html-editor',
    templateSelect: '.template-select',
    sendButton: 'button:has-text("发送邮件")',
    clearButton: 'button:has-text("清空")',
    previewButton: 'button:has-text("预览")'
  },

  // 邮件批量发送页面选择器
  emailBatch: {
    container: '.batch-email-container',
    fileUpload: '.file-upload',
    emailListTextarea: 'textarea[placeholder="请输入邮箱地址列表"]',
    subjectInput: 'input[placeholder="请输入邮件主题"]',
    contentTextarea: 'textarea[placeholder="请输入邮件内容"]',
    htmlEditor: '.html-editor',
    templateSelect: '.template-select',
    sendButton: 'button:has-text("批量发送")',
    previewButton: 'button:has-text("预览")'
  },

  // 模板管理页面选择器
  template: {
    container: '.template-container',
    searchForm: '.search-form',
    codeInput: 'input[placeholder="请输入模板编码"]',
    nameInput: 'input[placeholder="请输入模板名称"]',
    typeSelect: '.type-select',
    statusSelect: '.status-select',
    searchButton: 'button:has-text("查询")',
    resetButton: 'button:has-text("重置")',
    addButton: 'button:has-text("新增")',
    table: '.el-table',
    editButton: 'button:has-text("编辑")',
    deleteButton: 'button:has-text("删除")',
    pagination: '.el-pagination'
  },

  // 模板编辑页面选择器
  templateEdit: {
    container: '.template-edit-container',
    codeInput: 'input[placeholder="请输入模板编码"]',
    nameInput: 'input[placeholder="请输入模板名称"]',
    typeSelect: '.type-select',
    prioritySelect: '.priority-select',
    subjectInput: 'input[placeholder="请输入邮件主题"]',
    contentTextarea: 'textarea[placeholder="请输入模板内容"]',
    parameterList: '.parameter-list',
    addParameterButton: 'button:has-text("添加参数")',
    saveButton: 'button:has-text("保存")',
    cancelButton: 'button:has-text("取消")',
    previewButton: 'button:has-text("预览")'
  },

  // 用户管理页面选择器
  user: {
    container: '.user-container',
    searchForm: '.search-form',
    loginNameInput: 'input[placeholder="请输入登录名"]',
    userNameInput: 'input[placeholder="请输入用户名"]',
    phoneInput: 'input[placeholder="请输入手机号"]',
    emailInput: 'input[placeholder="请输入邮箱"]',
    orgSelect: '.org-select',
    roleSelect: '.role-select',
    statusSelect: '.status-select',
    searchButton: 'button:has-text("查询")',
    resetButton: 'button:has-text("重置")',
    addButton: 'button:has-text("新增用户")',
    table: '.el-table',
    editButton: 'button:has-text("编辑")',
    deleteButton: 'button:has-text("删除")',
    resetPasswordButton: 'button:has-text("重置密码")'
  },

  // 角色管理页面选择器
  role: {
    container: '.role-container',
    nameInput: 'input[placeholder="请输入角色名称"]',
    codeInput: 'input[placeholder="请输入角色编码"]',
    descriptionTextarea: 'textarea[placeholder="请输入角色描述"]',
    permissionTree: '.permission-tree',
    saveButton: 'button:has-text("保存")',
    cancelButton: 'button:has-text("取消")'
  },

  // 渠道管理页面选择器
  channel: {
    container: '.channel-container',
    accessChannelTab: 'text=接入渠道',
    sendChannelTab: 'text=发送渠道',
    nameInput: 'input[placeholder="请输入渠道名称"]',
    typeSelect: '.type-select',
    statusSelect: '.status-select',
    configTextarea: 'textarea[placeholder="请输入配置信息"]',
    testButton: 'button:has-text("测试连接")',
    saveButton: 'button:has-text("保存")',
    enableButton: 'button:has-text("启用")',
    disableButton: 'button:has-text("禁用")'
  },

  // 统计分析页面选择器
  statistics: {
    container: '.statistics-container',
    templateStatTab: 'text=模板发送统计',
    messageStatTab: 'text=消息发送详情',
    dateRangePicker: '.date-range-picker',
    templateSelect: '.template-select',
    statusSelect: '.status-select',
    messageTypeFilter: '.message-type-filter',
    statusFilter: '.status-filter',
    queryButton: 'button:has-text("查询")',
    exportButton: 'button:has-text("导出")',
    chart: '.chart-container',
    table: '.statistics-table',
    detailTable: '.detail-table'
  },

  // 安全管理页面选择器
  security: {
    blacklistContainer: '.blacklist-container',
    whitelistContainer: '.whitelist-container',
    keywordContainer: '.keyword-container',
    phoneInput: 'input[placeholder="请输入手机号"]',
    emailInput: 'input[placeholder="请输入邮箱"]',
    reasonInput: 'input[placeholder="请输入原因"]',
    keywordInput: 'input[placeholder="请输入关键字"]',
    typeSelect: '.type-select',
    addButton: 'button:has-text("新增")',
    deleteButton: 'button:has-text("删除")',
    editButton: 'button:has-text("编辑")',
    batchDeleteButton: 'button:has-text("批量删除")',
    batchImportButton: 'button:has-text("批量导入")',
    batchDataTextarea: 'textarea[placeholder="请输入批量数据"]',
    importButton: 'button:has-text("导入")',
    exportButton: 'button:has-text("导出")'
  },

  // 系统设置页面选择器
  system: {
    passwordChangeContainer: '.password-change-container',
    oldPasswordInput: 'input[placeholder="请输入原密码"]',
    newPasswordInput: 'input[placeholder="请输入新密码"]',
    confirmPasswordInput: 'input[placeholder="请确认新密码"]',
    changePasswordButton: 'button:has-text("修改密码")',
    logContainer: '.log-container',
    logTable: '.log-table',
    logLevelSelect: '.log-level-select',
    logDatePicker: '.log-date-picker'
  },

  // 系统日志页面选择器
  systemLog: {
    container: '.system-log-container',
    logTable: '.log-table',
    dateRangePicker: '.date-range-picker',
    logLevelFilter: '.log-level-filter',
    searchInput: 'input[placeholder="请输入搜索关键字"]',
    searchButton: 'button:has-text("搜索")',
    queryButton: 'button:has-text("查询")',
    refreshButton: 'button:has-text("刷新")',
    exportButton: 'button:has-text("导出")',
    clearButton: 'button:has-text("清空")'
  },

  // 权限管理页面选择器
  permission: {
    container: '.permission-container',
    roleList: '.role-list',
    permissionTree: '.permission-tree',
    addRoleButton: 'button:has-text("新增角色")',
    saveButton: 'button:has-text("保存权限")',
    roleNameInput: 'input[placeholder="请输入角色名称"]',
    roleCodeInput: 'input[placeholder="请输入角色编码"]',
    roleDescInput: 'textarea[placeholder="请输入角色描述"]'
  },

  // 关键字过滤页面选择器
  keywordFilter: {
    container: '.keyword-filter-container',
    keywordInput: 'input[placeholder="请输入关键字"]',
    descriptionInput: 'input[placeholder="请输入描述"]',
    typeSelect: '.keyword-type-select',
    actionSelect: '.keyword-action-select',
    replacementInput: 'input[placeholder="请输入替换文本"]',
    filterTypeSelect: '.filter-type-select'
  },

  // 休眠账户通知页面选择器
  dormantAccount: {
    container: '.dormant-account-container',
    accountList: '.account-list',
    filterForm: '.filter-form',
    sendNotificationButton: 'button:has-text("发送通知")',
    batchSendButton: 'button:has-text("批量发送")',
    templateSelect: '.template-select',
    selectAllCheckbox: '.select-all-checkbox',
    dormantDaysInput: 'input[placeholder="请输入休眠天数"]',
    accountTypeSelect: '.account-type-select',
    minBalanceInput: 'input[placeholder="请输入最低余额"]',
    queryButton: 'button:has-text("查询")',
    previewButton: 'button:has-text("预览")',
    previewContent: '.preview-content'
  },

  // ESB接口管理页面选择器
  esbInterface: {
    container: '.esb-interface-container',
    interfaceList: '.interface-list',
    testConnectionButton: 'button:has-text("测试连接")',
    configForm: '.config-form',
    nameInput: 'input[placeholder="请输入接口名称"]',
    serviceCodeInput: 'input[placeholder="请输入服务编码"]',
    endpointInput: 'input[placeholder="请输入接口端点"]',
    methodSelect: '.method-select',
    timeoutInput: 'input[placeholder="请输入超时时间"]',
    descriptionInput: 'input[placeholder="请输入描述"]',
    saveButton: 'button:has-text("保存")',
    requestParamsTextarea: 'textarea[placeholder="请输入请求参数"]',
    responseMappingTextarea: 'textarea[placeholder="请输入响应映射"]',
    logContainer: '.log-container',
    logTable: '.log-table',
    dateFilter: '.date-filter',
    interfaceFilter: '.interface-filter'
  },

  // 模板类型管理页面选择器
  templateType: {
    container: '.template-type-container',
    typeList: '.type-list',
    searchForm: '.search-form',
    typeTree: '.type-tree',
    codeInput: 'input[placeholder="请输入类型编码"]',
    nameInput: 'input[placeholder="请输入类型名称"]',
    descriptionInput: 'input[placeholder="请输入描述"]',
    sortOrderInput: 'input[placeholder="请输入排序"]',
    statusSelect: '.status-select',
    parentSelect: '.parent-select',
    saveButton: 'button:has-text("保存")',
    sortByNameButton: 'button:has-text("按名称排序")'
  },

  // 模板发送统计页面选择器
  templateStatistics: {
    container: '.template-statistics-container',
    statisticsChart: '.statistics-chart',
    templateFilter: '.template-filter',
    statisticsTable: '.statistics-table',
    successRateTab: '.success-rate-tab',
    successRateTable: '.success-rate-table',
    successRateChart: '.success-rate-chart',
    typeStatisticsTab: '.type-statistics-tab',
    typeFilter: '.type-filter',
    typeStatisticsTable: '.type-statistics-table',
    typeDistributionChart: '.type-distribution-chart',
    usageChart: '.usage-chart'
  },

  // 角色管理页面选择器
  role: {
    container: '.role-container',
    table: '.role-table',
    searchForm: '.search-form',
    permissionPanel: '.permission-panel',
    codeInput: 'input[placeholder="请输入角色编码"]',
    nameInput: 'input[placeholder="请输入角色名称"]',
    descriptionInput: 'input[placeholder="请输入角色描述"]',
    statusSelect: '.status-select',
    saveButton: 'button:has-text("保存")',
    savePermissionButton: 'button:has-text("保存权限")'
  },

  // API接口页面选择器
  api: {
    container: '.api-container',
    apiList: '.api-list',
    searchBox: 'input[placeholder="搜索API接口"]',
    searchButton: 'button:has-text("搜索")',
    apiDetail: '.api-detail',
    tryItButton: 'button:has-text("试用")',
    methodInfo: '.method-info',
    parametersInfo: '.parameters-info',
    responseInfo: '.response-info',
    exampleCode: '.example-code',
    parameterInput: 'input[data-parameter="{name}"]',
    executeButton: 'button:has-text("执行")',
    responseResult: '.response-result',
    keyContainer: '.key-container',
    generateKeyButton: 'button:has-text("生成密钥")',
    keyNameInput: 'input[placeholder="请输入密钥名称"]',
    keyDescriptionInput: 'input[placeholder="请输入密钥描述"]',
    confirmGenerateButton: 'button:has-text("确认生成")',
    generatedKey: '.generated-key'
  },

  // 系统配置页面选择器
  systemConfig: {
    container: '.system-config-container',
    configTabs: '.config-tabs',
    basicConfig: '.basic-config',
    notificationConfig: '.notification-config',
    securityConfig: '.security-config',
    basicConfigTab: '.basic-config-tab',
    notificationConfigTab: '.notification-config-tab',
    securityConfigTab: '.security-config-tab',
    systemNameInput: 'input[placeholder="请输入系统名称"]',
    systemDescInput: 'textarea[placeholder="请输入系统描述"]',
    timeoutInput: 'input[placeholder="请输入超时时间"]',
    smsRateLimitInput: 'input[placeholder="短信频率限制"]',
    smsDailyLimitInput: 'input[placeholder="短信日限制"]',
    emailRateLimitInput: 'input[placeholder="邮件频率限制"]',
    emailDailyLimitInput: 'input[placeholder="邮件日限制"]',
    enableSmsCheckbox: '.enable-sms-checkbox',
    enableEmailCheckbox: '.enable-email-checkbox',
    passwordMinLengthInput: 'input[placeholder="密码最小长度"]',
    passwordMaxLengthInput: 'input[placeholder="密码最大长度"]',
    loginMaxAttemptsInput: 'input[placeholder="最大登录尝试次数"]',
    lockoutDurationInput: 'input[placeholder="锁定时长"]',
    sessionTimeoutInput: 'input[placeholder="会话超时时间"]',
    enablePasswordComplexityCheckbox: '.enable-password-complexity-checkbox',
    enableTwoFactorCheckbox: '.enable-two-factor-checkbox',
    saveButton: 'button:has-text("保存配置")',
    backupButton: 'button:has-text("备份配置")',
    restoreButton: 'button:has-text("恢复配置")'
  },

  // 数据备份页面选择器
  dataBackup: {
    container: '.data-backup-container',
    backupList: '.backup-list',
    createBackupButton: 'button:has-text("创建备份")',
    backupConfig: '.backup-config',
    scheduleConfig: '.schedule-config',
    storageConfig: '.storage-config',
    scheduleConfigTab: '.schedule-config-tab',
    backupNameInput: 'input[placeholder="请输入备份名称"]',
    backupDescriptionInput: 'textarea[placeholder="请输入备份描述"]',
    backupTypeSelect: '.backup-type-select',
    startBackupButton: 'button:has-text("开始备份")',
    backupProgress: '.backup-progress',
    enableScheduleCheckbox: '.enable-schedule-checkbox',
    scheduleFrequencySelect: '.schedule-frequency-select',
    scheduleTimeInput: 'input[type="time"]',
    retentionDaysInput: 'input[placeholder="保留天数"]',
    scheduleBackupTypeSelect: '.schedule-backup-type-select',
    saveScheduleButton: 'button:has-text("保存调度")',
    restoreModeSelect: '.restore-mode-select',
    confirmRestoreButton: 'button:has-text("确认恢复")',
    restoreProgress: '.restore-progress'
  },

  // 消息队列页面选择器
  messageQueue: {
    container: '.message-queue-container',
    queueList: '.queue-list',
    queueStats: '.queue-stats',
    messageList: '.message-list',
    controlPanel: '.control-panel',
    refreshButton: 'button:has-text("刷新")',
    retryButton: 'button:has-text("重试")',
    configButton: 'button:has-text("配置")',
    maxRetriesInput: 'input[placeholder="最大重试次数"]',
    retryIntervalInput: 'input[placeholder="重试间隔"]',
    maxQueueSizeInput: 'input[placeholder="最大队列大小"]',
    consumerCountInput: 'input[placeholder="消费者数量"]',
    batchSizeInput: 'input[placeholder="批处理大小"]',
    enableDeadLetterCheckbox: '.enable-dead-letter-checkbox',
    saveConfigButton: 'button:has-text("保存配置")',
    deadLetterQueueTab: '.dead-letter-queue-tab',
    deadLetterMessageList: '.dead-letter-message-list',
    requeueButton: 'button:has-text("重新入队")',
    deleteDeadLetterButton: 'button:has-text("删除")'
  },

  // 模板版本页面选择器
  templateVersion: {
    container: '.template-version-container',
    templateList: '.template-list',
    versionList: '.version-list',
    versionCompare: '.version-compare',
    createVersionButton: 'button:has-text("创建版本")',
    versionControlPanel: '.version-control-panel',
    versionNumberInput: 'input[placeholder="请输入版本号"]',
    versionDescriptionInput: 'textarea[placeholder="请输入版本描述"]',
    changeLogInput: 'textarea[placeholder="请输入更新日志"]',
    templateContentInput: 'textarea[placeholder="请输入模板内容"]',
    versionTypeSelect: '.version-type-select',
    saveVersionButton: 'button:has-text("保存版本")',
    compareButton: 'button:has-text("对比")',
    compareResult: '.compare-result',
    diffView: '.diff-view',
    rollbackButton: 'button:has-text("回滚")',
    rollbackReasonInput: 'textarea[placeholder="请输入回滚原因"]',
    confirmRollbackButton: 'button:has-text("确认回滚")',
    publishButton: 'button:has-text("发布")',
    releaseNotesInput: 'textarea[placeholder="请输入发布说明"]',
    effectiveDateInput: 'input[type="date"]',
    confirmPublishButton: 'button:has-text("确认发布")',
    unpublishButton: 'button:has-text("下线")'
  },

  // 实时监控页面选择器
  realTimeMonitor: {
    container: '.real-time-monitor-container',
    realTimeChart: '.real-time-chart',
    metricsCards: '.metrics-cards',
    alertPanel: '.alert-panel',
    systemStatus: '.system-status',
    refreshControl: '.refresh-control',
    refreshIntervalSelect: '.refresh-interval-select',
    autoRefreshCheckbox: '.auto-refresh-checkbox',
    manualRefreshButton: 'button:has-text("刷新")',
    performanceTab: '.performance-tab',
    cpuUsageMetric: '.cpu-usage-metric',
    memoryUsageMetric: '.memory-usage-metric',
    networkTrafficMetric: '.network-traffic-metric',
    dbConnectionsMetric: '.db-connections-metric',
    responseTimeMetric: '.response-time-metric',
    performanceChart: '.performance-chart',
    alertConfigTab: '.alert-config-tab',
    createAlertRuleButton: 'button:has-text("创建告警规则")',
    alertRuleNameInput: 'input[placeholder="请输入规则名称"]',
    alertRuleDescriptionInput: 'textarea[placeholder="请输入规则描述"]',
    alertMetricSelect: '.alert-metric-select',
    alertConditionSelect: '.alert-condition-select',
    alertThresholdInput: 'input[placeholder="请输入阈值"]',
    alertDurationInput: 'input[placeholder="请输入持续时间"]',
    saveAlertRuleButton: 'button:has-text("保存规则")',
    alertList: '.alert-list',
    acknowledgeAlertButton: 'button:has-text("确认告警")',
    acknowledgeNoteInput: 'textarea[placeholder="请输入确认备注"]',
    resolveAlertButton: 'button:has-text("解决告警")',
    resolutionNoteInput: 'textarea[placeholder="请输入解决方案"]'
  },

  // 渠道配置页面选择器
  channelConfig: {
    container: '.channel-config-container',
    channelList: '.channel-list',
    configPanel: '.config-panel',
    providerSelect: '.provider-select',
    configForm: '.config-form',
    testConnectionButton: 'button:has-text("测试连接")',
    smsChannelTab: '.sms-channel-tab',
    emailChannelTab: '.email-channel-tab',
    smsProviderSelect: '.sms-provider-select',
    emailProviderSelect: '.email-provider-select',
    accessKeyIdInput: 'input[placeholder="请输入AccessKeyId"]',
    accessKeySecretInput: 'input[placeholder="请输入AccessKeySecret"]',
    signNameInput: 'input[placeholder="请输入签名"]',
    templateCodeInput: 'input[placeholder="请输入模板代码"]',
    endpointInput: 'input[placeholder="请输入服务端点"]',
    rateLimitInput: 'input[placeholder="请输入频率限制"]',
    dailyLimitInput: 'input[placeholder="请输入日限制"]',
    smtpHostInput: 'input[placeholder="请输入SMTP服务器"]',
    smtpPortInput: 'input[placeholder="请输入端口"]',
    smtpUsernameInput: 'input[placeholder="请输入用户名"]',
    smtpPasswordInput: 'input[placeholder="请输入密码"]',
    fromNameInput: 'input[placeholder="请输入发件人名称"]',
    encryptionSelect: '.encryption-select',
    emailRateLimitInput: 'input[placeholder="邮件频率限制"]',
    emailDailyLimitInput: 'input[placeholder="邮件日限制"]',
    testSmtpConnectionButton: 'button:has-text("测试SMTP连接")',
    saveConfigButton: 'button:has-text("保存配置")',
    saveEmailConfigButton: 'button:has-text("保存邮件配置")',
    priorityConfigTab: '.priority-config-tab',
    enableFailoverCheckbox: '.enable-failover-checkbox',
    savePriorityButton: 'button:has-text("保存优先级")',
    healthCheckTab: '.health-check-tab',
    channelStatusTable: '.channel-status-table',
    checkAllChannelsButton: 'button:has-text("检查所有渠道")',
    healthCheckResultTable: '.health-check-result-table',
    viewDetailReportButton: 'button:has-text("查看详细报告")'
  },

  // 高级搜索页面选择器
  advancedSearch: {
    container: '.advanced-search-container',
    searchForm: '.search-form',
    filterPanel: '.filter-panel',
    resultTable: '.result-table',
    exportButton: 'button:has-text("导出")',
    savedSearches: '.saved-searches',
    startDateInput: 'input[name="startDate"]',
    endDateInput: 'input[name="endDate"]',
    messageTypeSelect: '.message-type-select',
    statusSelect: '.status-select',
    phoneInput: 'input[placeholder="请输入手机号"]',
    templateSelect: '.template-select',
    channelSelect: '.channel-select',
    searchButton: 'button:has-text("搜索")',
    resultCount: '.result-count',
    exportFormatSelect: '.export-format-select',
    confirmExportButton: 'button:has-text("确认导出")',
    saveSearchButton: 'button:has-text("保存搜索")',
    searchNameInput: 'input[placeholder="请输入搜索名称"]',
    searchDescriptionInput: 'textarea[placeholder="请输入搜索描述"]',
    publicSearchCheckbox: '.public-search-checkbox',
    confirmSaveButton: 'button:has-text("确认保存")',
    savedSearchList: '.saved-search-list',
    quickTemplateList: '.quick-template-list'
  },

  // 安全审计页面选择器
  securityAudit: {
    container: '.security-audit-container',
    auditLogTable: '.audit-log-table',
    filterPanel: '.filter-panel',
    securityDashboard: '.security-dashboard',
    riskAnalysis: '.risk-analysis',
    exportAuditButton: 'button:has-text("导出审计")',
    loginAuditTab: '.login-audit-tab',
    operationAuditTab: '.operation-audit-tab',
    riskAnalysisTab: '.risk-analysis-tab',
    complianceCheckTab: '.compliance-check-tab',
    startDateInput: 'input[name="startDate"]',
    endDateInput: 'input[name="endDate"]',
    loginStatusSelect: '.login-status-select',
    usernameSearchInput: 'input[placeholder="请输入用户名"]',
    searchAuditButton: 'button:has-text("搜索审计")',
    loginAuditTable: '.login-audit-table',
    operationTypeSelect: '.operation-type-select',
    moduleSelect: '.module-select',
    operationStartDateInput: 'input[name="operationStartDate"]',
    searchOperationButton: 'button:has-text("搜索操作")',
    operationAuditTable: '.operation-audit-table',
    exportOperationLogButton: 'button:has-text("导出操作日志")',
    riskOverview: '.risk-overview',
    highRiskEventsTable: '.high-risk-events-table',
    abnormalLoginAnalysisButton: 'button:has-text("异常登录分析")',
    privilegeAbuseDetectionButton: 'button:has-text("权限滥用检测")',
    generateRiskReportButton: 'button:has-text("生成风险报告")',
    runComplianceCheckButton: 'button:has-text("执行合规检查")',
    complianceResultTable: '.compliance-result-table',
    nonComplianceTable: '.non-compliance-table',
    generateComplianceReportButton: 'button:has-text("生成合规报告")'
  },

  // 国际化支持页面选择器
  i18nSupport: {
    container: '.i18n-support-container',
    languageTabs: '.language-tabs',
    templateList: '.template-list',
    languageSelector: '.language-selector',
    translationPanel: '.translation-panel',
    addLanguageButton: 'button:has-text("添加语言")',
    newLanguageSelect: '.new-language-select',
    confirmAddLanguageButton: 'button:has-text("确认添加")',
    chineseTab: '.chinese-tab',
    englishTab: '.english-tab',
    japaneseTab: '.japanese-tab',
    titleInput: 'input[placeholder="请输入标题"]',
    contentInput: 'textarea[placeholder="请输入内容"]',
    subjectInput: 'input[placeholder="请输入主题"]',
    saveTranslationButton: 'button:has-text("保存翻译")',
    previewButton: 'button:has-text("预览")',
    previewLanguageSelect: '.preview-language-select',
    autoTranslateButton: 'button:has-text("自动翻译")',
    translationServiceSelect: '.translation-service-select',
    startAutoTranslateButton: 'button:has-text("开始翻译")',
    translationResultTable: '.translation-result-table',
    translationQualityScore: '.translation-quality-score',
    qualityManagementTab: '.quality-management-tab',
    qualityReportTable: '.quality-report-table',
    reviewCommentInput: 'textarea[placeholder="请输入审核意见"]',
    qualityScoreSelect: '.quality-score-select',
    reviewStatusSelect: '.review-status-select',
    submitReviewButton: 'button:has-text("提交审核")',
    compareTranslationsButton: 'button:has-text("对比翻译")',
    compareLanguage1Select: '.compare-language1-select',
    compareLanguage2Select: '.compare-language2-select',
    executeCompareButton: 'button:has-text("执行对比")',
    generateQualityReportButton: 'button:has-text("生成质量报告")'
  },

  // 通用组件选择器
  common: {
    modal: '.el-dialog',
    modalTitle: '.el-dialog__title',
    modalBody: '.el-dialog__body',
    modalFooter: '.el-dialog__footer',
    confirmButton: 'button:has-text("确定")',
    cancelButton: 'button:has-text("取消")',
    closeButton: '.el-dialog__close',
    loading: '.el-loading-mask',
    message: '.el-message',
    notification: '.el-notification',
    table: '.el-table',
    tableRow: '.el-table__row',
    tableCell: '.el-table__cell',
    pagination: '.el-pagination',
    form: '.el-form',
    formItem: '.el-form-item',
    input: '.el-input__inner',
    select: '.el-select',
    selectOption: '.el-select-dropdown__item',
    datePicker: '.el-date-picker',
    upload: '.el-upload',
    tree: '.el-tree',
    tabs: '.el-tabs',
    tabPane: '.el-tab-pane',
    steps: '.el-steps',
    progress: '.el-progress',
    empty: '.el-empty'
  }
};
