
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告汇总</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card.passed { border-left-color: #28a745; }
        .summary-card.failed { border-left-color: #dc3545; }
        .summary-card.skipped { border-left-color: #ffc107; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .passed .number { color: #28a745; }
        .failed .number { color: #dc3545; }
        .skipped .number { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-item { background: #fff; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-item.passed { border-left: 4px solid #28a745; }
        .test-item.failed { border-left: 4px solid #dc3545; }
        .test-item.skipped { border-left: 4px solid #ffc107; }
        .test-title { font-weight: bold; margin-bottom: 5px; }
        .test-status { display: inline-block; padding: 6px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .detail-card { transition: transform 0.2s, box-shadow 0.2s; }
        .detail-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .screenshot-item { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .screenshot-item img { max-width: 100%; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 测试报告汇总</h1>
            <p>生成时间: 2025/7/29 09:41:41</p>
            <p>测试持续时间: 31秒</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">9</div>
            </div>
            <div class="summary-card passed">
                <h3>通过</h3>
                <div class="number">9</div>
            </div>
            <div class="summary-card failed">
                <h3>失败</h3>
                <div class="number">0</div>
            </div>
            <div class="summary-card skipped">
                <h3>跳过</h3>
                <div class="number">0</div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细测试报告</h2>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T001 - 登录页面加载测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 1741ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证登录页面能够正常加载并显示所有必要的UI元素</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保用户能够看到完整的登录界面，为后续登录操作提供基础</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">访问登录页面URL: http://localhost:3000/login</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">页面正常加载，显示用户名输入框、密码输入框和登录按钮</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问登录页面</li><li style="margin-bottom: 4px;">2. 等待页面加载完成</li><li style="margin-bottom: 4px;">3. 验证登录容器存在</li><li style="margin-bottom: 4px;">4. 验证用户名输入框可见</li><li style="margin-bottom: 4px;">5. 验证密码输入框可见</li><li style="margin-bottom: 4px;">6. 验证登录按钮可见</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T001-login-initial.png" alt="登录页面初始状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:14</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">登录页面初始状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T001-login-initial.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 198KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T001-elements-verified.png" alt="页面元素验证完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:14</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">页面元素验证完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T001-elements-verified.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 197KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T002 - 用户名密码填写测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 1438ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证用户名和密码输入框的填写功能是否正常工作</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保用户能够正常输入登录凭据，验证表单输入功能</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">用户名: testuser, 密码: testpass</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">输入框能够正常接收和显示输入内容</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问登录页面</li><li style="margin-bottom: 4px;">2. 在用户名输入框中输入"testuser"</li><li style="margin-bottom: 4px;">3. 验证用户名输入成功</li><li style="margin-bottom: 4px;">4. 在密码输入框中输入"testpass"</li><li style="margin-bottom: 4px;">5. 验证密码输入成功</li><li style="margin-bottom: 4px;">6. 确认表单填写完整</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T002-username-filled.png" alt="用户名填写完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:15</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">用户名填写完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T002-username-filled.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 198KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T002-form-completed.png" alt="表单填写完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:15</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">表单填写完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T002-form-completed.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 196KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T003 - 登录提交测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 3290ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证登录提交功能，测试用户点击登录按钮后的系统响应</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保登录流程能够正常执行，验证系统对登录请求的处理</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">用户名: admin, 密码: Admin123!</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">系统处理登录请求，根据凭据有效性给出相应响应</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问登录页面</li><li style="margin-bottom: 4px;">2. 输入有效的用户名和密码</li><li style="margin-bottom: 4px;">3. 点击登录按钮</li><li style="margin-bottom: 4px;">4. 等待系统响应</li><li style="margin-bottom: 4px;">5. 验证登录结果（成功跳转或错误提示）</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T003-before-submit.png" alt="提交前状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:16</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">提交前状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T003-before-submit.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 196KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T003-after-submit.png" alt="提交后状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:19</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">提交后状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T003-after-submit.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 43KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T004 - 空用户名登录测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 1415ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证空用户名情况下的登录验证机制</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保系统能够正确处理用户名为空的异常情况，提供适当的错误提示</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">用户名: (空), 密码: testpass</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">系统显示用户名不能为空的错误提示或阻止提交</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问登录页面</li><li style="margin-bottom: 4px;">2. 保持用户名输入框为空</li><li style="margin-bottom: 4px;">3. 在密码输入框中输入密码</li><li style="margin-bottom: 4px;">4. 点击登录按钮</li><li style="margin-bottom: 4px;">5. 验证系统的错误处理机制</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T004-empty-username-state.png" alt="用户名为空状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:20</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">用户名为空状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T004-empty-username-state.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 197KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T004-validation-result.png" alt="验证结果" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:20</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">验证结果</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T004-validation-result.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 197KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T005 - 空密码登录测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 3449ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证空密码情况下的登录验证机制</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保系统能够正确处理密码为空的异常情况，提供适当的错误提示</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">用户名: testuser, 密码: (空)</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">系统显示密码不能为空的错误提示或阻止提交</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问登录页面</li><li style="margin-bottom: 4px;">2. 在用户名输入框中输入用户名</li><li style="margin-bottom: 4px;">3. 保持密码输入框为空</li><li style="margin-bottom: 4px;">4. 点击登录按钮</li><li style="margin-bottom: 4px;">5. 验证系统的错误处理机制</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T005-empty-password-state.png" alt="密码为空状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:23</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">密码为空状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T005-empty-password-state.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 197KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T005-validation-result.png" alt="验证结果" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:23</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">验证结果</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T005-validation-result.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 197KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T006 - 短信发送功能完整流程测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 7577ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证短信发送功能的完整业务流程，包括发送前后的数据变化</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保短信发送功能正常工作，验证发送量统计的准确性</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">手机号: 13636367233, 短信内容: 测试短信内容</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">短信发送成功，今日发送量增加1</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 登录系统</li><li style="margin-bottom: 4px;">2. 查看发送前的今日短信发送量</li><li style="margin-bottom: 4px;">3. 进入短信单发页面</li><li style="margin-bottom: 4px;">4. 填写手机号码和短信内容</li><li style="margin-bottom: 4px;">5. 点击发送短信</li><li style="margin-bottom: 4px;">6. 返回仪表板查看发送量变化</li><li style="margin-bottom: 4px;">7. 验证发送量是否正确增加</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">5张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T006-dashboard-before-sms.png" alt="发送短信前仪表板" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:25</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">发送短信前仪表板</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T006-dashboard-before-sms.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 35KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T006-sms-page-loaded.png" alt="短信页面加载完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:26</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">短信页面加载完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T006-sms-page-loaded.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 35KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T006-form-filled.png" alt="表单填写完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 3</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:26</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">表单填写完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T006-form-filled.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 33KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T006-after-send.png" alt="发送完成后状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 4</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:29</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">发送完成后状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T006-after-send.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 38KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T006-dashboard-after-sms.png" alt="发送短信后仪表板" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 5</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:31</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">发送短信后仪表板</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T006-dashboard-after-sms.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 41KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T007 - 短信页面元素验证</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 1913ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证短信发送页面的UI元素完整性和可用性</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保短信发送页面的所有必要元素都正确显示和可用</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">访问短信单发页面</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">页面显示手机号输入框、短信内容输入框和发送按钮</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 登录系统</li><li style="margin-bottom: 4px;">2. 导航到短信单发页面</li><li style="margin-bottom: 4px;">3. 验证手机号输入框存在且可见</li><li style="margin-bottom: 4px;">4. 验证短信内容输入框存在且可见</li><li style="margin-bottom: 4px;">5. 验证发送短信按钮存在且可见</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T007-page-elements.png" alt="页面元素展示" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:33</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">页面元素展示</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T007-page-elements.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 35KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T007-elements-verified.png" alt="页面元素验证完成" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:33</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">页面元素验证完成</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T007-elements-verified.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 35KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T008 - 无效手机号码测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 3812ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证系统对无效手机号码的验证和错误处理机制</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保系统能够识别和拒绝无效的手机号码，保护系统安全</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">手机号: 123 (无效), 短信内容: 测试短信</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">系统显示手机号格式错误的提示信息</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 登录系统</li><li style="margin-bottom: 4px;">2. 进入短信单发页面</li><li style="margin-bottom: 4px;">3. 输入无效的手机号码"123"</li><li style="margin-bottom: 4px;">4. 输入短信内容</li><li style="margin-bottom: 4px;">5. 点击发送按钮</li><li style="margin-bottom: 4px;">6. 验证系统的错误提示</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T008-invalid-phone-filled.png" alt="无效手机号填写" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:35</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">无效手机号填写</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T008-invalid-phone-filled.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 33KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T008-validation-result.png" alt="验证结果" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:37</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">验证结果</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T008-validation-result.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 36KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
                <div class="test-item passed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T009 - 空短信内容测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-passed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">PASSED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 4018ms</span>
                            </div>
                        </div>
                        
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证系统对空短信内容的验证和错误处理机制</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保系统不允许发送空内容的短信，维护短信质量</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">手机号: 13636367233, 短信内容: (空)</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">系统显示短信内容不能为空的提示信息</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 登录系统</li><li style="margin-bottom: 4px;">2. 进入短信单发页面</li><li style="margin-bottom: 4px;">3. 输入有效的手机号码</li><li style="margin-bottom: 4px;">4. 保持短信内容为空</li><li style="margin-bottom: 4px;">5. 点击发送按钮</li><li style="margin-bottom: 4px;">6. 验证系统的错误提示</li>
                            </ol>
                        </div>

                        <div class="execution-result" style="background: #d4edda; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                            <h4 style="margin: 0 0 8px 0; color: #155724; font-size: 0.9em;">
                                ✅ 执行结果 - 通过
                            </h4>
                            <p style="margin: 0; color: #155724; font-size: 0.85em; line-height: 1.4;">
                                测试执行成功，所有验证点均通过，功能正常工作。
                            </p>
                        </div>
                    </div>


                    
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T009-empty-content-state.png" alt="短信内容为空状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:39</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">短信内容为空状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T009-empty-content-state.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 35KB</p>
                                        </div>
                                    </div>
                                
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T009-validation-result.png" alt="验证结果" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 09:41:41</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">验证结果</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T009-validation-result.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 37KB</p>
                                        </div>
                                    </div>
                                
                            </div>
                        </div>
                    
                </div>
            
        </div>

        <div class="section">
            <h2>📸 所有操作截图汇总 (按时间排序)</h2>
            <p style="color: #666; margin-bottom: 15px;">以下是本次测试生成的所有截图，按创建时间排序。详细截图已在上方各测试用例中展示。</p>
            <div class="screenshot-summary" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 12px;">
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T001-login-initial.png" alt="T001-login-initial.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#1</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">登录页面初始状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:14</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">198KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T001-elements-verified.png" alt="T001-elements-verified.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#2</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">页面元素验证完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:14</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">197KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T002-username-filled.png" alt="T002-username-filled.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#3</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">用户名填写完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:15</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">198KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T002-form-completed.png" alt="T002-form-completed.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#4</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">表单填写完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:15</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">196KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T003-before-submit.png" alt="T003-before-submit.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#5</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">提交前状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:16</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">196KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T003-after-submit.png" alt="T003-after-submit.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#6</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">提交后状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:19</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">43KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T004-empty-username-state.png" alt="T004-empty-username-state.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#7</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">用户名为空状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:20</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">197KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T004-validation-result.png" alt="T004-validation-result.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#8</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">验证结果</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:20</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">197KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T005-empty-password-state.png" alt="T005-empty-password-state.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#9</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">密码为空状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:23</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">197KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T005-validation-result.png" alt="T005-validation-result.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#10</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">验证结果</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:23</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">197KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T006-dashboard-before-sms.png" alt="T006-dashboard-before-sms.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#11</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">发送短信前仪表板</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:25</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">35KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T006-sms-page-loaded.png" alt="T006-sms-page-loaded.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#12</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">短信页面加载完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:26</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">35KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T006-form-filled.png" alt="T006-form-filled.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#13</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">表单填写完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:26</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">33KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T006-after-send.png" alt="T006-after-send.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#14</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">发送完成后状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:29</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">38KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T006-dashboard-after-sms.png" alt="T006-dashboard-after-sms.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#15</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">发送短信后仪表板</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:31</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">41KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T007-page-elements.png" alt="T007-page-elements.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#16</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">页面元素展示</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:33</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">35KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T007-elements-verified.png" alt="T007-elements-verified.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#17</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">页面元素验证完成</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:33</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">35KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T008-invalid-phone-filled.png" alt="T008-invalid-phone-filled.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#18</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">无效手机号填写</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:35</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">33KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T008-validation-result.png" alt="T008-validation-result.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#19</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">验证结果</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:37</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">36KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T009-empty-content-state.png" alt="T009-empty-content-state.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#20</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">短信内容为空状态</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:39</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">35KB</p>
                    </div>
                
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="screenshots/T009-validation-result.png" alt="T009-validation-result.png" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#21</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">验证结果</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ 2025/7/29 09:41:41</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">37KB</p>
                    </div>
                
            </div>
        </div>
    </div>
</body>
</html>