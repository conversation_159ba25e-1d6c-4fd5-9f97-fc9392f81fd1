<template>
  <div class="message-statistics-container">
    <!-- 查询表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" :rules="searchRules" ref="searchFormRef" inline>
        <el-form-item label="起始日期" prop="startDate">
          <el-date-picker
            v-model="searchForm.startDate"
            type="datetime"
            placeholder="请选择起始日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="searchForm.endDate"
            type="datetime"
            placeholder="请选择结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="手机号/邮箱">
          <el-input v-model="searchForm.recipient" placeholder="请输入手机号或邮箱" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <h3>{{ pageTitle }}</h3>
        <el-button v-if="currentStatus === 'FAILED'" type="danger" :disabled="selectedRows.length === 0" @click="handleBatchResend">
          重发
        </el-button>
      </div>
      
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" @row-click="handleRowClick">
        <el-table-column v-if="currentStatus === 'FAILED'" type="selection" width="55" />
        <el-table-column prop="templateName" label="模板名称" width="150" />
        <el-table-column prop="messageContent" label="发送内容" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="clickable-content" @click.stop="handleContentClick(row)">
              {{ row.messageContent }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="recipient" label="发送地址" width="180" />
        <el-table-column prop="messageType" label="消息类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.messageType === 1 ? 'primary' : 'success'">
              {{ row.messageType === 1 ? '邮件' : '短信' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="currentStatus !== 'FAILED'" prop="uplinkContent" label="上行内容" width="120" show-overflow-tooltip />
        <el-table-column prop="sendTime" label="发送时间" width="180" />
        <el-table-column prop="completeTime" label="结束时间" width="180" />
        <el-table-column v-if="currentStatus === 'FAILED'" prop="errorMessage" label="失败原因" show-overflow-tooltip />
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <div class="table-footer">
        <el-button @click="handleReturn">返回</el-button>
      </div>
    </el-card>

    <!-- 消息详情对话框 -->
    <el-dialog v-model="detailVisible" title="消息详情" width="800px">
      <div v-if="currentDetail" class="detail-content">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="消息ID">{{ currentDetail.messageId }}</el-descriptions-item>
          <el-descriptions-item label="模板名称">{{ currentDetail.templateName }}</el-descriptions-item>
          <el-descriptions-item label="消息类型">
            <el-tag :type="currentDetail.messageType === 1 ? 'primary' : 'success'">
              {{ currentDetail.messageType === 1 ? '邮件' : '短信' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送状态">
            <el-tag :type="getStatusType(currentDetail.sendStatus)">
              {{ getStatusText(currentDetail.sendStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="发送地址">{{ currentDetail.recipient }}</el-descriptions-item>
          <el-descriptions-item label="发送时间">{{ currentDetail.sendTime }}</el-descriptions-item>
          <el-descriptions-item v-if="currentDetail.completeTime" label="结束时间">{{ currentDetail.completeTime }}</el-descriptions-item>
          <el-descriptions-item v-if="currentDetail.uplinkContent" label="上行内容">{{ currentDetail.uplinkContent }}</el-descriptions-item>
          <el-descriptions-item v-if="currentDetail.errorMessage" label="失败原因" span="2">{{ currentDetail.errorMessage }}</el-descriptions-item>
          <el-descriptions-item label="消息内容" span="2">
            <div class="message-content">{{ currentDetail.messageContent }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button v-if="currentDetail && currentDetail.sendStatus === 'FAILED'" type="danger" @click="handleSingleResend">
          重发
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMessageRecords, getMessageRecordById, resendMessage, batchResendMessages } from '@/api/statistics'

const route = useRoute()
const loading = ref(false)
const detailVisible = ref(false)
const searchFormRef = ref()
const selectedRows = ref([])
const currentDetail = ref(null)

const currentStatus = computed(() => route.meta?.status || 'SUCCESS')
const pageTitle = computed(() => {
  const titleMap = {
    'SUCCESS': '发送成功',
    'SENDING': '正在发送',
    'FAILED': '发送失败'
  }
  return titleMap[currentStatus.value] || '消息统计'
})

const searchForm = reactive({
  startDate: '',
  endDate: '',
  recipient: ''
})

const searchRules = {
  startDate: [{ required: true, message: '请选择起始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }]
}

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const tableData = ref([])

const getStatusText = (status) => {
  const statusMap = {
    'SUCCESS': '发送成功',
    'SENDING': '正在发送',
    'FAILED': '发送失败',
    'PENDING': '等待发送'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    'SUCCESS': 'success',
    'SENDING': 'warning',
    'FAILED': 'danger',
    'PENDING': 'info'
  }
  return typeMap[status] || 'info'
}

const getSendStatusValue = (status) => {
  const statusMap = {
    'SUCCESS': 2,
    'SENDING': 1,
    'FAILED': 3,
    'PENDING': 0
  }
  return statusMap[status]
}

const loadData = async () => {
  try {
    await searchFormRef.value.validate()
  } catch {
    return
  }
  
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      sendStatus: getSendStatusValue(currentStatus.value),
      startTime: searchForm.startDate,
      endTime: searchForm.endDate,
      recipient: searchForm.recipient || undefined
    }
    const response = await getMessageRecords(params)
    if (response.data) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    startDate: '',
    endDate: '',
    recipient: ''
  })
  searchFormRef.value?.resetFields()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleRowClick = async (row) => {
  try {
    const response = await getMessageRecordById(row.messageId)
    if (response.data) {
      currentDetail.value = response.data
      detailVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取消息详情失败')
  }
}

const handleContentClick = (row) => {
  handleRowClick(row)
}

const handleSingleResend = async () => {
  if (!currentDetail.value) return
  
  try {
    await ElMessageBox.confirm('确定要重发该消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await resendMessage(currentDetail.value.messageId)
    if (response.data) {
      ElMessage.success('重发成功')
      detailVisible.value = false
      loadData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重发失败')
    }
  }
}

const handleBatchResend = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要重发的消息')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要重发选中的${selectedRows.value.length}条消息吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const messageIds = selectedRows.value.map(row => row.messageId)
    const response = await batchResendMessages(messageIds)
    if (response.data) {
      ElMessage.success('批量重发成功')
      loadData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量重发失败')
    }
  }
}

const handleReturn = () => {
  window.history.back()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

// 初始化默认日期（最近7天）
const initDefaultDates = () => {
  const now = new Date()
  const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
  
  searchForm.startDate = sevenDaysAgo.toISOString().slice(0, 19).replace('T', ' ')
  searchForm.endDate = now.toISOString().slice(0, 19).replace('T', ' ')
}

onMounted(() => {
  initDefaultDates()
  loadData()
})
</script>

<style scoped>
.message-statistics-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  margin: 0;
  color: #303133;
}

.clickable-content {
  cursor: pointer;
  color: #409eff;
}

.clickable-content:hover {
  text-decoration: underline;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.table-footer {
  margin-top: 15px;
  text-align: left;
}

.detail-content {
  padding: 10px 0;
}

.message-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>