import request from '@/utils/request'

// 查询角色列表
export function getRoleList(params) {
  return request({
    url: '/api/roles',
    method: 'get',
    params
  })
}

// 查询角色详情
export function getRoleById(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'get'
  })
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/api/roles',
    method: 'post',
    data
  })
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/api/roles/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'delete'
  })
}

// 分配权限
export function assignPermissions(id, permissionIds) {
  return request({
    url: `/api/roles/${id}/permissions`,
    method: 'post',
    data: permissionIds
  })
}

// 查询角色权限
export function getRolePermissions(id) {
  return request({
    url: `/api/roles/${id}/permissions`,
    method: 'get'
  })
}