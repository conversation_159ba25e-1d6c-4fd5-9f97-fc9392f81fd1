import request from '@/utils/request'

// 分页查询参数
export function getParameterPage(params) {
  return request({
    url: '/api/parameters/page',
    method: 'get',
    params
  })
}

// 获取所有启用的参数
export function getAllEnabledParameters() {
  return request({
    url: '/api/parameters/all',
    method: 'get'
  })
}

// 根据分类查询参数
export function getParametersByCategory(category) {
  return request({
    url: `/api/parameters/category/${category}`,
    method: 'get'
  })
}

// 根据ID获取参数
export function getParameterById(id) {
  return request({
    url: `/api/parameters/${id}`,
    method: 'get'
  })
}

// 根据参数代码获取参数
export function getParameterByCode(paramCode) {
  return request({
    url: `/api/parameters/code/${paramCode}`,
    method: 'get'
  })
}

// 创建参数
export function createParameter(data) {
  return request({
    url: '/api/parameters',
    method: 'post',
    data
  })
}

// 更新参数
export function updateParameter(id, data) {
  return request({
    url: `/api/parameters/${id}`,
    method: 'put',
    data
  })
}

// 删除参数
export function deleteParameter(id) {
  return request({
    url: `/api/parameters/${id}`,
    method: 'delete'
  })
}

// 批量删除参数
export function batchDeleteParameters(ids) {
  return request({
    url: '/api/parameters/batch',
    method: 'delete',
    data: ids
  })
}

// 更新参数状态
export function updateParameterStatus(id, status) {
  return request({
    url: `/api/parameters/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 搜索参数
export function searchParameters(paramName) {
  return request({
    url: '/api/parameters/search',
    method: 'get',
    params: { paramName }
  })
}

// 获取参数使用统计
export function getParameterUsageStats() {
  return request({
    url: '/api/parameters/usage-stats',
    method: 'get'
  })
}

// 批量创建参数
export function batchCreateParameters(data) {
  return request({
    url: '/api/parameters/batch',
    method: 'post',
    data
  })
}