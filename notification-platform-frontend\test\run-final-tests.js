#!/usr/bin/env node

/**
 * 最终测试执行脚本
 * 运行所有已实现的测试用例，生成完整的测试报告
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class FinalTestRunner {
  constructor() {
    this.testResults = [];
    this.startTime = Date.now();
  }

  /**
   * 运行单个测试模块
   */
  async runTestModule(moduleName, scriptPath) {
    console.log(`\n🧪 开始执行 ${moduleName} 测试...`);
    
    return new Promise((resolve) => {
      const testProcess = spawn('node', [scriptPath], {
        stdio: 'pipe',
        cwd: process.cwd()
      });

      let output = '';
      let errorOutput = '';

      testProcess.stdout.on('data', (data) => {
        output += data.toString();
        process.stdout.write(data);
      });

      testProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
        process.stderr.write(data);
      });

      testProcess.on('close', (code) => {
        const result = {
          module: moduleName,
          script: scriptPath,
          exitCode: code,
          success: code === 0,
          output: output,
          error: errorOutput,
          duration: Date.now()
        };

        this.testResults.push(result);
        
        if (code === 0) {
          console.log(`✅ ${moduleName} 测试完成`);
        } else {
          console.log(`❌ ${moduleName} 测试失败 (退出码: ${code})`);
        }
        
        resolve(result);
      });
    });
  }

  /**
   * 运行所有测试模块
   */
  async runAllTests() {
    console.log('🚀 开始执行通知平台前端完整测试套件');
    console.log('='.repeat(80));

    const testModules = [
      // 认证权限模块
      { name: '用户登录测试', script: 'test/auth/login-test.js' },
      { name: '用户管理测试', script: 'test/auth/user-management-test.js' },
      { name: '角色管理测试', script: 'test/auth/role-management-test.js' },
      { name: '渠道配置测试', script: 'test/auth/channel-config-test.js' },
      { name: 'API接口测试', script: 'test/api/api-interface-test.js' },
      
      // 消息发送模块
      { name: '短信单发测试', script: 'test/message/sms-single-test.js' },
      { name: '短信批量测试', script: 'test/message/sms-batch-test.js' },
      { name: '邮件单发测试', script: 'test/message/email-single-test.js' },
      { name: '邮件批量测试', script: 'test/message/email-batch-test.js' },
      { name: '营销邮件测试', script: 'test/message/email-marketing-test.js' },
      { name: '休眠账户通知测试', script: 'test/message/dormant-account-test.js' },
      { name: '数据备份测试', script: 'test/system/data-backup-test.js' },

      // 模板管理模块
      { name: '模板管理测试', script: 'test/template/template-manage-test.js' },
      { name: '模板参数测试', script: 'test/template/template-parameter-test.js' },
      { name: '模板类型测试', script: 'test/template/template-type-test.js' },
      { name: '模板版本管理测试', script: 'test/template/template-version-test.js' },
      { name: '国际化支持测试', script: 'test/template/i18n-support-test.js' },

      // 渠道管理模块
      { name: '接入渠道测试', script: 'test/channel/access-channel-test.js' },
      { name: '发送渠道测试', script: 'test/channel/send-channel-test.js' },
      { name: 'ESB接口测试', script: 'test/channel/esb-interface-test.js' },
      { name: '消息队列管理测试', script: 'test/channel/message-queue-test.js' },
      
      // 统计分析模块
      { name: '仪表板统计测试', script: 'test/statistics/dashboard-test.js' },
      { name: '发送详情统计测试', script: 'test/statistics/send-detail-test.js' },
      { name: '模板发送统计测试', script: 'test/statistics/template-statistics-test.js' },
      { name: '高级搜索测试', script: 'test/statistics/advanced-search-test.js' },
      { name: '实时监控测试', script: 'test/statistics/real-time-monitor-test.js' },
      
      // 安全管理模块
      { name: '黑名单管理测试', script: 'test/security/blacklist-test.js' },
      { name: '白名单管理测试', script: 'test/security/whitelist-test.js' },
      { name: '关键字过滤测试', script: 'test/security/keyword-filter-test.js' },
      { name: '安全审计测试', script: 'test/security/security-audit-test.js' },

      // 系统设置模块
      { name: '密码修改测试', script: 'test/system/password-change-test.js' },
      { name: '系统日志测试', script: 'test/system/system-log-test.js' },
      { name: '权限测试功能', script: 'test/system/permission-test.js' },
      { name: '系统配置管理测试', script: 'test/system/system-config-test.js' }
    ];

    // 检查测试文件是否存在
    const existingModules = testModules.filter(module => {
      const fullPath = path.join(process.cwd(), module.script);
      return fs.existsSync(fullPath);
    });

    const missingModules = testModules.filter(module => {
      const fullPath = path.join(process.cwd(), module.script);
      return !fs.existsSync(fullPath);
    });

    if (missingModules.length > 0) {
      console.log('\n⚠️  以下测试模块文件不存在:');
      missingModules.forEach(module => {
        console.log(`   - ${module.name}: ${module.script}`);
      });
    }

    console.log(`\n📊 将执行 ${existingModules.length} 个测试模块`);
    console.log('='.repeat(80));

    // 依次执行所有测试模块
    for (const module of existingModules) {
      await this.runTestModule(module.name, module.script);
      
      // 模块间稍作停顿
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // 生成最终报告
    this.generateFinalReport();
  }

  /**
   * 生成最终测试报告
   */
  generateFinalReport() {
    const endTime = Date.now();
    const totalDuration = endTime - this.startTime;
    
    const successfulTests = this.testResults.filter(r => r.success).length;
    const failedTests = this.testResults.filter(r => !r.success).length;
    const totalTests = this.testResults.length;

    console.log('\n' + '='.repeat(80));
    console.log('📊 通知平台前端测试套件 - 最终报告');
    console.log('='.repeat(80));
    console.log(`🕐 总执行时间: ${Math.round(totalDuration / 1000)}秒`);
    console.log(`📈 测试模块总数: ${totalTests}`);
    console.log(`✅ 成功模块: ${successfulTests}`);
    console.log(`❌ 失败模块: ${failedTests}`);
    console.log(`📊 成功率: ${totalTests > 0 ? Math.round((successfulTests / totalTests) * 100) : 0}%`);
    console.log('='.repeat(80));

    // 详细结果
    console.log('\n📋 详细测试结果:');
    this.testResults.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.module}`);
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error.substring(0, 100)}...`);
      }
    });

    // 生成JSON报告文件
    this.generateJSONReport();
    
    // 生成HTML报告文件
    this.generateHTMLReport();

    console.log('\n📄 报告文件已生成:');
    console.log('   - test-results/final-test-report.json');
    console.log('   - test-results/final-test-report.html');
    
    console.log('\n🎉 测试执行完成!');
  }

  /**
   * 生成JSON格式报告
   */
  generateJSONReport() {
    const reportDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalModules: this.testResults.length,
        successfulModules: this.testResults.filter(r => r.success).length,
        failedModules: this.testResults.filter(r => !r.success).length,
        totalDuration: Date.now() - this.startTime,
        successRate: this.testResults.length > 0 ? 
          Math.round((this.testResults.filter(r => r.success).length / this.testResults.length) * 100) : 0
      },
      results: this.testResults.map(result => ({
        module: result.module,
        script: result.script,
        success: result.success,
        exitCode: result.exitCode,
        hasError: !!result.error,
        duration: result.duration - this.startTime
      }))
    };

    const reportPath = path.join(reportDir, 'final-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  }

  /**
   * 生成HTML格式报告
   */
  generateHTMLReport() {
    const reportDir = path.join(process.cwd(), 'test-results');
    const successfulTests = this.testResults.filter(r => r.success).length;
    const failedTests = this.testResults.filter(r => !r.success).length;
    const totalTests = this.testResults.length;
    const successRate = totalTests > 0 ? Math.round((successfulTests / totalTests) * 100) : 0;

    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知平台前端测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .success { color: #28a745; }
        .danger { color: #dc3545; }
        .info { color: #17a2b8; }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .results-table th { background-color: #f8f9fa; font-weight: bold; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 通知平台前端测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试模块</h3>
                <div class="value info">${totalTests}</div>
            </div>
            <div class="summary-card">
                <h3>成功模块</h3>
                <div class="value success">${successfulTests}</div>
            </div>
            <div class="summary-card">
                <h3>失败模块</h3>
                <div class="value danger">${failedTests}</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value ${successRate >= 80 ? 'success' : successRate >= 60 ? 'info' : 'danger'}">${successRate}%</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${successRate}%"></div>
                </div>
            </div>
        </div>
        
        <h2>📋 详细测试结果</h2>
        <table class="results-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>测试模块</th>
                    <th>状态</th>
                    <th>脚本路径</th>
                    <th>退出码</th>
                </tr>
            </thead>
            <tbody>
                ${this.testResults.map((result, index) => `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${result.module}</td>
                        <td class="${result.success ? 'status-success' : 'status-failed'}">
                            ${result.success ? '✅ 成功' : '❌ 失败'}
                        </td>
                        <td><code>${result.script}</code></td>
                        <td>${result.exitCode}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>📊 测试执行耗时: ${Math.round((Date.now() - this.startTime) / 1000)}秒</p>
            <p>🔧 通知平台前端自动化测试套件 v1.0</p>
        </div>
    </div>
</body>
</html>`;

    const reportPath = path.join(reportDir, 'final-test-report.html');
    fs.writeFileSync(reportPath, html);
  }
}

// 主执行函数
async function main() {
  const runner = new FinalTestRunner();
  
  try {
    await runner.runAllTests();
    process.exit(0);
  } catch (error) {
    console.error('❌ 测试执行过程中发生错误:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = FinalTestRunner;
