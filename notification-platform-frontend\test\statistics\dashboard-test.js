/**
 * T181-T190: 仪表板统计展示测试
 * 基于需求文档中的统计分析功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class DashboardTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T181: 仪表板页面加载测试
   */
  async testT181_DashboardPageLoad() {
    const testId = 'T181';
    console.log(`\n🧪 执行测试 ${testId}: 仪表板页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到仪表板页面
      await this.testHelper.navigateTo('/dashboard');
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isSmsCardVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.smsCard);
      const isEmailCardVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.emailCard);
      const isTemplateCardVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.templateCard);
      const isUserCardVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.userCard);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '仪表板页面加载测试',
        testContent: '验证仪表板页面能够正常加载并显示所有统计卡片',
        testPurpose: '确保用户能够看到完整的系统统计信息',
        testInput: '访问仪表板页面URL: /dashboard',
        expectedOutput: '页面正常加载，显示短信、邮件、模板、用户等统计卡片',
        actualOutput: `短信卡片: ${isSmsCardVisible ? '✅显示' : '❌隐藏'}, 邮件卡片: ${isEmailCardVisible ? '✅显示' : '❌隐藏'}, 模板卡片: ${isTemplateCardVisible ? '✅显示' : '❌隐藏'}, 用户卡片: ${isUserCardVisible ? '✅显示' : '❌隐藏'}`,
        result: isSmsCardVisible && isEmailCardVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '仪表板页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T182: 统计数据显示测试
   */
  async testT182_StatisticsDataDisplay() {
    const testId = 'T182';
    console.log(`\n🧪 执行测试 ${testId}: 统计数据显示测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到仪表板页面
      await this.testHelper.navigateTo('/dashboard');
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取统计数据
      const stats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('统计数据获取完成');
      
      // 验证统计数据是否为数字
      const isSmsCountValid = typeof stats.smsCount === 'number' && stats.smsCount >= 0;
      const isEmailCountValid = typeof stats.emailCount === 'number' && stats.emailCount >= 0;
      
      const result = {
        testId: testId,
        testName: '统计数据显示测试',
        testContent: '验证仪表板能够正确显示各项统计数据',
        testPurpose: '确保统计数据的准确性和可读性',
        testInput: '读取仪表板上的统计数据',
        expectedOutput: '显示有效的数字统计数据',
        actualOutput: `短信发送量: ${stats.smsCount} (${isSmsCountValid ? '✅有效' : '❌无效'}), 邮件发送量: ${stats.emailCount} (${isEmailCountValid ? '✅有效' : '❌无效'})`,
        result: isSmsCountValid && isEmailCountValid ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '统计数据显示测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T183: 图表显示测试
   */
  async testT183_ChartDisplay() {
    const testId = 'T183';
    console.log(`\n🧪 执行测试 ${testId}: 图表显示测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到仪表板页面
      await this.testHelper.navigateTo('/dashboard');
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证图表容器是否存在
      const isChartContainerVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.chartContainer);
      
      if (isChartContainerVisible) {
        await this.screenshotHelper.takeChartScreenshot();
        
        // 等待图表加载
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeCustomScreenshot('图表加载完成');
        
        const result = {
          testId: testId,
          testName: '图表显示测试',
          testContent: '验证仪表板图表能够正常显示',
          testPurpose: '确保数据可视化功能正常工作',
          testInput: '查看仪表板图表区域',
          expectedOutput: '图表正常显示，展示统计数据的可视化',
          actualOutput: `图表容器: ${isChartContainerVisible ? '✅显示' : '❌隐藏'}`,
          result: isChartContainerVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } else {
        // 如果没有图表容器，标记为跳过
        const result = {
          testId: testId,
          testName: '图表显示测试',
          testContent: '验证仪表板图表能够正常显示',
          testPurpose: '确保数据可视化功能正常工作',
          testInput: '查找图表容器',
          expectedOutput: '找到图表并正常显示',
          actualOutput: '未找到图表容器',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到图表容器`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '图表显示测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T184: 统计表格显示测试
   */
  async testT184_StatisticsTableDisplay() {
    const testId = 'T184';
    console.log(`\n🧪 执行测试 ${testId}: 统计表格显示测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到仪表板页面
      await this.testHelper.navigateTo('/dashboard');
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证统计表格是否存在
      const isStatisticsTableVisible = await this.testHelper.verifyElementVisibility(selectors.dashboard.statisticsTable);
      
      if (isStatisticsTableVisible) {
        await this.screenshotHelper.takeTableScreenshot();
        
        // 获取表格数据
        const tableData = await this.testHelper.getTableData(selectors.dashboard.statisticsTable);
        
        const result = {
          testId: testId,
          testName: '统计表格显示测试',
          testContent: '验证仪表板统计表格能够正常显示数据',
          testPurpose: '确保详细统计数据的表格展示功能',
          testInput: '查看仪表板统计表格',
          expectedOutput: '表格正常显示，包含统计数据行',
          actualOutput: `统计表格: ${isStatisticsTableVisible ? '✅显示' : '❌隐藏'}, 数据行数: ${tableData.length}`,
          result: isStatisticsTableVisible && tableData.length >= 0 ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } else {
        // 如果没有统计表格，标记为跳过
        const result = {
          testId: testId,
          testName: '统计表格显示测试',
          testContent: '验证仪表板统计表格能够正常显示数据',
          testPurpose: '确保详细统计数据的表格展示功能',
          testInput: '查找统计表格',
          expectedOutput: '找到表格并正常显示数据',
          actualOutput: '未找到统计表格',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到统计表格`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '统计表格显示测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T185: 页面刷新数据更新测试
   */
  async testT185_DataRefreshTest() {
    const testId = 'T185';
    console.log(`\n🧪 执行测试 ${testId}: 页面刷新数据更新测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到仪表板页面
      await this.testHelper.navigateTo('/dashboard');
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 获取初始统计数据
      const initialStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('初始统计数据');
      
      // 刷新页面
      await this.testHelper.page.reload();
      await this.testHelper.waitForPageLoad(selectors.dashboard.container);
      await this.screenshotHelper.takeCustomScreenshot('页面刷新后');
      
      // 获取刷新后的统计数据
      const refreshedStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('刷新后统计数据');
      
      // 验证数据是否正常加载
      const isDataLoaded = typeof refreshedStats.smsCount === 'number' && typeof refreshedStats.emailCount === 'number';
      
      const result = {
        testId: testId,
        testName: '页面刷新数据更新测试',
        testContent: '验证页面刷新后统计数据能够正常重新加载',
        testPurpose: '确保数据刷新机制的可靠性',
        testInput: '刷新仪表板页面',
        expectedOutput: '页面刷新后统计数据正常显示',
        actualOutput: `刷新前: SMS=${initialStats.smsCount}, Email=${initialStats.emailCount}; 刷新后: SMS=${refreshedStats.smsCount}, Email=${refreshedStats.emailCount}`,
        result: isDataLoaded ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '页面刷新数据更新测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有仪表板测试
   */
  async runAllTests() {
    console.log('🚀 开始执行仪表板统计展示功能测试套件 (T181-T185)');
    
    const startTime = Date.now();
    
    await this.testT181_DashboardPageLoad();
    await this.testT182_StatisticsDataDisplay();
    await this.testT183_ChartDisplay();
    await this.testT184_StatisticsTableDisplay();
    await this.testT185_DataRefreshTest();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 仪表板统计展示功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const dashboardTest = new DashboardTest();
  dashboardTest.runAllTests().catch(console.error);
}

module.exports = DashboardTest;
