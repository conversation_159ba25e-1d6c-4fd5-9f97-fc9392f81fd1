<template>
  <div class="keyword-container">
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="敏感词">
          <el-input
            v-model="queryParams.keyword"
            placeholder="请输入敏感词"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="敏感词类型">
          <el-select v-model="queryParams.keywordType" placeholder="请选择类型" clearable style="width: 150px">
            <el-option label="敏感词" :value="1" />
            <el-option label="违禁词" :value="2" />
            <el-option label="广告词" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理方式">
          <el-select v-model="queryParams.actionType" placeholder="请选择处理方式" clearable style="width: 150px">
            <el-option label="拦截" :value="1" />
            <el-option label="替换" :value="2" />
            <el-option label="警告" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="toolbar-container">
      <el-button type="primary" @click="handleAdd">新增敏感词</el-button>
      <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchDelete">批量删除</el-button>
      <el-button type="warning" @click="handleImport">批量导入</el-button>
      <el-button type="info" @click="handleExport">导出</el-button>
      <el-button type="success" @click="refreshCache">刷新缓存</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="keywordList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="keyword" label="敏感词" width="150" />
      <el-table-column prop="keywordTypeName" label="敏感词类型" width="120" />
      <el-table-column prop="actionTypeName" label="处理方式" width="120" />
      <el-table-column prop="replacement" label="替换内容" width="150" show-overflow-tooltip />
      <el-table-column prop="statusName" label="状态" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
            {{ scope.row.statusName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createdBy" label="创建人" width="120" />
      <el-table-column prop="createdTime" label="创建时间" width="180" />
      <el-table-column prop="remark" label="备注" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            :type="scope.row.status === 1 ? 'warning' : 'success'" 
            size="small" 
            @click="handleStatusChange(scope.row)"
          >
            {{ scope.row.status === 1 ? '禁用' : '启用' }}
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <el-form
        ref="keywordFormRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="敏感词" prop="keyword">
          <el-input v-model="form.keyword" placeholder="请输入敏感词" maxlength="20" />
        </el-form-item>
        <el-form-item label="敏感词类型" prop="keywordType">
          <el-select v-model="form.keywordType" placeholder="请选择敏感词类型" style="width: 100%">
            <el-option label="敏感词" :value="1" />
            <el-option label="违禁词" :value="2" />
            <el-option label="广告词" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理方式" prop="actionType">
          <el-select v-model="form.actionType" placeholder="请选择处理方式" style="width: 100%" @change="handleActionTypeChange">
            <el-option label="拦截" :value="1" />
            <el-option label="替换" :value="2" />
            <el-option label="警告" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="替换内容" prop="replacement" v-if="form.actionType === 2">
          <el-input v-model="form.replacement" placeholder="请输入替换内容" maxlength="50" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" maxlength="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog title="批量导入敏感词" v-model="importDialogVisible" width="500px">
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx,.xls"
        :headers="uploadHeaders"
        :action="uploadUrl"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
      >
        <template #trigger>
          <el-button type="primary">选择文件</el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="updateSupport" />是否更新已经存在的敏感词数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitFileForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 内容检查对话框 -->
    <el-dialog title="内容敏感词检查" v-model="checkDialogVisible" width="700px">
      <el-form>
        <el-form-item label="检查内容">
          <el-input
            v-model="checkContent"
            type="textarea"
            :rows="4"
            placeholder="请输入要检查的内容"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleContentCheck">检查</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="checkResult">
        <el-divider content-position="left">检查结果</el-divider>
        <el-alert
          :title="checkResult.passed ? '内容检查通过' : '内容检查未通过'"
          :type="checkResult.passed ? 'success' : 'error'"
          :closable="false"
          style="margin-bottom: 15px"
        />
        
        <div v-if="checkResult.matchedKeywords && checkResult.matchedKeywords.length > 0">
          <h4>匹配的敏感词：</h4>
          <el-table :data="checkResult.matchedKeywords" size="small">
            <el-table-column prop="keyword" label="敏感词" width="120" />
            <el-table-column prop="keywordTypeName" label="类型" width="100" />
            <el-table-column prop="actionTypeName" label="处理方式" width="100" />
            <el-table-column prop="replacement" label="替换内容" />
          </el-table>
        </div>
        
        <div v-if="checkResult.warnings && checkResult.warnings.length > 0" style="margin-top: 15px">
          <h4>警告信息：</h4>
          <ul>
            <li v-for="warning in checkResult.warnings" :key="warning">{{ warning }}</li>
          </ul>
        </div>
        
        <div v-if="checkResult.processedContent !== checkContent" style="margin-top: 15px">
          <h4>处理后内容：</h4>
          <el-input
            v-model="checkResult.processedContent"
            type="textarea"
            :rows="4"
            readonly
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getKeywordPage, createKeyword, updateKeyword, deleteKeyword, batchDeleteKeyword, updateKeywordStatus, checkContent as checkContentApi, batchImportKeyword, refreshKeywordCache } from '@/api/security'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const keywordList = ref([])
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const importDialogVisible = ref(false)
const checkDialogVisible = ref(false)
const multipleSelection = ref([])
const updateSupport = ref(false)
const checkContent = ref('')
const checkResult = ref(null)

// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10,
  keyword: '',
  keywordType: null,
  actionType: null,
  status: null
})

// 表单数据
const form = reactive({
  id: null,
  keyword: '',
  keywordType: null,
  actionType: null,
  replacement: '',
  status: 1,
  remark: ''
})

// 表单验证规则
const rules = {
  keyword: [
    { required: true, message: '敏感词不能为空', trigger: 'blur' },
    { max: 20, message: '敏感词长度不能超过20个字符', trigger: 'blur' }
  ],
  keywordType: [
    { required: true, message: '请选择敏感词类型', trigger: 'change' }
  ],
  actionType: [
    { required: true, message: '请选择处理方式', trigger: 'change' }
  ],
  replacement: [
    { required: true, message: '替换内容不能为空', trigger: 'blur' },
    { max: 50, message: '替换内容长度不能超过50个字符', trigger: 'blur' }
  ]
}

// refs
const keywordFormRef = ref()
const uploadRef = ref()

// 上传相关
const uploadHeaders = ref({})
const uploadUrl = ref('/api/security/keyword/import')

// 生命周期
onMounted(() => {
  getList()
})

// 方法
const getList = async () => {
  loading.value = true
  try {
    const response = await getKeywordPage(queryParams)
    if (response.code === 200) {
      // 处理数据，添加显示名称
      const records = response.data.records.map(item => ({
        ...item,
        keywordTypeName: getKeywordTypeName(item.keywordType),
        actionTypeName: getActionTypeName(item.actionType),
        statusName: item.status === 1 ? '启用' : '禁用'
      }))
      keywordList.value = records
      total.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取关键字列表失败')
    }
  } catch (error) {
    console.error('获取关键字列表失败:', error)
    ElMessage.error('获取关键字列表失败')
  } finally {
    loading.value = false
  }
}

// 获取关键字类型名称
const getKeywordTypeName = (type) => {
  const typeMap = {
    1: '敏感词',
    2: '违禁词',
    3: '广告词'
  }
  return typeMap[type] || '未知'
}

// 获取处理方式名称
const getActionTypeName = (type) => {
  const typeMap = {
    1: '拦截',
    2: '替换',
    3: '警告'
  }
  return typeMap[type] || '未知'
}

const handleQuery = () => {
  queryParams.current = 1
  getList()
}

const resetQuery = () => {
  queryParams.keyword = ''
  queryParams.keywordType = null
  queryParams.actionType = null
  queryParams.status = null
  handleQuery()
}

const handleAdd = () => {
  resetForm()
  dialogTitle.value = '新增敏感词'
  dialogVisible.value = true
}

const handleEdit = (row) => {
  resetForm()
  Object.assign(form, row)
  dialogTitle.value = '编辑敏感词'
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个敏感词吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteKeyword(row.id)
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除关键字失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的敏感词吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = multipleSelection.value.map(item => item.id)
    await batchDeleteKeyword(ids)
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除关键字失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleStatusChange = async (row) => {
  const newStatus = row.status === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '启用' : '禁用'
  
  try {
    await ElMessageBox.confirm(`确定要${statusText}这个敏感词吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await updateKeywordStatus(row.id, newStatus)
    ElMessage.success(`${statusText}成功`)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新关键字状态失败:', error)
      ElMessage.error(`${statusText}失败`)
    }
  }
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const handleActionTypeChange = (value) => {
  if (value !== 2) {
    form.replacement = ''
  }
}

const submitForm = async () => {
  try {
    await keywordFormRef.value.validate()
    
    if (form.id) {
      await updateKeyword(form)
      ElMessage.success('更新成功')
    } else {
      await createKeyword(form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    getList()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败')
  }
}

const resetForm = () => {
  form.id = null
  form.keyword = ''
  form.keywordType = null
  form.actionType = null
  form.replacement = ''
  form.status = 1
  form.remark = ''
}

const cancel = () => {
  dialogVisible.value = false
}

const handleClose = () => {
  dialogVisible.value = false
}

const handleImport = () => {
  importDialogVisible.value = true
}

const handleExport = () => {
  // TODO: 实现导出功能
  ElMessage.info('导出功能开发中')
}

const refreshCache = async () => {
  try {
    await refreshKeywordCache()
    ElMessage.success('缓存刷新成功')
  } catch (error) {
    console.error('刷新缓存失败:', error)
    ElMessage.error('刷新缓存失败')
  }
}

const handleContentCheck = async () => {
  if (!checkContent.value.trim()) {
    ElMessage.warning('请输入要检查的内容')
    return
  }
  
  try {
    const response = await checkContentApi(checkContent.value)
    checkResult.value = response.data
  } catch (error) {
    console.error('内容检查失败:', error)
    ElMessage.error('内容检查失败')
  }
}

const submitFileForm = () => {
  uploadRef.value.submit()
}

const handleFileUploadProgress = () => {
  // 处理上传进度
}

const handleFileSuccess = (response) => {
  importDialogVisible.value = false
  ElMessage.success('导入成功')
  getList()
}

const importTemplate = () => {
  // TODO: 下载模板文件
  ElMessage.info('模板下载功能开发中')
}
</script>

<style scoped>
.keyword-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.toolbar-container {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>