/**
 * 详细测试报告生成器
 * 按照detailed-report样式生成测试报告
 */

const fs = require('fs');
const path = require('path');

class DetailedReportGenerator {
  constructor() {
    this.testResults = [];
    this.startTime = null;
    this.endTime = null;
  }

  /**
   * 添加测试结果
   */
  addTestResult(result) {
    // 确保结果包含所有必要字段
    const formattedResult = {
      testId: result.testId || 'Unknown',
      testName: result.testName || 'Unknown Test',
      testContent: result.testContent || '测试内容未定义',
      testPurpose: result.testPurpose || '测试目的未定义',
      testInput: result.testInput || '测试输入未定义',
      expectedOutput: result.expectedOutput || '预期输出未定义',
      actualOutput: result.actualOutput || '实际输出未定义',
      testSteps: result.testSteps || [],
      result: result.result || 'UNKNOWN',
      duration: result.duration || 0,
      screenshots: result.screenshots || [],
      error: result.error || null,
      timestamp: result.timestamp || new Date().toISOString()
    };
    
    this.testResults.push(formattedResult);
  }

  /**
   * 设置测试开始时间
   */
  setStartTime(time) {
    this.startTime = time;
  }

  /**
   * 设置测试结束时间
   */
  setEndTime(time) {
    this.endTime = time;
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport(outputPath) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    const duration = this.endTime && this.startTime ? 
      Math.round((this.endTime - this.startTime) / 1000) : 0;

    const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card.passed { border-left-color: #28a745; }
        .summary-card.failed { border-left-color: #dc3545; }
        .summary-card.skipped { border-left-color: #ffc107; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .passed .number { color: #28a745; }
        .failed .number { color: #dc3545; }
        .skipped .number { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-item { background: #fff; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-item.passed { border-left: 4px solid #28a745; }
        .test-item.failed { border-left: 4px solid #dc3545; }
        .test-item.skipped { border-left: 4px solid #ffc107; }
        .test-status { display: inline-block; padding: 6px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .detail-card { transition: transform 0.2s, box-shadow 0.2s; }
        .detail-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px; }
        .screenshot-item { background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 详细测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>测试持续时间: ${duration}秒</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">${totalTests}</div>
            </div>
            <div class="summary-card passed">
                <h3>通过</h3>
                <div class="number">${passedTests}</div>
            </div>
            <div class="summary-card failed">
                <h3>失败</h3>
                <div class="number">${failedTests}</div>
            </div>
            <div class="summary-card skipped">
                <h3>跳过</h3>
                <div class="number">${skippedTests}</div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细测试报告</h2>
            ${this.generateTestItemsHTML()}
        </div>
    </div>
</body>
</html>`;

    fs.writeFileSync(outputPath, html, 'utf8');
    console.log(`📄 详细测试报告已生成: ${outputPath}`);
  }

  /**
   * 生成测试项目HTML
   */
  generateTestItemsHTML() {
    return this.testResults.map(result => this.generateTestItemHTML(result)).join('\n');
  }

  /**
   * 生成单个测试项目HTML
   */
  generateTestItemHTML(result) {
    const statusClass = result.result.toLowerCase();
    const statusText = result.result === 'PASSED' ? '通过' : 
                     result.result === 'FAILED' ? '失败' : '跳过';
    const durationText = result.duration ? `${result.duration}ms` : '未知';

    return `
                <div class="test-item ${statusClass}" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">${result.testId} - ${result.testName}</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-${statusClass}" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">${result.result}</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ ${durationText}</span>
                            </div>
                        </div>
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${result.testContent}</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${result.testPurpose}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">${result.testInput}</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${result.expectedOutput}</p>
                            </div>
                        </div>

                        ${this.generateTestStepsHTML(result.testSteps)}

                        ${this.generateExecutionResultHTML(result)}
                    </div>

                    ${this.generateScreenshotsHTML(result.screenshots)}
                </div>`;
  }

  /**
   * 生成测试步骤HTML
   */
  generateTestStepsHTML(steps) {
    if (!steps || steps.length === 0) return '';
    
    const stepsHTML = steps.map((step, index) => 
      `<li style="margin-bottom: 4px;">${index + 1}. ${step}</li>`
    ).join('');

    return `
                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                ${stepsHTML}
                            </ol>
                        </div>`;
  }

  /**
   * 生成执行结果HTML
   */
  generateExecutionResultHTML(result) {
    const isSuccess = result.result === 'PASSED';
    const bgColor = isSuccess ? '#d4edda' : result.result === 'FAILED' ? '#f8d7da' : '#fff3cd';
    const textColor = isSuccess ? '#155724' : result.result === 'FAILED' ? '#721c24' : '#856404';
    const borderColor = isSuccess ? '#28a745' : result.result === 'FAILED' ? '#dc3545' : '#ffc107';
    const icon = isSuccess ? '✅' : result.result === 'FAILED' ? '❌' : '⏭️';
    const statusText = isSuccess ? '通过' : result.result === 'FAILED' ? '失败' : '跳过';

    let content = `
                        <div class="execution-result" style="background: ${bgColor}; padding: 15px; border-radius: 6px; border-left: 4px solid ${borderColor};">
                            <h4 style="margin: 0 0 8px 0; color: ${textColor}; font-size: 0.9em;">
                                ${icon} 执行结果 - ${statusText}
                            </h4>
                            <p style="margin: 0; color: ${textColor}; font-size: 0.85em; line-height: 1.4;">
                                ${result.actualOutput}
                            </p>`;

    if (result.error) {
      content += `
                            <div class="error-details" style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em;">
                                <strong>错误详情:</strong><br>
                                ${result.error}
                            </div>`;
    }

    content += `
                        </div>`;

    return content;
  }

  /**
   * 生成截图HTML
   */
  generateScreenshotsHTML(screenshots) {
    if (!screenshots || screenshots.length === 0) return '';

    const screenshotsHTML = screenshots.map((screenshot, index) => `
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="${screenshot.path}" alt="${screenshot.description}" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 ${index + 1}</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 ${new Date(screenshot.timestamp).toLocaleString('zh-CN')}</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">${screenshot.description}</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">${path.basename(screenshot.path)}</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 ${screenshot.size || 'Unknown'}</p>
                                        </div>
                                    </div>`).join('');

    return `
                    <!-- 测试截图区域 -->
                    <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                        <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                            📸 执行截图记录
                            <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">${screenshots.length}张</span>
                        </h4>
                        <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                            ${screenshotsHTML}
                        </div>
                    </div>`;
  }

  /**
   * 生成JSON报告
   */
  generateJSONReport(outputPath) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.testResults.length,
        passedTests: this.testResults.filter(r => r.result === 'PASSED').length,
        failedTests: this.testResults.filter(r => r.result === 'FAILED').length,
        skippedTests: this.testResults.filter(r => r.result === 'SKIPPED').length,
        duration: this.endTime && this.startTime ? this.endTime - this.startTime : 0
      },
      results: this.testResults
    };

    fs.writeFileSync(outputPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(`📄 JSON测试报告已生成: ${outputPath}`);
  }
}

module.exports = DetailedReportGenerator;
