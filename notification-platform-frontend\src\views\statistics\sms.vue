<template>
  <div class="sms-statistics-container">
    <!-- 查询条件 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input
            v-model="queryParams.recipient"
            placeholder="请输入手机号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="模板编号">
          <el-input
            v-model="queryParams.templateCode"
            placeholder="请输入模板编号"
            clearable
            style="width: 150px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select v-model="queryParams.sendStatus" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="发送成功" :value="1" />
            <el-option label="发送失败" :value="2" />
            <el-option label="正在发送" :value="3" />
            <el-option label="不明状态" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="exportData">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 统计卡片 -->
    <div class="statistics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon total">
                <el-icon><Message /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">总发送数</div>
                <div class="card-value">{{ overallStats.totalCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">成功数</div>
                <div class="card-value">{{ overallStats.successCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">失败数</div>
                <div class="card-value">{{ overallStats.failedCount || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="statistics-card">
            <div class="card-content">
              <div class="card-icon rate">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">成功率</div>
                <div class="card-value">{{ (overallStats.successRate || 0).toFixed(2) }}%</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 操作按钮 -->
    <div class="toolbar-container">
      <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchResend">批量重发</el-button>
      <el-button type="warning" @click="handleRefreshStatus">刷新状态</el-button>
    </div>

    <!-- 短信发送详情表格 -->
    <el-table
      v-loading="loading"
      :data="smsList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="messageId" label="消息ID" width="180" show-overflow-tooltip />
      <el-table-column prop="templateCode" label="模板编号" width="120" />
      <el-table-column prop="templateName" label="模板名称" width="150" show-overflow-tooltip />
      <el-table-column prop="recipient" label="手机号" width="130" />
      <el-table-column prop="content" label="短信内容" width="200" show-overflow-tooltip />
      <el-table-column prop="channelCode" label="发送渠道" width="100" />
      <el-table-column prop="sendStatus" label="发送状态" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.sendStatus)">
            {{ getStatusName(scope.row.sendStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="sendTime" label="发送时间" width="160" />
      <el-table-column prop="completeTime" label="完成时间" width="160" />
      <el-table-column prop="retryCount" label="重试次数" width="80" align="center" />
      <el-table-column prop="errorMessage" label="错误信息" width="200" show-overflow-tooltip />
      <el-table-column prop="upContent" label="上行内容" width="150" show-overflow-tooltip />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="viewDetails(scope.row)">详情</el-button>
          <el-button 
            v-if="scope.row.sendStatus === 2" 
            type="warning" 
            size="small" 
            @click="resendSms(scope.row.messageId)"
          >
            重发
          </el-button>
          <el-button type="info" size="small" @click="checkStatus(scope.row.messageId)">查状态</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.current"
      v-model:limit="queryParams.size"
      @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog
      title="短信发送详情"
      v-model="detailDialogVisible"
      width="60%"
      :before-close="handleClose"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="消息ID">{{ currentDetail.messageId }}</el-descriptions-item>
        <el-descriptions-item label="模板编号">{{ currentDetail.templateCode }}</el-descriptions-item>
        <el-descriptions-item label="模板名称">{{ currentDetail.templateName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ currentDetail.recipient }}</el-descriptions-item>
        <el-descriptions-item label="发送渠道">{{ currentDetail.channelCode }}</el-descriptions-item>
        <el-descriptions-item label="发送状态">
          <el-tag :type="getStatusType(currentDetail.sendStatus)">
            {{ getStatusName(currentDetail.sendStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="发送时间">{{ currentDetail.sendTime }}</el-descriptions-item>
        <el-descriptions-item label="完成时间">{{ currentDetail.completeTime }}</el-descriptions-item>
        <el-descriptions-item label="重试次数">{{ currentDetail.retryCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="错误代码">{{ currentDetail.errorCode }}</el-descriptions-item>
        <el-descriptions-item label="短信内容" :span="2">
          <div class="content-box">{{ currentDetail.content }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="上行内容" :span="2" v-if="currentDetail.upContent">
          <div class="content-box">{{ currentDetail.upContent }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2" v-if="currentDetail.errorMessage">
          <div class="error-box">{{ currentDetail.errorMessage }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2" v-if="currentDetail.remark">
          <div class="remark-box">{{ currentDetail.remark }}</div>
        </el-descriptions-item>
      </el-descriptions>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button 
            v-if="currentDetail.sendStatus === 2" 
            type="warning" 
            @click="resendSms(currentDetail.messageId)"
          >
            重发
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 状态检查对话框 -->
    <el-dialog title="状态检查结果" v-model="statusDialogVisible" width="500px">
      <div v-if="statusResult">
        <el-alert
          :title="statusResult.success ? '状态查询成功' : '状态查询失败'"
          :type="statusResult.success ? 'success' : 'error'"
          :closable="false"
          style="margin-bottom: 15px"
        />
        
        <el-descriptions :column="1" border v-if="statusResult.success">
          <el-descriptions-item label="消息ID">{{ statusResult.messageId }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(statusResult.status)">
              {{ statusResult.statusDesc }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="!statusResult.success" class="error-info">
          <p><strong>错误信息：</strong>{{ statusResult.errorMessage }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Message, Check, Close, TrendCharts } from '@element-plus/icons-vue'
import { getMessageStatistics, getMessageRecords, getMessageRecordById, resendMessage, batchResendMessages, querySmsStatus, refreshMessageStatus } from '@/api/message'
import Pagination from '@/components/Pagination/index.vue'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const statusDialogVisible = ref(false)
const dateRange = ref([])
const smsList = ref([])
const total = ref(0)
const multipleSelection = ref([])
const currentDetail = ref({})
const statusResult = ref(null)
const overallStats = ref({})

// 查询参数
const queryParams = reactive({
  current: 1,
  size: 10,
  messageType: 1, // 短信类型
  templateCode: '',
  recipient: '',
  sendStatus: null,
  startTime: '',
  endTime: ''
})

// 生命周期
onMounted(() => {
  // 设置默认时间范围（最近7天）
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)
  
  dateRange.value = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]
  
  handleQuery()
})

// 方法
const handleQuery = async () => {
  if (dateRange.value && dateRange.value.length === 2) {
    queryParams.startTime = dateRange.value[0]
    queryParams.endTime = dateRange.value[1]
  }
  
  queryParams.current = 1
  await getList()
  await getStatistics()
}

const resetQuery = () => {
  queryParams.templateCode = ''
  queryParams.recipient = ''
  queryParams.sendStatus = null
  
  const endTime = new Date()
  const startTime = new Date()
  startTime.setDate(startTime.getDate() - 7)
  
  dateRange.value = [
    startTime.toISOString().slice(0, 19).replace('T', ' '),
    endTime.toISOString().slice(0, 19).replace('T', ' ')
  ]
  
  handleQuery()
}

const getList = async () => {
  loading.value = true
  try {
    const response = await getMessageRecords(queryParams)
    smsList.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('获取短信列表失败:', error)
    ElMessage.error('获取短信列表失败')
  } finally {
    loading.value = false
  }
}

const getStatistics = async () => {
  try {
    const statsParams = {
      messageType: 1,
      startTime: queryParams.startTime,
      endTime: queryParams.endTime
    }
    const response = await getMessageStatistics(statsParams)
    overallStats.value = response.data.overall || {}
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

const getStatusType = (status) => {
  switch (status) {
    case 1: return 'success'
    case 2: return 'danger'
    case 3: return 'warning'
    case 4: return 'info'
    default: return 'info'
  }
}

const getStatusName = (status) => {
  switch (status) {
    case 1: return '发送成功'
    case 2: return '发送失败'
    case 3: return '正在发送'
    case 4: return '不明状态'
    default: return '未知'
  }
}

const viewDetails = async (row) => {
  try {
    const response = await getMessageRecordById(row.messageId)
    currentDetail.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

const resendSms = async (messageId) => {
  try {
    await ElMessageBox.confirm('确定要重发这条短信吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await resendMessage(messageId)
    ElMessage.success('重发成功')
    getList()
    detailDialogVisible.value = false
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重发短信失败:', error)
      ElMessage.error('重发失败')
    }
  }
}

const handleBatchResend = async () => {
  const failedMessages = multipleSelection.value.filter(item => item.sendStatus === 2)
  
  if (failedMessages.length === 0) {
    ElMessage.warning('请选择发送失败的短信进行重发')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要重发选中的 ${failedMessages.length} 条失败短信吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const messageIds = failedMessages.map(item => item.messageId)
    const response = await batchResendMessages(messageIds)
    ElMessage.success(response.data)
    getList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量重发失败:', error)
      ElMessage.error('批量重发失败')
    }
  }
}

const checkStatus = async (messageId) => {
  try {
    const response = await querySmsStatus(messageId)
    statusResult.value = response.data
    statusDialogVisible.value = true
  } catch (error) {
    console.error('查询状态失败:', error)
    ElMessage.error('查询状态失败')
  }
}

const handleRefreshStatus = async () => {
  try {
    await ElMessageBox.confirm('确定要刷新所有短信的状态吗？这可能需要一些时间。', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    loading.value = true
    const refreshParams = {
      messageType: 1, // 短信类型
      startTime: queryParams.startTime,
      endTime: queryParams.endTime
    }
    
    const response = await refreshMessageStatus(refreshParams)
    ElMessage.success(response.data || '状态刷新完成')
    
    // 刷新列表
    await getList()
    await getStatistics()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('刷新状态失败:', error)
      ElMessage.error('刷新状态失败')
    }
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  detailDialogVisible.value = false
}

const exportData = async () => {
  try {
    loading.value = true
    
    // 构建导出参数
    const exportParams = {
      ...queryParams,
      current: 1,
      size: 10000 // 导出更多数据
    }
    
    const response = await getMessageRecords(exportParams)
    const data = response.data.records
    
    if (!data || data.length === 0) {
      ElMessage.warning('没有数据可导出')
      return
    }
    
    // 构建CSV内容
    const headers = [
      '消息ID', '模板编号', '模板名称', '手机号', '短信内容', 
      '发送渠道', '发送状态', '发送时间', '完成时间', '重试次数', 
      '错误信息', '上行内容'
    ]
    
    const csvContent = [
      headers.join(','),
      ...data.map(row => [
        row.messageId || '',
        row.templateCode || '',
        row.templateName || '',
        row.recipient || '',
        `"${(row.content || '').replace(/"/g, '""')}"`,
        row.channelCode || '',
        getStatusName(row.sendStatus),
        row.sendTime || '',
        row.completeTime || '',
        row.retryCount || 0,
        `"${(row.errorMessage || '').replace(/"/g, '""')}"`,
        `"${(row.upContent || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n')
    
    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `短信发送统计_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.sms-statistics-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.statistics-cards {
  margin-bottom: 20px;
}

.statistics-card {
  height: 100px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.card-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.failed {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.card-icon.rate {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 5px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.toolbar-container {
  margin-bottom: 20px;
}

.content-box {
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  word-break: break-all;
}

.error-box {
  background: #fef0f0;
  color: #f56c6c;
  padding: 10px;
  border-radius: 4px;
  word-break: break-all;
}

.remark-box {
  background: #f0f9ff;
  color: #409eff;
  padding: 10px;
  border-radius: 4px;
  word-break: break-all;
}

.error-info {
  padding: 10px;
  background: #fef0f0;
  border-radius: 4px;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>