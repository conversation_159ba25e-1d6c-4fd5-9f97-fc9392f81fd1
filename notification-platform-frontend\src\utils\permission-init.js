/**
 * 权限初始化配置
 * 定义系统的基础权限结构
 */

export const PERMISSION_TREE = [
  {
    id: 1,
    name: '系统管理',
    code: 'system',
    type: 'menu',
    path: '/system',
    icon: 'setting',
    sort: 1,
    status: 1,
    children: [
      {
        id: 11,
        name: '用户管理',
        code: 'system:user',
        type: 'menu',
        path: '/user/operator',
        icon: 'user',
        sort: 1,
        status: 1,
        children: [
          {
            id: 111,
            name: '查看用户',
            code: 'system:user:view',
            type: 'button',
            sort: 1,
            status: 1
          },
          {
            id: 112,
            name: '新增用户',
            code: 'system:user:add',
            type: 'button',
            sort: 2,
            status: 1
          },
          {
            id: 113,
            name: '编辑用户',
            code: 'system:user:edit',
            type: 'button',
            sort: 3,
            status: 1
          },
          {
            id: 114,
            name: '删除用户',
            code: 'system:user:delete',
            type: 'button',
            sort: 4,
            status: 1
          },
          {
            id: 115,
            name: '分配角色',
            code: 'system:user:role',
            type: 'button',
            sort: 5,
            status: 1
          },
          {
            id: 116,
            name: '重置密码',
            code: 'system:user:reset',
            type: 'button',
            sort: 6,
            status: 1
          },
          {
            id: 117,
            name: '更新状态',
            code: 'system:user:status',
            type: 'button',
            sort: 7,
            status: 1
          }
        ]
      },
      {
        id: 12,
        name: '角色管理',
        code: 'system:role',
        type: 'menu',
        path: '/user/role',
        icon: 'role',
        sort: 2,
        status: 1,
        children: [
          {
            id: 121,
            name: '查看角色',
            code: 'system:role:view',
            type: 'button',
            sort: 1,
            status: 1
          },
          {
            id: 122,
            name: '新增角色',
            code: 'system:role:add',
            type: 'button',
            sort: 2,
            status: 1
          },
          {
            id: 123,
            name: '编辑角色',
            code: 'system:role:edit',
            type: 'button',
            sort: 3,
            status: 1
          },
          {
            id: 124,
            name: '删除角色',
            code: 'system:role:delete',
            type: 'button',
            sort: 4,
            status: 1
          },
          {
            id: 125,
            name: '分配权限',
            code: 'system:role:permission',
            type: 'button',
            sort: 5,
            status: 1
          }
        ]
      },
      {
        id: 13,
        name: '权限管理',
        code: 'system:permission',
        type: 'menu',
        path: '/system/permission',
        icon: 'permission',
        sort: 3,
        status: 1,
        children: [
          {
            id: 131,
            name: '查看权限',
            code: 'system:permission:view',
            type: 'button',
            sort: 1,
            status: 1
          },
          {
            id: 132,
            name: '新增权限',
            code: 'system:permission:add',
            type: 'button',
            sort: 2,
            status: 1
          },
          {
            id: 133,
            name: '编辑权限',
            code: 'system:permission:edit',
            type: 'button',
            sort: 3,
            status: 1
          },
          {
            id: 134,
            name: '删除权限',
            code: 'system:permission:delete',
            type: 'button',
            sort: 4,
            status: 1
          }
        ]
      },
      {
        id: 14,
        name: '机构管理',
        code: 'system:organization',
        type: 'menu',
        path: '/user/organization',
        icon: 'organization',
        sort: 4,
        status: 1,
        children: [
          {
            id: 141,
            name: '查看机构',
            code: 'system:organization:view',
            type: 'button',
            sort: 1,
            status: 1
          },
          {
            id: 142,
            name: '新增机构',
            code: 'system:organization:add',
            type: 'button',
            sort: 2,
            status: 1
          },
          {
            id: 143,
            name: '编辑机构',
            code: 'system:organization:edit',
            type: 'button',
            sort: 3,
            status: 1
          },
          {
            id: 144,
            name: '删除机构',
            code: 'system:organization:delete',
            type: 'button',
            sort: 4,
            status: 1
          }
        ]
      },
      {
        id: 15,
        name: 'RBAC管理',
        code: 'system:rbac',
        type: 'menu',
        path: '/system/rbac',
        icon: 'rbac',
        sort: 5,
        status: 1
      }
    ]
  },
  {
    id: 2,
    name: '消息管理',
    code: 'message',
    type: 'menu',
    path: '/message',
    icon: 'message',
    sort: 2,
    status: 1,
    children: [
      {
        id: 21,
        name: '短信发送',
        code: 'message:sms',
        type: 'menu',
        sort: 1,
        status: 1,
        children: [
          {
            id: 211,
            name: '单条短信',
            code: 'message:sms:single',
            type: 'menu',
            path: '/message/sms/single',
            sort: 1,
            status: 1
          },
          {
            id: 212,
            name: '批量短信',
            code: 'message:sms:batch',
            type: 'menu',
            path: '/message/sms/batch',
            sort: 2,
            status: 1
          }
        ]
      },
      {
        id: 22,
        name: '邮件发送',
        code: 'message:email',
        type: 'menu',
        sort: 2,
        status: 1,
        children: [
          {
            id: 221,
            name: '单条邮件',
            code: 'message:email:single',
            type: 'menu',
            path: '/message/email/single',
            sort: 1,
            status: 1
          },
          {
            id: 222,
            name: '批量邮件',
            code: 'message:email:batch',
            type: 'menu',
            path: '/message/email/batch',
            sort: 2,
            status: 1
          },
          {
            id: 223,
            name: '营销邮件',
            code: 'message:email:marketing',
            type: 'menu',
            path: '/message/email/marketing',
            sort: 3,
            status: 1
          }
        ]
      }
    ]
  },
  {
    id: 3,
    name: '渠道管理',
    code: 'channel',
    type: 'menu',
    path: '/channel',
    icon: 'channel',
    sort: 3,
    status: 1,
    children: [
      {
        id: 31,
        name: '接入渠道',
        code: 'channel:access',
        type: 'menu',
        path: '/channel/access',
        sort: 1,
        status: 1
      },
      {
        id: 32,
        name: '发送渠道',
        code: 'channel:send',
        type: 'menu',
        path: '/channel/send',
        sort: 2,
        status: 1
      }
    ]
  },
  {
    id: 4,
    name: '模板管理',
    code: 'template',
    type: 'menu',
    path: '/template',
    icon: 'template',
    sort: 4,
    status: 1,
    children: [
      {
        id: 41,
        name: '模板管理',
        code: 'template:manage',
        type: 'menu',
        path: '/template/manage',
        sort: 1,
        status: 1
      },
      {
        id: 42,
        name: '参数管理',
        code: 'template:parameter',
        type: 'menu',
        path: '/template/parameter',
        sort: 2,
        status: 1
      },
      {
        id: 43,
        name: '类型管理',
        code: 'template:type',
        type: 'menu',
        path: '/template/type',
        sort: 3,
        status: 1
      }
    ]
  },
  {
    id: 5,
    name: '统计监控',
    code: 'statistics',
    type: 'menu',
    path: '/statistics',
    icon: 'statistics',
    sort: 5,
    status: 1,
    children: [
      {
        id: 51,
        name: '模板统计',
        code: 'statistics:template',
        type: 'menu',
        path: '/statistics/template',
        sort: 1,
        status: 1
      },
      {
        id: 52,
        name: '消息统计',
        code: 'statistics:message',
        type: 'menu',
        path: '/statistics/message',
        sort: 2,
        status: 1
      }
    ]
  },
  {
    id: 6,
    name: '安全管理',
    code: 'security',
    type: 'menu',
    path: '/security',
    icon: 'security',
    sort: 6,
    status: 1,
    children: [
      {
        id: 61,
        name: '黑名单管理',
        code: 'security:blacklist',
        type: 'menu',
        path: '/security/blacklist',
        sort: 1,
        status: 1
      },
      {
        id: 62,
        name: '白名单管理',
        code: 'security:whitelist',
        type: 'menu',
        path: '/security/whitelist',
        sort: 2,
        status: 1
      },
      {
        id: 63,
        name: '关键字管理',
        code: 'security:keyword',
        type: 'menu',
        path: '/security/keyword',
        sort: 3,
        status: 1
      }
    ]
  }
]

/**
 * 默认角色配置
 */
export const DEFAULT_ROLES = [
  {
    id: 1,
    roleName: '超级管理员',
    roleKey: 'admin',
    roleSort: 1,
    status: 1,
    remark: '系统超级管理员，拥有所有权限'
  },
  {
    id: 2,
    roleName: '系统管理员',
    roleKey: 'system_admin',
    roleSort: 2,
    status: 1,
    remark: '系统管理员，负责用户和权限管理'
  },
  {
    id: 3,
    roleName: '业务管理员',
    roleKey: 'business_admin',
    roleSort: 3,
    status: 1,
    remark: '业务管理员，负责业务功能管理'
  },
  {
    id: 4,
    roleName: '普通用户',
    roleKey: 'user',
    roleSort: 4,
    status: 1,
    remark: '普通用户，只能使用基本功能'
  }
]

/**
 * 默认用户配置
 */
export const DEFAULT_USERS = [
  {
    id: 1,
    userName: '超级管理员',
    loginName: 'admin',
    email: '<EMAIL>',
    mobile: '***********',
    status: 1,
    roles: ['admin']
  }
]

/**
 * 获取扁平化的权限列表
 */
export function getFlatPermissions(permissions = PERMISSION_TREE) {
  const result = []
  
  function traverse(items) {
    items.forEach(item => {
      result.push({
        id: item.id,
        name: item.name,
        code: item.code,
        type: item.type,
        path: item.path,
        icon: item.icon,
        sort: item.sort,
        status: item.status,
        parentId: item.parentId || null
      })
      
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => {
          child.parentId = item.id
        })
        traverse(item.children)
      }
    })
  }
  
  traverse(permissions)
  return result
}

/**
 * 根据角色获取默认权限
 */
export function getDefaultPermissionsByRole(roleKey) {
  const allPermissions = getFlatPermissions()
  
  switch (roleKey) {
    case 'admin':
      // 超级管理员拥有所有权限
      return allPermissions.map(p => p.code)
    
    case 'system_admin':
      // 系统管理员拥有系统管理相关权限
      return allPermissions
        .filter(p => p.code.startsWith('system:'))
        .map(p => p.code)
    
    case 'business_admin':
      // 业务管理员拥有业务相关权限
      return allPermissions
        .filter(p => !p.code.startsWith('system:') || p.code === 'system')
        .map(p => p.code)
    
    case 'user':
      // 普通用户只有查看权限
      return allPermissions
        .filter(p => p.type === 'menu' || p.code.includes(':view'))
        .map(p => p.code)
    
    default:
      return []
  }
}