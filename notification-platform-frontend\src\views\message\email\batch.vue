<template>
  <div class="batch-email-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>批量邮件发送</span>
        </div>
      </template>
      
      <el-form
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-width="120px"
        class="email-form"
      >
        <el-form-item label="发送方式" prop="sendType">
          <el-radio-group v-model="emailForm.sendType">
            <el-radio :label="1">手动输入</el-radio>
            <el-radio :label="2">Excel导入</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 手动输入方式 -->
        <template v-if="emailForm.sendType === 1">
          <el-form-item label="收件人邮箱" prop="recipients">
            <el-input
              v-model="emailForm.recipients"
              type="textarea"
              :rows="6"
              placeholder="请输入收件人邮箱地址，每行一个或用逗号分隔"
              show-word-limit
            />
            <div class="input-tip">
              支持每行一个邮箱或用逗号分隔，最多支持200个邮箱
            </div>
          </el-form-item>
        </template>

        <!-- Excel导入方式 -->
        <template v-if="emailForm.sendType === 2">
          <el-form-item label="Excel文件" prop="excelFile">
            <el-upload
              ref="uploadRef"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :auto-upload="false"
              :limit="1"
              accept=".xlsx,.xls"
              :on-exceed="handleExceed"
            >
              <el-button type="primary">选择Excel文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件，且不超过5MB
                  <el-link type="primary" @click="downloadTemplate">下载模板</el-link>
                </div>
              </template>
            </el-upload>
          </el-form-item>

          <!-- 显示解析结果 -->
          <el-form-item v-if="parsedEmails.length > 0" label="解析结果">
            <div class="parsed-result">
              <div class="result-summary">
                共解析到 <strong>{{ parsedEmails.length }}</strong> 个邮箱地址
                <el-button type="text" @click="showParsedEmails = !showParsedEmails">
                  {{ showParsedEmails ? '隐藏' : '查看' }}详情
                </el-button>
              </div>
              <div v-if="showParsedEmails" class="email-list">
                <el-tag
                  v-for="(email, index) in parsedEmails.slice(0, 50)"
                  :key="index"
                  class="email-tag"
                >
                  {{ email }}
                </el-tag>
                <div v-if="parsedEmails.length > 50" class="more-tip">
                  还有 {{ parsedEmails.length - 50 }} 个邮箱...
                </div>
              </div>
            </div>
          </el-form-item>
        </template>

        <el-form-item label="邮件主题" prop="subject">
          <el-input
            v-model="emailForm.subject"
            placeholder="请输入邮件主题"
            clearable
          />
        </el-form-item>

        <el-form-item label="邮件内容" prop="content">
          <el-input
            v-model="emailForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入邮件内容，支持HTML格式"
          />
        </el-form-item>

        <el-form-item label="发送渠道" prop="channelCode">
          <el-select
            v-model="emailForm.channelCode"
            placeholder="请选择发送渠道（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.channelCode"
              :label="channel.channelName"
              :value="channel.channelCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendBatchEmail"
          >
            {{ sending ? '发送中...' : '批量发送' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="previewEmail">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="批量邮件预览"
      width="60%"
    >
      <div class="email-preview">
        <div class="preview-item">
          <strong>发送数量：</strong>{{ getEmailList().length }} 个
        </div>
        <div class="preview-item">
          <strong>邮件主题：</strong>{{ emailForm.subject }}
        </div>
        <div class="preview-item">
          <strong>邮件内容：</strong>
          <div class="content-preview" v-html="emailForm.content"></div>
        </div>
        <div class="preview-item">
          <strong>收件人邮箱：</strong>
          <div class="email-preview-list">
            <el-tag
              v-for="(email, index) in getEmailList().slice(0, 20)"
              :key="index"
              class="email-tag"
            >
              {{ email }}
            </el-tag>
            <div v-if="getEmailList().length > 20" class="more-tip">
              还有 {{ getEmailList().length - 20 }} 个邮箱...
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 发送结果对话框 -->
    <el-dialog
      v-model="resultVisible"
      title="发送结果"
      width="50%"
    >
      <div class="send-result">
        <div class="result-item">
          <strong>批次ID：</strong>{{ sendResult.batchId }}
        </div>
        <div class="result-item">
          <strong>总数量：</strong>{{ sendResult.totalCount }}
        </div>
        <div class="result-item">
          <strong>成功数量：</strong>
          <span class="success-count">{{ sendResult.successCount }}</span>
        </div>
        <div class="result-item">
          <strong>失败数量：</strong>
          <span class="failure-count">{{ sendResult.failureCount }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { sendBatchEmail as sendBatchEmailApi } from '@/api/email'
import { getSendChannelsByType } from '@/api/channel'

// 表单数据
const emailForm = reactive({
  sendType: 1, // 1-手动输入，2-Excel导入
  recipients: '',
  subject: '',
  content: '',
  channelCode: ''
})

// 表单验证规则
const emailRules = {
  sendType: [
    { required: true, message: '请选择发送方式', trigger: 'change' }
  ],
  recipients: [
    { 
      validator: (rule, value, callback) => {
        if (emailForm.sendType === 1) {
          if (!value || value.trim() === '') {
            callback(new Error('请输入收件人邮箱地址'))
          } else {
            const emails = getEmailList()
            if (emails.length === 0) {
              callback(new Error('请输入有效的邮箱地址'))
            } else if (emails.length > 200) {
              callback(new Error('邮箱地址数量不能超过200个'))
            } else {
              callback()
            }
          }
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  excelFile: [
    { 
      validator: (rule, value, callback) => {
        if (emailForm.sendType === 2) {
          if (fileList.value.length === 0) {
            callback(new Error('请选择Excel文件'))
          } else if (parsedEmails.value.length === 0) {
            callback(new Error('Excel文件中没有解析到有效的邮箱地址'))
          } else if (parsedEmails.value.length > 200) {
            callback(new Error('邮箱地址数量不能超过200个'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' },
    { max: 200, message: '邮件主题不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' }
  ]
}

// 响应式数据
const emailFormRef = ref()
const uploadRef = ref()
const sending = ref(false)
const previewVisible = ref(false)
const resultVisible = ref(false)
const showParsedEmails = ref(false)
const fileList = ref([])
const parsedEmails = ref([])
const channelList = ref([])
const sendResult = reactive({
  batchId: '',
  totalCount: 0,
  successCount: 0,
  failureCount: 0
})

// 邮箱地址正则表达式
const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/

// 获取发送渠道列表
const getChannelList = async () => {
  try {
    const response = await getSendChannelsByType(2) // 2-邮件
    if (response.code === 200) {
      channelList.value = response.data || []
      // 默认选择第一个渠道
      if (channelList.value.length > 0) {
        emailForm.channelCode = channelList.value[0].channelCode
      }
    }
  } catch (error) {
    console.error('获取发送渠道失败:', error)
  }
}

// 获取邮箱地址列表
const getEmailList = () => {
  if (emailForm.sendType === 1) {
    // 手动输入方式
    if (!emailForm.recipients) return []
    
    const emails = emailForm.recipients
      .split(/[,，\n\r]/)
      .map(email => email.trim())
      .filter(email => email && emailPattern.test(email))
    
    // 去重
    return [...new Set(emails)]
  } else {
    // Excel导入方式
    return parsedEmails.value
  }
}

// 文件上传处理
const handleFileChange = (file, files) => {
  // 检查文件类型
  const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    files.splice(files.indexOf(file), 1)
    return
  }

  // 检查文件大小
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过5MB')
    files.splice(files.indexOf(file), 1)
    return
  }

  fileList.value = files
  parseExcelFile(file.raw)
}

// 文件移除处理
const handleFileRemove = (file, files) => {
  fileList.value = files
  parsedEmails.value = []
}

// 文件数量超限处理
const handleExceed = () => {
  ElMessage.warning('只能上传一个Excel文件')
}

// 解析Excel文件
const parseExcelFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      const emails = []
      jsonData.forEach(row => {
        if (row && row.length > 0) {
          const email = String(row[0]).trim()
          if (emailPattern.test(email)) {
            emails.push(email)
          }
        }
      })
      
      // 去重
      parsedEmails.value = [...new Set(emails)]
      
      if (parsedEmails.value.length === 0) {
        ElMessage.warning('Excel文件中没有找到有效的邮箱地址')
      } else {
        ElMessage.success(`成功解析到${parsedEmails.value.length}个邮箱地址`)
      }
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      ElMessage.error('解析Excel文件失败')
    }
  }
  reader.readAsArrayBuffer(file)
}

// 下载模板
const downloadTemplate = () => {
  const data = [
    ['邮箱地址'],
    ['<EMAIL>'],
    ['<EMAIL>'],
    ['<EMAIL>']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '邮箱地址')
  XLSX.writeFile(workbook, '批量邮件模板.xlsx')
}

// 批量发送邮件
const sendBatchEmail = async () => {
  try {
    // 表单验证
    await emailFormRef.value.validate()

    const emailList = getEmailList()
    if (emailList.length === 0) {
      ElMessage.error('请输入有效的邮箱地址')
      return
    }

    sending.value = true

    const emailData = {
      recipients: emailList,
      subject: emailForm.subject,
      content: emailForm.content
    }

    // 如果指定了渠道
    if (emailForm.channelCode) {
      emailData.channelCode = emailForm.channelCode
    }

    const response = await sendBatchEmailApi(emailData)

    if (response.code === 200) {
      // 显示发送结果
      Object.assign(sendResult, response.data)
      resultVisible.value = true
      ElMessage.success('批量邮件发送完成')
    } else {
      ElMessage.error(response.message || '批量邮件发送失败')
    }
  } catch (error) {
    console.error('批量发送邮件失败:', error)
    ElMessage.error('批量发送邮件失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  emailFormRef.value.resetFields()
  fileList.value = []
  parsedEmails.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 预览邮件
const previewEmail = async () => {
  try {
    await emailFormRef.value.validate()
    previewVisible.value = true
  } catch (error) {
    ElMessage.warning('请先完善邮件信息')
  }
}

// 组件挂载时获取渠道列表
onMounted(() => {
  getChannelList()
})
</script>

<style scoped>
.batch-email-container {
  padding: 20px;
}

.box-card {
  max-width: 900px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.email-form {
  margin-top: 20px;
}

.input-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.parsed-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.result-summary {
  margin-bottom: 10px;
}

.email-list {
  max-height: 200px;
  overflow-y: auto;
}

.email-tag {
  margin: 2px;
}

.more-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
}

.email-preview {
  padding: 20px;
}

.preview-item {
  margin-bottom: 15px;
  line-height: 1.6;
}

.content-preview {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 100px;
}

.email-preview-list {
  margin-top: 10px;
  max-height: 150px;
  overflow-y: auto;
}

.send-result {
  padding: 20px;
}

.result-item {
  margin-bottom: 15px;
  line-height: 1.6;
  font-size: 16px;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
}

.failure-count {
  color: #f56c6c;
  font-weight: bold;
}
</style>