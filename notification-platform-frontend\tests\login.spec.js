const { test, expect } = require('@playwright/test');

test.describe('登录功能测试', () => {
  test.beforeEach(async ({ page }) => {
    // 每个测试前访问登录页面
    await page.goto('/login');
  });

  test('T001-登录页面加载测试', async ({ page }) => {
    const testId = 'T001';

    // 测试信息记录
    const testInfo = {
      content: '验证登录页面能够正常加载并显示所有必要的UI元素',
      purpose: '确保用户能够看到完整的登录界面，包括用户名输入框、密码输入框和登录按钮',
      input: '访问登录页面URL: http://localhost:3000/login',
      expectedOutput: '页面正常加载，显示登录表单的所有必要元素',
      actualOutput: '',
      result: 'PENDING'
    };

    try {
      // 等待页面加载
      await page.waitForSelector('.login-container');

      // 截图：初始页面
      await page.screenshot({
        path: `test-results/screenshots/${testId}-login-initial.png`,
        fullPage: true
      });

      // 验证页面元素
      await expect(page.locator('input[placeholder="用户名"]')).toBeVisible();
      await expect(page.locator('input[placeholder="密码"]')).toBeVisible();
      await expect(page.locator('button:has-text("登录")')).toBeVisible();

      // 截图：页面元素验证完成
      await page.screenshot({
        path: `test-results/screenshots/${testId}-elements-verified.png`
      });

      testInfo.actualOutput = '✅ 登录容器加载成功\n✅ 用户名输入框显示正常\n✅ 密码输入框显示正常\n✅ 登录按钮显示正常';
      testInfo.result = 'PASSED';

    } catch (error) {
      testInfo.actualOutput = `❌ 测试失败: ${error.message}`;
      testInfo.result = 'FAILED';
      throw error;
    }

    // 将测试信息附加到页面上下文（用于报告生成）
    await page.evaluate((info) => {
      window.testInfo = window.testInfo || {};
      window.testInfo['T001'] = info;
    }, testInfo);
  });

  test('T002-用户名密码填写测试', async ({ page }) => {
    const testId = 'T002';

    // 等待页面加载
    await page.waitForSelector('.login-container');

    // 填写用户名
    await page.fill('input[placeholder="用户名"]', 'testuser');
    await page.screenshot({
      path: `test-results/screenshots/${testId}-username-filled.png`
    });

    // 验证用户名已填写
    await expect(page.locator('input[placeholder="用户名"]')).toHaveValue('testuser');

    // 填写密码
    await page.fill('input[placeholder="密码"]', 'testpass');
    await page.screenshot({
      path: `test-results/screenshots/${testId}-form-completed.png`
    });

    // 验证密码已填写
    await expect(page.locator('input[placeholder="密码"]')).toHaveValue('testpass');
  });

  test('T003-登录提交测试', async ({ page }) => {
    const testId = 'T003';

    // 等待页面加载
    await page.waitForSelector('.login-container');

    // 填写登录信息
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'Admin123!');

    // 截图：提交前
    await page.screenshot({
      path: `test-results/screenshots/${testId}-before-submit.png`
    });

    // 点击登录按钮
    await page.click('button:has-text("登录")');

    // 等待响应
    await page.waitForTimeout(2000);
    await page.screenshot({
      path: `test-results/screenshots/${testId}-after-submit.png`
    });

    // 验证登录结果（这里可能需要根据实际情况调整）
    // 如果登录成功，应该跳转到dashboard
    // 如果登录失败，应该显示错误信息
  });

  test('T004-空用户名登录测试', async ({ page }) => {
    const testId = 'T004';

    // 等待页面加载
    await page.waitForSelector('.login-container');

    // 只填写密码，不填用户名
    await page.fill('input[placeholder="密码"]', 'testpass');

    // 截图：填写密码但用户名为空
    await page.screenshot({
      path: `test-results/screenshots/${testId}-empty-username-state.png`
    });

    // 点击登录按钮
    await page.click('button:has-text("登录")');

    await page.screenshot({
      path: `test-results/screenshots/${testId}-validation-result.png`
    });

    // 验证应该显示错误提示或者按钮不可点击
  });

  test('T005-空密码登录测试', async ({ page }) => {
    const testId = 'T005';

    // 等待页面加载
    await page.waitForSelector('.login-container');

    // 只填写用户名，不填密码
    await page.fill('input[placeholder="用户名"]', 'testuser');

    // 截图：填写用户名但密码为空
    await page.screenshot({
      path: `test-results/screenshots/${testId}-empty-password-state.png`
    });

    // 点击登录按钮
    await page.click('button:has-text("登录")');

    await page.screenshot({
      path: `test-results/screenshots/${testId}-validation-result.png`
    });

    // 验证应该显示错误提示或者按钮不可点击
  });
});
