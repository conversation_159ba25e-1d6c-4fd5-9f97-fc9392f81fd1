/**
 * T156-T160: 模板版本管理功能测试
 * 基于需求文档中的模板版本管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class TemplateVersionTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T156: 模板版本管理页面加载测试
   */
  async testT156_TemplateVersionPageLoad() {
    const testId = 'T156';
    console.log(`\n🧪 执行测试 ${testId}: 模板版本管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到模板版本管理页面
      await this.testHelper.navigateTo('/template/version');
      await this.testHelper.waitForPageLoad(selectors.templateVersion.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isTemplateListVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.templateList);
      const isVersionListVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.versionList);
      const isVersionCompareVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.versionCompare);
      const isCreateVersionButtonVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.createVersionButton);
      const isVersionControlPanelVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.versionControlPanel);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '模板版本管理页面加载测试',
        testContent: '验证模板版本管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的模板版本管理界面',
        testInput: '访问模板版本管理页面URL: /template/version',
        expectedOutput: '页面正常加载，显示模板列表、版本列表、版本对比、创建版本按钮和版本控制面板',
        actualOutput: `模板列表: ${isTemplateListVisible ? '✅显示' : '❌隐藏'}, 版本列表: ${isVersionListVisible ? '✅显示' : '❌隐藏'}, 版本对比: ${isVersionCompareVisible ? '✅显示' : '❌隐藏'}, 创建版本: ${isCreateVersionButtonVisible ? '✅显示' : '❌隐藏'}, 控制面板: ${isVersionControlPanelVisible ? '✅显示' : '❌隐藏'}`,
        result: isTemplateListVisible || isVersionListVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '模板版本管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T157: 创建模板版本测试
   */
  async testT157_CreateTemplateVersion() {
    const testId = 'T157';
    console.log(`\n🧪 执行测试 ${testId}: 创建模板版本测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板版本管理页面
      await this.testHelper.navigateTo('/template/version');
      await this.testHelper.waitForPageLoad(selectors.templateVersion.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 创建新版本
      try {
        // 选择一个模板
        await this.testHelper.page.click(`${selectors.templateVersion.templateList} .template-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('选择模板');
        
        // 点击创建版本按钮
        await this.testHelper.page.click(selectors.templateVersion.createVersionButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('创建版本对话框');
        
        // 填写版本信息
        const versionInfo = {
          version: `v${this.testHelper.generateRandomString(3)}.0`,
          description: `版本描述_${this.testHelper.generateRandomString(4)}`,
          changeLog: `更新日志_${this.testHelper.generateRandomString(6)}`
        };
        
        await this.testHelper.page.fill(selectors.templateVersion.versionNumberInput, versionInfo.version);
        await this.testHelper.page.fill(selectors.templateVersion.versionDescriptionInput, versionInfo.description);
        await this.testHelper.page.fill(selectors.templateVersion.changeLogInput, versionInfo.changeLog);
        
        // 修改模板内容
        const newContent = `更新的模板内容_${this.testHelper.generateRandomString(6)}`;
        await this.testHelper.page.fill(selectors.templateVersion.templateContentInput, newContent);
        
        // 选择版本类型
        try {
          await this.testHelper.page.selectOption(selectors.templateVersion.versionTypeSelect, 'MINOR');
        } catch (typeError) {
          // 如果没有类型选择器，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存版本
        await this.testHelper.page.click(selectors.templateVersion.saveVersionButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '创建模板版本测试',
          testContent: '创建新的模板版本',
          testPurpose: '验证模板版本创建功能能够正常工作',
          testInput: `版本号: ${versionInfo.version}, 描述: ${versionInfo.description}`,
          expectedOutput: '模板版本创建成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (createError) {
        // 如果没有创建版本功能，标记为跳过
        const result = {
          testId: testId,
          testName: '创建模板版本测试',
          testContent: '创建新的模板版本',
          testPurpose: '验证模板版本创建功能能够正常工作',
          testInput: '查找模板版本创建功能',
          expectedOutput: '找到版本创建并成功执行',
          actualOutput: '未找到模板版本创建功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到模板版本创建功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '创建模板版本测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T158: 版本对比功能测试
   */
  async testT158_VersionComparison() {
    const testId = 'T158';
    console.log(`\n🧪 执行测试 ${testId}: 版本对比功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板版本管理页面
      await this.testHelper.navigateTo('/template/version');
      await this.testHelper.waitForPageLoad(selectors.templateVersion.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行版本对比
      try {
        // 选择一个模板
        await this.testHelper.page.click(`${selectors.templateVersion.templateList} .template-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        
        // 获取版本列表
        const versions = await this.testHelper.page.$$(`${selectors.templateVersion.versionList} .version-item`);
        
        if (versions.length >= 2) {
          // 选择两个版本进行对比
          await this.testHelper.page.click(`${selectors.templateVersion.versionList} .version-item:nth-child(1) .compare-checkbox`);
          await this.testHelper.page.click(`${selectors.templateVersion.versionList} .version-item:nth-child(2) .compare-checkbox`);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('选择对比版本');
          
          // 点击对比按钮
          await this.testHelper.page.click(selectors.templateVersion.compareButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeCustomScreenshot('版本对比结果');
          
          // 验证对比结果
          const isCompareResultVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.compareResult);
          const isDiffViewVisible = await this.testHelper.verifyElementVisibility(selectors.templateVersion.diffView);
          
          const result = {
            testId: testId,
            testName: '版本对比功能测试',
            testContent: '对比不同版本的模板内容',
            testPurpose: '验证版本对比功能能够正常工作',
            testInput: `对比版本数量: 2个，总版本数: ${versions.length}`,
            expectedOutput: '显示版本对比结果和差异视图',
            actualOutput: `对比结果: ${isCompareResultVisible ? '✅显示' : '❌隐藏'}, 差异视图: ${isDiffViewVisible ? '✅显示' : '❌隐藏'}`,
            result: isCompareResultVisible || isDiffViewVisible ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } else {
          // 版本数量不足
          const result = {
            testId: testId,
            testName: '版本对比功能测试',
            testContent: '对比不同版本的模板内容',
            testPurpose: '验证版本对比功能能够正常工作',
            testInput: `当前版本数量: ${versions.length}`,
            expectedOutput: '至少需要2个版本进行对比',
            actualOutput: '版本数量不足，无法进行对比',
            result: 'SKIPPED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`⏭️ 测试 ${testId} 跳过: 版本数量不足`);
        }
        
      } catch (compareError) {
        // 如果没有对比功能，标记为跳过
        const result = {
          testId: testId,
          testName: '版本对比功能测试',
          testContent: '对比不同版本的模板内容',
          testPurpose: '验证版本对比功能能够正常工作',
          testInput: '查找版本对比功能',
          expectedOutput: '找到版本对比并成功执行',
          actualOutput: '未找到版本对比功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到版本对比功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '版本对比功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T159: 版本回滚测试
   */
  async testT159_VersionRollback() {
    const testId = 'T159';
    console.log(`\n🧪 执行测试 ${testId}: 版本回滚测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板版本管理页面
      await this.testHelper.navigateTo('/template/version');
      await this.testHelper.waitForPageLoad(selectors.templateVersion.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行版本回滚
      try {
        // 选择一个模板
        await this.testHelper.page.click(`${selectors.templateVersion.templateList} .template-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        
        // 选择要回滚到的版本（选择第二个版本）
        await this.testHelper.page.click(`${selectors.templateVersion.versionList} .version-item:nth-child(2)`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('选择回滚版本');
        
        // 点击回滚按钮
        await this.testHelper.page.click(selectors.templateVersion.rollbackButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('版本回滚确认对话框');
        
        // 填写回滚原因
        const rollbackReason = `版本回滚测试_${this.testHelper.generateRandomString(4)}`;
        await this.testHelper.page.fill(selectors.templateVersion.rollbackReasonInput, rollbackReason);
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 确认回滚
        await this.testHelper.page.click(selectors.templateVersion.confirmRollbackButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取回滚结果
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '版本回滚测试',
          testContent: '回滚模板到指定版本',
          testPurpose: '验证版本回滚功能能够正常工作',
          testInput: `回滚原因: ${rollbackReason}`,
          expectedOutput: '版本回滚成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (rollbackError) {
        // 如果没有回滚功能，标记为跳过
        const result = {
          testId: testId,
          testName: '版本回滚测试',
          testContent: '回滚模板到指定版本',
          testPurpose: '验证版本回滚功能能够正常工作',
          testInput: '查找版本回滚功能',
          expectedOutput: '找到版本回滚并成功执行',
          actualOutput: '未找到版本回滚功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到版本回滚功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '版本回滚测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T160: 版本发布管理测试
   */
  async testT160_VersionReleaseManagement() {
    const testId = 'T160';
    console.log(`\n🧪 执行测试 ${testId}: 版本发布管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到模板版本管理页面
      await this.testHelper.navigateTo('/template/version');
      await this.testHelper.waitForPageLoad(selectors.templateVersion.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 管理版本发布
      try {
        // 选择一个模板
        await this.testHelper.page.click(`${selectors.templateVersion.templateList} .template-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        
        // 选择一个草稿版本进行发布
        await this.testHelper.page.click(`${selectors.templateVersion.versionList} .version-item[data-status="DRAFT"]:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('选择草稿版本');
        
        // 点击发布按钮
        await this.testHelper.page.click(selectors.templateVersion.publishButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('版本发布确认对话框');
        
        // 填写发布信息
        const releaseInfo = {
          releaseNotes: `发布说明_${this.testHelper.generateRandomString(6)}`,
          effectiveDate: new Date().toISOString().split('T')[0]
        };
        
        await this.testHelper.page.fill(selectors.templateVersion.releaseNotesInput, releaseInfo.releaseNotes);
        await this.testHelper.page.fill(selectors.templateVersion.effectiveDateInput, releaseInfo.effectiveDate);
        
        // 选择发布环境
        try {
          await this.testHelper.page.check('.environment-checkbox[data-env="PRODUCTION"]');
        } catch (envError) {
          // 如果没有环境选择，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 确认发布
        await this.testHelper.page.click(selectors.templateVersion.confirmPublishButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取发布结果
        const successMessage = await this.testHelper.getSuccessMessage();
        
        // 测试版本下线
        try {
          // 选择一个已发布的版本
          await this.testHelper.page.click(`${selectors.templateVersion.versionList} .version-item[data-status="PUBLISHED"]:first-child`);
          await this.testHelper.wait(testData.timeouts.short);
          
          // 点击下线按钮
          await this.testHelper.page.click(selectors.templateVersion.unpublishButton);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('版本下线确认');
          
          // 确认下线
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
        } catch (unpublishError) {
          // 如果没有下线功能，跳过
        }
        
        const result = {
          testId: testId,
          testName: '版本发布管理测试',
          testContent: '发布和下线模板版本',
          testPurpose: '验证版本发布管理功能能够正常工作',
          testInput: `发布说明: ${releaseInfo.releaseNotes}, 生效日期: ${releaseInfo.effectiveDate}`,
          expectedOutput: '版本发布和下线操作成功执行',
          actualOutput: `发布结果: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (releaseError) {
        // 如果没有发布管理功能，标记为跳过
        const result = {
          testId: testId,
          testName: '版本发布管理测试',
          testContent: '发布和下线模板版本',
          testPurpose: '验证版本发布管理功能能够正常工作',
          testInput: '查找版本发布管理功能',
          expectedOutput: '找到发布管理并成功执行',
          actualOutput: '未找到版本发布管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到版本发布管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '版本发布管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有模板版本管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行模板版本管理功能测试套件 (T156-T160)');
    
    const startTime = Date.now();
    
    await this.testT156_TemplateVersionPageLoad();
    await this.testT157_CreateTemplateVersion();
    await this.testT158_VersionComparison();
    await this.testT159_VersionRollback();
    await this.testT160_VersionReleaseManagement();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 模板版本管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const templateVersionTest = new TemplateVersionTest();
  templateVersionTest.runAllTests().catch(console.error);
}

module.exports = TemplateVersionTest;
