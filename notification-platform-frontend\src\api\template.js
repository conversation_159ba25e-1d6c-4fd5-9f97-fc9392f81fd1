import request from '@/utils/request'

// 分页查询模板列表
export function getTemplatePage(params) {
  return request({
    url: '/api/template/page',
    method: 'get',
    params
  })
}

// 根据ID获取模板详情
export function getTemplateById(id) {
  return request({
    url: `/api/template/${id}`,
    method: 'get'
  })
}

// 根据模板编码获取模板详情
export function getTemplateByCode(templateCode) {
  return request({
    url: `/api/template/code/${templateCode}`,
    method: 'get'
  })
}

// 创建模板
export function createTemplate(data) {
  return request({
    url: '/api/template',
    method: 'post',
    data
  })
}

// 更新模板
export function updateTemplate(id, data) {
  return request({
    url: `/api/template/${id}`,
    method: 'put',
    data
  })
}

// 删除模板
export function deleteTemplate(id) {
  return request({
    url: `/api/template/${id}`,
    method: 'delete'
  })
}

// 批量删除模板
export function batchDeleteTemplates(ids) {
  return request({
    url: '/api/template/batch',
    method: 'delete',
    data: ids
  })
}

// 审核模板
export function auditTemplate(id, auditStatus, auditRemark) {
  return request({
    url: `/api/template/${id}/audit`,
    method: 'put',
    params: { auditStatus, auditRemark }
  })
}

// 更新模板状态
export function updateTemplateStatus(id, status) {
  return request({
    url: `/api/template/${id}/status`,
    method: 'put',
    params: { status }
  })
}

// 根据模板类型查询模板列表
export function getTemplatesByType(templateType) {
  return request({
    url: `/api/template/type/${templateType}`,
    method: 'get'
  })
}

// 检查模板编码是否存在
export function checkTemplateCodeExists(templateCode, excludeId) {
  return request({
    url: '/api/template/check-code',
    method: 'get',
    params: { templateCode, excludeId }
  })
}

// 复制模板
export function copyTemplate(id, newTemplateCode, newTemplateName) {
  return request({
    url: `/api/template/${id}/copy`,
    method: 'post',
    params: { newTemplateCode, newTemplateName }
  })
}

// 渲染模板内容
export function renderTemplate(templateCode, parameters) {
  return request({
    url: `/api/template/render/${templateCode}`,
    method: 'post',
    data: parameters
  })
}

// 验证模板参数
export function validateTemplateParameters(templateCode, parameters) {
  return request({
    url: `/api/template/validate/${templateCode}`,
    method: 'post',
    data: parameters
  })
}

// 获取模板统计信息
export function getTemplateStatistics() {
  return request({
    url: '/api/template/statistics',
    method: 'get'
  })
}

// 兼容性函数 - 分页查询邮件模板
export function getEmailTemplatePage(params) {
  return getTemplatePage({ ...params, templateType: 2 })
}

// 兼容性函数 - 分页查询短信模板
export function getSmsTemplatePage(params) {
  return getTemplatePage({ ...params, templateType: 1 })
}

// 兼容性函数 - 根据ID获取邮件模板
export function getEmailTemplateById(id) {
  return getTemplateById(id)
}

// 兼容性函数 - 根据ID获取短信模板
export function getSmsTemplateById(id) {
  return getTemplateById(id)
}

// 兼容性函数 - 创建邮件模板
export function createEmailTemplate(data) {
  return createTemplate({ ...data, templateType: 2 })
}

// 兼容性函数 - 创建短信模板
export function createSmsTemplate(data) {
  return createTemplate({ ...data, templateType: 1 })
}

// 兼容性函数 - 更新邮件模板
export function updateEmailTemplate(id, data) {
  return updateTemplate(id, { ...data, templateType: 2 })
}

// 兼容性函数 - 更新短信模板
export function updateSmsTemplate(id, data) {
  return updateTemplate(id, { ...data, templateType: 1 })
}

// 渲染短信模板
export function renderSmsTemplate(templateCode, parameters) {
  return request({
    url: `/api/template-render/sms/${templateCode}`,
    method: 'post',
    data: parameters
  })
}

// 渲染邮件模板
export function renderEmailTemplate(templateCode, parameters) {
  return request({
    url: `/api/template-render/email/${templateCode}`,
    method: 'post',
    data: parameters
  })
}

// 解析模板参数
export function parseTemplateParameters(content) {
  return request({
    url: '/api/template-render/parse-parameters',
    method: 'post',
    data: { content }
  })
}