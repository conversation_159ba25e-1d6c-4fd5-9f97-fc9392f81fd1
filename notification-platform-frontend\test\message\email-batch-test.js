/**
 * T071-T080: 邮件批量发送功能测试
 * 基于需求文档中的批量邮件发送功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class EmailBatchTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T071: 邮件批量发送页面加载测试
   */
  async testT071_EmailBatchPageLoad() {
    const testId = 'T071';
    console.log(`\n🧪 执行测试 ${testId}: 邮件批量发送页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到邮件批量发送页面
      await this.testHelper.navigateTo('/message/email/batch');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isFileUploadVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.fileUpload);
      const isEmailListVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.emailListTextarea);
      const isSubjectInputVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.subjectInput);
      const isContentTextareaVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.contentTextarea);
      const isSendButtonVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.sendButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '邮件批量发送页面加载测试',
        testContent: '验证邮件批量发送页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的批量邮件发送界面',
        testInput: '访问邮件批量发送页面URL: /message/email/batch',
        expectedOutput: '页面正常加载，显示文件上传、邮箱列表、主题、内容和发送按钮',
        actualOutput: `文件上传: ${isFileUploadVisible ? '✅显示' : '❌隐藏'}, 邮箱列表: ${isEmailListVisible ? '✅显示' : '❌隐藏'}, 主题输入: ${isSubjectInputVisible ? '✅显示' : '❌隐藏'}, 内容输入: ${isContentTextareaVisible ? '✅显示' : '❌隐藏'}, 发送按钮: ${isSendButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isFileUploadVisible && isEmailListVisible && isSubjectInputVisible && isContentTextareaVisible && isSendButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '邮件批量发送页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T072: 手动输入邮箱批量发送测试
   */
  async testT072_ManualEmailListSend() {
    const testId = 'T072';
    console.log(`\n🧪 执行测试 ${testId}: 手动输入邮箱批量发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件批量发送页面
      await this.testHelper.navigateTo('/message/email/batch');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 手动输入邮箱列表
      const emailList = testData.email.batchEmails.join('\n');
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, emailList);
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, testData.email.subject);
      await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送批量邮件
      await this.testHelper.page.click(selectors.emailBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '手动输入邮箱批量发送测试',
        testContent: '通过手动输入邮箱列表进行批量邮件发送',
        testPurpose: '验证手动输入方式的批量邮件发送功能',
        testInput: `邮箱列表: ${testData.email.batchEmails.join(', ')}, 主题: ${testData.email.subject}, 内容: ${testData.email.content}`,
        expectedOutput: '批量发送任务提交成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '手动输入邮箱批量发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T073: HTML格式邮件批量发送测试
   */
  async testT073_HtmlEmailBatchSend() {
    const testId = 'T073';
    console.log(`\n🧪 执行测试 ${testId}: HTML格式邮件批量发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件批量发送页面
      await this.testHelper.navigateTo('/message/email/batch');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写HTML格式邮件信息
      const emailList = testData.email.batchEmails.slice(0, 2).join('\n'); // 使用前2个邮箱
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, emailList);
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, testData.email.subject);
      
      // 尝试使用HTML编辑器或直接填写HTML内容
      try {
        await this.testHelper.page.fill(selectors.emailBatch.htmlEditor, testData.email.htmlContent);
      } catch (htmlError) {
        // 如果没有HTML编辑器，使用普通文本框
        await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, testData.email.htmlContent);
      }
      
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送HTML格式批量邮件
      await this.testHelper.page.click(selectors.emailBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: 'HTML格式邮件批量发送测试',
        testContent: '发送包含HTML格式内容的批量邮件',
        testPurpose: '验证系统支持HTML格式的批量邮件发送',
        testInput: `邮箱数量: ${emailList.split('\n').length}, HTML内容: ${testData.email.htmlContent.substring(0, 50)}...`,
        expectedOutput: 'HTML格式批量邮件发送成功，显示成功提示',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: 'HTML格式邮件批量发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T074: 邮件预览功能测试
   */
  async testT074_EmailPreviewFunction() {
    const testId = 'T074';
    console.log(`\n🧪 执行测试 ${testId}: 邮件预览功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件批量发送页面
      await this.testHelper.navigateTo('/message/email/batch');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写批量发送信息
      const emailList = testData.email.batchEmails.join('\n');
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, emailList);
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, testData.email.subject);
      await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 点击预览按钮
      try {
        await this.testHelper.page.click(selectors.emailBatch.previewButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('邮件预览对话框');
        
        // 验证预览对话框是否显示
        const isPreviewVisible = await this.testHelper.verifyElementVisibility(selectors.common.modal);
        
        const result = {
          testId: testId,
          testName: '邮件预览功能测试',
          testContent: '测试批量邮件发送的预览功能',
          testPurpose: '验证用户能够预览批量邮件的内容和目标',
          testInput: `邮箱数量: ${testData.email.batchEmails.length}, 主题: ${testData.email.subject}`,
          expectedOutput: '显示预览对话框，展示邮件详情',
          actualOutput: `预览对话框: ${isPreviewVisible ? '✅显示' : '❌隐藏'}`,
          result: isPreviewVisible ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (previewError) {
        // 如果没有预览按钮，标记为跳过
        const result = {
          testId: testId,
          testName: '邮件预览功能测试',
          testContent: '测试批量邮件发送的预览功能',
          testPurpose: '验证用户能够预览批量邮件的内容和目标',
          testInput: '查找预览功能',
          expectedOutput: '找到预览按钮并显示预览内容',
          actualOutput: '未找到预览功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到预览功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '邮件预览功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T075: 无效邮箱批量发送测试
   */
  async testT075_InvalidEmailBatch() {
    const testId = 'T075';
    console.log(`\n🧪 执行测试 ${testId}: 无效邮箱批量发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到邮件批量发送页面
      await this.testHelper.navigateTo('/message/email/batch');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 输入包含无效邮箱的列表
      const invalidEmailList = [
        testData.email.validEmail,
        testData.email.invalidEmail,
        'invalid-email-2',
        'test@',
        '@example.com'
      ].join('\n');
      
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, invalidEmailList);
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, testData.email.subject);
      await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, testData.email.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送批量邮件
      await this.testHelper.page.click(selectors.emailBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '无效邮箱批量发送测试',
        testContent: '使用包含无效邮箱的列表进行批量发送',
        testPurpose: '验证系统对无效邮箱的批量验证机制',
        testInput: `邮箱列表包含无效邮箱: ${invalidEmailList.replace(/\n/g, ', ')}`,
        expectedOutput: '发送失败或过滤无效邮箱，显示相应提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '无效邮箱批量发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有邮件批量发送测试
   */
  async runAllTests() {
    console.log('🚀 开始执行邮件批量发送功能测试套件 (T071-T075)');
    
    const startTime = Date.now();
    
    await this.testT071_EmailBatchPageLoad();
    await this.testT072_ManualEmailListSend();
    await this.testT073_HtmlEmailBatchSend();
    await this.testT074_EmailPreviewFunction();
    await this.testT075_InvalidEmailBatch();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 邮件批量发送功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const emailBatchTest = new EmailBatchTest();
  emailBatchTest.runAllTests().catch(console.error);
}

module.exports = EmailBatchTest;
