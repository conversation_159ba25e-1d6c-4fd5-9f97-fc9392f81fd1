/**
 * T126-T130: 消息队列管理功能测试
 * 基于需求文档中的消息队列管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class MessageQueueTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T126: 消息队列监控页面加载测试
   */
  async testT126_MessageQueueMonitorPageLoad() {
    const testId = 'T126';
    console.log(`\n🧪 执行测试 ${testId}: 消息队列监控页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到消息队列监控页面
      await this.testHelper.navigateTo('/channel/message-queue');
      await this.testHelper.waitForPageLoad(selectors.messageQueue.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isQueueListVisible = await this.testHelper.verifyElementVisibility(selectors.messageQueue.queueList);
      const isQueueStatsVisible = await this.testHelper.verifyElementVisibility(selectors.messageQueue.queueStats);
      const isMessageListVisible = await this.testHelper.verifyElementVisibility(selectors.messageQueue.messageList);
      const isControlPanelVisible = await this.testHelper.verifyElementVisibility(selectors.messageQueue.controlPanel);
      const isRefreshButtonVisible = await this.testHelper.verifyElementVisibility(selectors.messageQueue.refreshButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '消息队列监控页面加载测试',
        testContent: '验证消息队列监控页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的消息队列监控界面',
        testInput: '访问消息队列监控页面URL: /channel/message-queue',
        expectedOutput: '页面正常加载，显示队列列表、队列统计、消息列表、控制面板和刷新按钮',
        actualOutput: `队列列表: ${isQueueListVisible ? '✅显示' : '❌隐藏'}, 队列统计: ${isQueueStatsVisible ? '✅显示' : '❌隐藏'}, 消息列表: ${isMessageListVisible ? '✅显示' : '❌隐藏'}, 控制面板: ${isControlPanelVisible ? '✅显示' : '❌隐藏'}, 刷新按钮: ${isRefreshButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isQueueListVisible || isQueueStatsVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '消息队列监控页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T127: 队列状态监控测试
   */
  async testT127_QueueStatusMonitoring() {
    const testId = 'T127';
    console.log(`\n🧪 执行测试 ${testId}: 队列状态监控测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到消息队列监控页面
      await this.testHelper.navigateTo('/channel/message-queue');
      await this.testHelper.waitForPageLoad(selectors.messageQueue.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 监控队列状态
      try {
        // 获取队列统计信息
        const queueStats = await this.testHelper.getElementText(selectors.messageQueue.queueStats);
        
        // 刷新队列状态
        await this.testHelper.page.click(selectors.messageQueue.refreshButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeCustomScreenshot('刷新队列状态');
        
        // 查看不同队列的状态
        const queueTypes = [
          '.queue-item[data-type="sms"]',
          '.queue-item[data-type="email"]',
          '.queue-item[data-type="push"]'
        ];
        
        for (const queueType of queueTypes) {
          try {
            await this.testHelper.page.click(queueType);
            await this.testHelper.wait(testData.timeouts.short);
            await this.screenshotHelper.takeCustomScreenshot(`查看${queueType}队列`);
          } catch (queueError) {
            // 如果队列类型不存在，跳过
          }
        }
        
        // 获取队列详细信息
        const queueDetails = await this.testHelper.getTableData(selectors.messageQueue.queueList);
        
        const result = {
          testId: testId,
          testName: '队列状态监控测试',
          testContent: '监控消息队列的状态信息',
          testPurpose: '验证队列状态监控功能能够正常工作',
          testInput: '刷新队列状态，查看不同类型队列',
          expectedOutput: '显示队列统计信息和详细状态',
          actualOutput: `队列统计: ${queueStats ? '有数据' : '无数据'}, 队列详情: ${queueDetails.length}个队列`,
          result: queueStats || queueDetails.length > 0 ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (monitorError) {
        // 如果没有监控功能，标记为跳过
        const result = {
          testId: testId,
          testName: '队列状态监控测试',
          testContent: '监控消息队列的状态信息',
          testPurpose: '验证队列状态监控功能能够正常工作',
          testInput: '查找队列状态监控功能',
          expectedOutput: '找到监控功能并显示状态',
          actualOutput: '未找到队列状态监控功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到队列状态监控功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '队列状态监控测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T128: 消息重试机制测试
   */
  async testT128_MessageRetryMechanism() {
    const testId = 'T128';
    console.log(`\n🧪 执行测试 ${testId}: 消息重试机制测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到消息队列监控页面
      await this.testHelper.navigateTo('/channel/message-queue');
      await this.testHelper.waitForPageLoad(selectors.messageQueue.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 测试消息重试
      try {
        // 查找失败的消息
        const failedMessages = await this.testHelper.page.$$('.message-item[data-status="FAILED"]');
        
        if (failedMessages.length > 0) {
          // 选择第一个失败的消息
          await this.testHelper.page.click('.message-item[data-status="FAILED"]:first-child');
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('选择失败消息');
          
          // 点击重试按钮
          await this.testHelper.page.click(selectors.messageQueue.retryButton);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('重试确认对话框');
          
          // 确认重试
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          // 获取重试结果
          const successMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '消息重试机制测试',
            testContent: '重试失败的消息',
            testPurpose: '验证消息重试机制能够正常工作',
            testInput: `重试失败消息，失败消息数量: ${failedMessages.length}`,
            expectedOutput: '消息重试成功，显示成功提示信息',
            actualOutput: `成功消息: ${successMessage || '无'}`,
            result: successMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } else {
          // 没有失败消息，创建测试场景
          const result = {
            testId: testId,
            testName: '消息重试机制测试',
            testContent: '重试失败的消息',
            testPurpose: '验证消息重试机制能够正常工作',
            testInput: '查找失败消息进行重试',
            expectedOutput: '找到失败消息并成功重试',
            actualOutput: '当前没有失败消息可供重试',
            result: 'SKIPPED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`⏭️ 测试 ${testId} 跳过: 没有失败消息可供重试`);
        }
        
      } catch (retryError) {
        // 如果没有重试功能，标记为跳过
        const result = {
          testId: testId,
          testName: '消息重试机制测试',
          testContent: '重试失败的消息',
          testPurpose: '验证消息重试机制能够正常工作',
          testInput: '查找消息重试功能',
          expectedOutput: '找到重试功能并成功执行',
          actualOutput: '未找到消息重试功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到消息重试功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '消息重试机制测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T129: 队列配置管理测试
   */
  async testT129_QueueConfigManagement() {
    const testId = 'T129';
    console.log(`\n🧪 执行测试 ${testId}: 队列配置管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到消息队列监控页面
      await this.testHelper.navigateTo('/channel/message-queue');
      await this.testHelper.waitForPageLoad(selectors.messageQueue.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 配置队列参数
      try {
        // 点击配置按钮
        await this.testHelper.page.click(selectors.messageQueue.configButton);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('队列配置对话框');
        
        // 配置队列参数
        const queueConfig = {
          maxRetries: '3',
          retryInterval: '60',
          maxQueueSize: '10000',
          consumerCount: '5',
          batchSize: '100'
        };
        
        await this.testHelper.page.fill(selectors.messageQueue.maxRetriesInput, queueConfig.maxRetries);
        await this.testHelper.page.fill(selectors.messageQueue.retryIntervalInput, queueConfig.retryInterval);
        await this.testHelper.page.fill(selectors.messageQueue.maxQueueSizeInput, queueConfig.maxQueueSize);
        await this.testHelper.page.fill(selectors.messageQueue.consumerCountInput, queueConfig.consumerCount);
        await this.testHelper.page.fill(selectors.messageQueue.batchSizeInput, queueConfig.batchSize);
        
        // 启用死信队列
        try {
          await this.testHelper.page.check(selectors.messageQueue.enableDeadLetterCheckbox);
        } catch (checkboxError) {
          // 如果没有复选框，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存配置
        await this.testHelper.page.click(selectors.messageQueue.saveConfigButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '队列配置管理测试',
          testContent: '配置消息队列参数',
          testPurpose: '验证队列配置管理功能能够正常工作',
          testInput: `最大重试: ${queueConfig.maxRetries}, 重试间隔: ${queueConfig.retryInterval}秒, 队列大小: ${queueConfig.maxQueueSize}`,
          expectedOutput: '队列配置保存成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (configError) {
        // 如果没有配置功能，标记为跳过
        const result = {
          testId: testId,
          testName: '队列配置管理测试',
          testContent: '配置消息队列参数',
          testPurpose: '验证队列配置管理功能能够正常工作',
          testInput: '查找队列配置管理功能',
          expectedOutput: '找到配置功能并成功设置',
          actualOutput: '未找到队列配置管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到队列配置管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '队列配置管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T130: 死信队列处理测试
   */
  async testT130_DeadLetterQueueHandling() {
    const testId = 'T130';
    console.log(`\n🧪 执行测试 ${testId}: 死信队列处理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到消息队列监控页面
      await this.testHelper.navigateTo('/channel/message-queue');
      await this.testHelper.waitForPageLoad(selectors.messageQueue.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 处理死信队列
      try {
        // 切换到死信队列视图
        await this.testHelper.page.click(selectors.messageQueue.deadLetterQueueTab);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('死信队列视图');
        
        // 获取死信消息列表
        const deadLetterMessages = await this.testHelper.getTableData(selectors.messageQueue.deadLetterMessageList);
        
        if (deadLetterMessages.length > 0) {
          // 查看死信消息详情
          await this.testHelper.page.click(`${selectors.messageQueue.deadLetterMessageList} tr:first-child`);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('死信消息详情');
          
          // 重新入队
          try {
            await this.testHelper.page.click(selectors.messageQueue.requeueButton);
            await this.testHelper.wait(testData.timeouts.short);
            await this.screenshotHelper.takeCustomScreenshot('重新入队确认');
            
            // 确认重新入队
            await this.testHelper.page.click(selectors.common.confirmButton);
            await this.testHelper.wait(testData.timeouts.medium);
            await this.screenshotHelper.takeAfterSubmitScreenshot();
          } catch (requeueError) {
            // 如果没有重新入队功能，跳过
          }
          
          // 删除死信消息
          try {
            await this.testHelper.page.click(selectors.messageQueue.deleteDeadLetterButton);
            await this.testHelper.wait(testData.timeouts.short);
            await this.screenshotHelper.takeCustomScreenshot('删除死信消息确认');
            
            // 确认删除
            await this.testHelper.page.click(selectors.common.confirmButton);
            await this.testHelper.wait(testData.timeouts.medium);
          } catch (deleteError) {
            // 如果没有删除功能，跳过
          }
        }
        
        // 获取操作结果
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '死信队列处理测试',
          testContent: '处理死信队列中的消息',
          testPurpose: '验证死信队列处理功能能够正常工作',
          testInput: `死信消息数量: ${deadLetterMessages.length}`,
          expectedOutput: '死信队列处理操作成功执行',
          actualOutput: `操作结果: ${successMessage || '操作完成'}`,
          result: deadLetterMessages.length >= 0 ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (deadLetterError) {
        // 如果没有死信队列功能，标记为跳过
        const result = {
          testId: testId,
          testName: '死信队列处理测试',
          testContent: '处理死信队列中的消息',
          testPurpose: '验证死信队列处理功能能够正常工作',
          testInput: '查找死信队列处理功能',
          expectedOutput: '找到死信队列并成功处理',
          actualOutput: '未找到死信队列处理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到死信队列处理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '死信队列处理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有消息队列管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行消息队列管理功能测试套件 (T126-T130)');
    
    const startTime = Date.now();
    
    await this.testT126_MessageQueueMonitorPageLoad();
    await this.testT127_QueueStatusMonitoring();
    await this.testT128_MessageRetryMechanism();
    await this.testT129_QueueConfigManagement();
    await this.testT130_DeadLetterQueueHandling();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 消息队列管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const messageQueueTest = new MessageQueueTest();
  messageQueueTest.runAllTests().catch(console.error);
}

module.exports = MessageQueueTest;
