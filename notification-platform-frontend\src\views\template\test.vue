<template>
  <div class="template-test-container">
    <el-card>
      <template #header>
        <span>模板API测试</span>
      </template>
      
      <el-button type="primary" @click="testGetTemplatePage">测试分页查询</el-button>
      <el-button type="success" @click="testGetTemplateById">测试根据ID查询</el-button>
      <el-button type="warning" @click="testGetTemplateByCode">测试根据编码查询</el-button>
      
      <div v-if="result" style="margin-top: 20px;">
        <h3>测试结果：</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getTemplatePage, getTemplateById, getTemplateByCode } from '@/api/template'

const result = ref(null)

const testGetTemplatePage = async () => {
  try {
    const response = await getTemplatePage({
      current: 1,
      size: 10
    })
    result.value = response
    ElMessage.success('分页查询测试成功')
  } catch (error) {
    result.value = error
    ElMessage.error('分页查询测试失败')
  }
}

const testGetTemplateById = async () => {
  try {
    const response = await getTemplateById(1)
    result.value = response
    ElMessage.success('根据ID查询测试成功')
  } catch (error) {
    result.value = error
    ElMessage.error('根据ID查询测试失败')
  }
}

const testGetTemplateByCode = async () => {
  try {
    const response = await getTemplateByCode('TEST_001')
    result.value = response
    ElMessage.success('根据编码查询测试成功')
  } catch (error) {
    result.value = error
    ElMessage.error('根据编码查询测试失败')
  }
}
</script>

<style scoped>
.template-test-container {
  padding: 20px;
}

pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>