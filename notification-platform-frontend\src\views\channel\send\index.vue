<template>
  <div class="send-channel">
    <div class="page-header">
      <h2>发送渠道</h2>
      <div class="actions">
        <el-button 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增发送渠道
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="渠道名称">
          <el-input v-model="searchForm.channelName" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="渠道类型">
          <el-select v-model="searchForm.channelType" placeholder="请选择渠道类型">
            <el-option label="短信" :value="1" />
            <el-option label="邮件" :value="2" />
            <el-option label="推送" :value="3" />
            <el-option label="语音" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="启用" :value="1" />
            <el-option label="禁用" :value="0" />
            <el-option label="维护" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <div style="margin-bottom: 10px; color: #666; font-size: 12px;">
        数据条数: {{ tableData.length }} | 总数: {{ pagination.total }}
      </div>
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="channelName" label="渠道名称" />
        <el-table-column prop="channelCode" label="渠道编码" />
        <el-table-column prop="channelType" label="渠道类型">
          <template #default="{ row }">
            <el-tag :type="getChannelTypeTag(row.channelType)">
              {{ getChannelTypeName(row.channelType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="providerName" label="服务商" />
        <el-table-column prop="dailyLimit" label="日发送限制" />
        <el-table-column prop="priority" label="优先级" width="80" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 编辑弹窗 -->
    <SendChannelForm
      :visible="formVisible"
      :is-edit="isEdit"
      :data="currentRow"
      @close="handleFormClose"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserInfo } from '@/utils/auth';
import { getPageButtonPermissions } from '@/utils/permission-buttons';
import { getSendChannelPage, deleteSendChannel } from '@/api/channel';
import SendChannelForm from './components/SendChannelForm.vue';

// 权限控制
const userInfo = getUserInfo();
const permissions = computed(() => {
  return getPageButtonPermissions('channel:send', userInfo?.permissions || []);
});

// 数据定义
const loading = ref(false);
const tableData = ref([]);

// 搜索表单
const searchForm = reactive({
  channelName: '',
  channelType: '',
  status: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 表单相关
const formVisible = ref(false);
const isEdit = ref(false);
const currentRow = ref(null);

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      channelName: searchForm.channelName || undefined,
      channelType: searchForm.channelType || undefined,
      status: searchForm.status || undefined
    };
    
    const response = await getSendChannelPage(params);
    
    if (response.code === 200) {
      tableData.value = response.data.records || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '查询失败');
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getChannelTypeName = (type) => {
  const typeMap = { 1: '短信', 2: '邮件', 3: '推送', 4: '语音' };
  return typeMap[type] || '未知';
};

const getChannelTypeTag = (type) => {
  const tagMap = { 1: 'primary', 2: 'success', 3: 'warning', 4: 'info' };
  return tagMap[type] || '';
};

const getStatusName = (status) => {
  const statusMap = { 1: '启用', 0: '禁用', 2: '维护' };
  return statusMap[status] || '未知';
};

const getStatusTag = (status) => {
  const tagMap = { 1: 'success', 0: 'danger', 2: 'warning' };
  return tagMap[status] || '';
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    channelName: '',
    channelType: '',
    status: ''
  });
  handleSearch();
};

const handleAdd = () => {
  isEdit.value = false;
  currentRow.value = null;
  formVisible.value = true;
};

const handleView = (row) => {
  ElMessage.info(`查看发送渠道: ${row.channelName}`);
};

const handleEdit = (row) => {
  isEdit.value = true;
  currentRow.value = { ...row };
  formVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除发送渠道"${row.channelName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const response = await deleteSendChannel(row.id);
    
    if (response.code === 200) {
      ElMessage.success('删除成功');
      loadData();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }
};

const handleFormClose = () => {
  formVisible.value = false;
  currentRow.value = null;
};

const handleFormSuccess = () => {
  loadData();
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
  
  // 如果没有数据，添加测试数据
  setTimeout(() => {
    if (tableData.value.length === 0) {
      tableData.value = [
        {
          id: 1,
          channelCode: 'SMS_001',
          channelName: '测试短信渠道',
          channelType: 1,
          providerName: '阿里云',
          dailyLimit: 10000,
          priority: 1,
          status: 1
        },
        {
          id: 2,
          channelCode: 'EMAIL_001',
          channelName: '测试邮件渠道',
          channelType: 2,
          providerName: '腾讯云',
          dailyLimit: 5000,
          priority: 2,
          status: 1
        }
      ];
      pagination.total = 2;
    }
  }, 1000);
});
</script>

<style scoped>
.send-channel {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.danger {
  color: #f56c6c;
}
</style>