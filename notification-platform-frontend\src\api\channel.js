import request from '@/utils/request'

/**
 * 根据渠道类型获取启用的发送渠道
 * @param {Number} channelType 渠道类型：1-短信，2-邮件
 */
export function getSendChannelsByType(channelType) {
  return request({
    url: `/api/send-channels/type/${channelType}`,
    method: 'get'
  })
}

/**
 * 获取所有启用的发送渠道
 */
export function getAllEnabledSendChannels() {
  return request({
    url: '/api/send-channels/all',
    method: 'get'
  })
}

/**
 * 分页查询发送渠道
 * @param {Object} params 查询参数
 */
export function getSendChannelPage(params) {
  return request({
    url: '/api/send-channels/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取发送渠道
 * @param {Number} id 渠道ID
 */
export function getSendChannelById(id) {
  return request({
    url: `/api/send-channels/${id}`,
    method: 'get'
  })
}

/**
 * 创建发送渠道
 * @param {Object} data 渠道数据
 */
export function createSendChannel(data) {
  return request({
    url: '/api/send-channels',
    method: 'post',
    data
  })
}

/**
 * 更新发送渠道
 * @param {Number} id 渠道ID
 * @param {Object} data 渠道数据
 */
export function updateSendChannel(id, data) {
  return request({
    url: `/api/send-channels/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除发送渠道
 * @param {Number} id 渠道ID
 */
export function deleteSendChannel(id) {
  return request({
    url: `/api/send-channels/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除发送渠道
 * @param {Array} ids 渠道ID列表
 */
export function batchDeleteSendChannels(ids) {
  return request({
    url: '/api/send-channels/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 更新发送渠道状态
 * @param {Number} id 渠道ID
 * @param {Number} status 状态：1-启用，0-禁用，2-维护
 */
export function updateSendChannelStatus(id, status) {
  return request({
    url: `/api/send-channels/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 测试渠道连接
 * @param {Number} id 渠道ID
 */
export function testChannelConnection(id) {
  return request({
    url: `/api/send-channels/${id}/test`,
    method: 'post'
  })
}

/**
 * 获取最优发送渠道
 * @param {Number} channelType 渠道类型
 */
export function getOptimalSendChannel(channelType) {
  return request({
    url: `/api/send-channels/optimal/${channelType}`,
    method: 'get'
  })
}

// 接入渠道相关API
/**
 * 分页查询接入渠道
 * @param {Object} params 查询参数
 */
export function getAccessChannelPage(params) {
  return request({
    url: '/api/access-channels/page',
    method: 'get',
    params
  })
}

/**
 * 创建接入渠道
 * @param {Object} data 渠道数据
 */
export function createAccessChannel(data) {
  return request({
    url: '/api/access-channels',
    method: 'post',
    data
  })
}

/**
 * 更新接入渠道
 * @param {Number} id 渠道ID
 * @param {Object} data 渠道数据
 */
export function updateAccessChannel(id, data) {
  return request({
    url: `/api/access-channels/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除接入渠道
 * @param {Number} id 渠道ID
 */
export function deleteAccessChannel(id) {
  return request({
    url: `/api/access-channels/${id}`,
    method: 'delete'
  })
}