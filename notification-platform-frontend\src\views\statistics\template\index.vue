<template>
  <div class="template-statistics">
    <div class="page-header">
      <h2>按模板统计</h2>
      <div class="actions">
        <el-button @click="handleExport" icon="Download">导出报表</el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板名称">
          <el-input v-model="searchForm.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="消息类型">
          <el-select v-model="searchForm.messageType" placeholder="请选择消息类型">
            <el-option label="短信" :value="1" />
            <el-option label="邮件" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ totalTemplates }}</div>
            <div class="stat-label">模板总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ totalSent }}</div>
            <div class="stat-label">总发送量</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ activeTemplates }}</div>
            <div class="stat-label">活跃模板</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="templateCode" label="模板编码" />
        <el-table-column prop="templateType" label="模板类型">
          <template #default="{ row }">
            <el-tag :type="row.templateType === 'SMS' ? 'primary' : 'success'">
              {{ row.templateType === 'SMS' ? '短信' : '邮件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="totalSent" label="发送总量" />
        <el-table-column prop="successCount" label="成功数量" />
        <el-table-column prop="failCount" label="失败数量" />
        <el-table-column prop="successRate" label="成功率">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.successRate" 
              :color="getSuccessRateColor(row.successRate)"
              :show-text="true"
            />
          </template>
        </el-table-column>
        <el-table-column prop="lastUsedTime" label="最后使用时间" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              type="text" 
              size="small" 
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getMessageStatistics } from '@/api/statistics';
import { ExcelExporter } from '@/utils/excel-export';

// 数据定义
const loading = ref(false);
const tableData = ref([]);
const totalTemplates = ref(0);
const totalSent = ref(0);
const successRate = ref(0);
const activeTemplates = ref(0);

// 搜索表单
const searchForm = reactive({
  templateName: '',
  messageType: 1,
  dateRange: []
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      messageType: searchForm.messageType
    };
    
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startTime = searchForm.dateRange[0];
      params.endTime = searchForm.dateRange[1];
    }
    
    const response = await getMessageStatistics(params);
    const data = response.data;
    
    // 设置模板统计数据
    tableData.value = (data.templateStats || []).map(item => ({
      templateName: item.templateName,
      templateCode: item.templateCode,
      templateType: searchForm.messageType === 1 ? 'SMS' : 'EMAIL',
      totalSent: item.totalCount,
      successCount: item.successCount,
      failCount: item.failedCount,
      successRate: item.successRate,
      lastUsedTime: '-'
    }));
    
    // 设置统计数据
    const overall = data.overall || {};
    totalTemplates.value = overall.templateCount || 0;
    totalSent.value = overall.totalCount || 0;
    successRate.value = overall.successRate || 0;
    activeTemplates.value = tableData.value.length;
    
    pagination.total = tableData.value.length;
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getSuccessRateColor = (rate) => {
  if (rate >= 95) return '#67c23a';
  if (rate >= 85) return '#e6a23c';
  return '#f56c6c';
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    templateName: '',
    messageType: 1,
    dateRange: []
  });
  handleSearch();
};

const handleExport = async () => {
  try {
    if (tableData.value.length === 0) {
      ElMessage.warning('暂无数据可导出');
      return;
    }
    
    // 定义导出列配置
    const columns = [
      { prop: 'templateName', label: '模板名称', width: 200 },
      { prop: 'templateCode', label: '模板编码', width: 150 },
      { 
        prop: 'templateType', 
        label: '模板类型', 
        width: 100,
        formatter: (value) => value === 'SMS' ? '短信' : '邮件'
      },
      { prop: 'totalSent', label: '发送总量', width: 100 },
      { prop: 'successCount', label: '成功数量', width: 100 },
      { prop: 'failCount', label: '失败数量', width: 100 },
      { 
        prop: 'successRate', 
        label: '成功率', 
        width: 100,
        formatter: (value) => `${value}%`
      },
      { prop: 'lastUsedTime', label: '最后使用时间', width: 180 }
    ];
    
    // 汇总信息
    const summary = {
      '模板总数': totalTemplates.value,
      '总发送量': totalSent.value,
      '成功率': `${successRate.value}%`,
      '活跃模板': activeTemplates.value,
      '导出时间': new Date().toLocaleString('zh-CN'),
      '查询条件': `消息类型: ${searchForm.messageType === 1 ? '短信' : '邮件'}${searchForm.templateName ? ', 模板名称: ' + searchForm.templateName : ''}${searchForm.dateRange && searchForm.dateRange.length ? ', 时间范围: ' + searchForm.dateRange.join(' 至 ') : ''}`
    };
    
    // 导出Excel
    await ExcelExporter.exportStatisticsReport(
      summary,
      tableData.value,
      columns,
      'template_statistics',
      '模板统计报表'
    );
    
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败: ' + error.message);
  }
};

const handleViewDetail = (row) => {
  ElMessage.info(`查看模板详情: ${row.templateName}`);
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.template-statistics {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}
</style>