import request from '@/utils/request'

// ==================== 黑名单管理 ====================

/**
 * 分页查询黑名单
 * @param {Object} params 查询参数
 */
export function getBlacklistPage(params) {
  return request({
    url: '/api/security/blacklist/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取黑名单详情
 * @param {Number} id 黑名单ID
 */
export function getBlacklistById(id) {
  return request({
    url: `/api/security/blacklist/${id}`,
    method: 'get'
  })
}

/**
 * 新增黑名单
 * @param {Object} data 黑名单数据
 */
export function createBlacklist(data) {
  return request({
    url: '/api/security/blacklist',
    method: 'post',
    data
  })
}

/**
 * 更新黑名单
 * @param {Object} data 黑名单数据
 */
export function updateBlacklist(data) {
  return request({
    url: '/api/security/blacklist',
    method: 'put',
    data
  })
}

/**
 * 删除黑名单
 * @param {Number} id 黑名单ID
 */
export function deleteBlacklist(id) {
  return request({
    url: `/api/security/blacklist/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除黑名单
 * @param {Array} ids 黑名单ID列表
 */
export function batchDeleteBlacklist(ids) {
  return request({
    url: '/api/security/blacklist/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 更新黑名单状态
 * @param {Number} id 黑名单ID
 * @param {Number} status 状态
 */
export function updateBlacklistStatus(id, status) {
  return request({
    url: `/api/security/blacklist/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 检查黑名单
 * @param {Number} blacklistType 黑名单类型
 * @param {String} blacklistValue 黑名单值
 */
export function checkBlacklist(blacklistType, blacklistValue) {
  return request({
    url: '/api/security/blacklist/check',
    method: 'get',
    params: { blacklistType, blacklistValue }
  })
}

/**
 * 批量导入黑名单
 * @param {Array} data 黑名单数据列表
 */
export function batchImportBlacklist(data) {
  return request({
    url: '/api/security/blacklist/batch-import',
    method: 'post',
    data
  })
}

/**
 * 获取黑名单统计信息
 */
export function getBlacklistStatistics() {
  return request({
    url: '/api/security/blacklist/statistics',
    method: 'get'
  })
}

// ==================== 白名单管理 ====================

/**
 * 分页查询白名单
 * @param {Object} params 查询参数
 */
export function getWhitelistPage(params) {
  return request({
    url: '/api/security/whitelist/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取白名单详情
 * @param {Number} id 白名单ID
 */
export function getWhitelistById(id) {
  return request({
    url: `/api/security/whitelist/${id}`,
    method: 'get'
  })
}

/**
 * 新增白名单
 * @param {Object} data 白名单数据
 */
export function createWhitelist(data) {
  return request({
    url: '/api/security/whitelist',
    method: 'post',
    data
  })
}

/**
 * 更新白名单
 * @param {Object} data 白名单数据
 */
export function updateWhitelist(data) {
  return request({
    url: '/api/security/whitelist',
    method: 'put',
    data
  })
}

/**
 * 删除白名单
 * @param {Number} id 白名单ID
 */
export function deleteWhitelist(id) {
  return request({
    url: `/api/security/whitelist/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除白名单
 * @param {Array} ids 白名单ID列表
 */
export function batchDeleteWhitelist(ids) {
  return request({
    url: '/api/security/whitelist/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 更新白名单状态
 * @param {Number} id 白名单ID
 * @param {Number} status 状态
 */
export function updateWhitelistStatus(id, status) {
  return request({
    url: `/api/security/whitelist/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 检查白名单
 * @param {Number} whitelistType 白名单类型
 * @param {String} whitelistValue 白名单值
 */
export function checkWhitelist(whitelistType, whitelistValue) {
  return request({
    url: '/api/security/whitelist/check',
    method: 'get',
    params: { whitelistType, whitelistValue }
  })
}

/**
 * 批量导入白名单
 * @param {Array} data 白名单数据列表
 */
export function batchImportWhitelist(data) {
  return request({
    url: '/api/security/whitelist/batch-import',
    method: 'post',
    data
  })
}

/**
 * 获取白名单统计信息
 */
export function getWhitelistStatistics() {
  return request({
    url: '/api/security/whitelist/statistics',
    method: 'get'
  })
}

// ==================== 关键字管理 ====================

/**
 * 分页查询关键字
 * @param {Object} params 查询参数
 */
export function getKeywordPage(params) {
  return request({
    url: '/api/security/keyword/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取关键字详情
 * @param {Number} id 关键字ID
 */
export function getKeywordById(id) {
  return request({
    url: `/api/security/keyword/${id}`,
    method: 'get'
  })
}

/**
 * 新增关键字
 * @param {Object} data 关键字数据
 */
export function createKeyword(data) {
  return request({
    url: '/api/security/keyword',
    method: 'post',
    data
  })
}

/**
 * 更新关键字
 * @param {Object} data 关键字数据
 */
export function updateKeyword(data) {
  return request({
    url: '/api/security/keyword',
    method: 'put',
    data
  })
}

/**
 * 删除关键字
 * @param {Number} id 关键字ID
 */
export function deleteKeyword(id) {
  return request({
    url: `/api/security/keyword/${id}`,
    method: 'delete'
  })
}

/**
 * 批量删除关键字
 * @param {Array} ids 关键字ID列表
 */
export function batchDeleteKeyword(ids) {
  return request({
    url: '/api/security/keyword/batch',
    method: 'delete',
    data: ids
  })
}

/**
 * 更新关键字状态
 * @param {Number} id 关键字ID
 * @param {Number} status 状态
 */
export function updateKeywordStatus(id, status) {
  return request({
    url: `/api/security/keyword/${id}/status`,
    method: 'put',
    params: { status }
  })
}

/**
 * 检查关键字
 * @param {String} content 内容
 */
export function checkContent(content) {
  return request({
    url: '/api/security/keyword/check',
    method: 'post',
    data: content,
    headers: {
      'Content-Type': 'text/plain'
    }
  })
}

/**
 * 批量导入关键字
 * @param {Array} data 关键字数据列表
 */
export function batchImportKeyword(data) {
  return request({
    url: '/api/security/keyword/batch-import',
    method: 'post',
    data
  })
}

/**
 * 获取关键字统计信息
 */
export function getKeywordStatistics() {
  return request({
    url: '/api/security/keyword/statistics',
    method: 'get'
  })
}

/**
 * 刷新关键字缓存
 */
export function refreshKeywordCache() {
  return request({
    url: '/api/security/keyword/refresh-cache',
    method: 'post'
  })
}