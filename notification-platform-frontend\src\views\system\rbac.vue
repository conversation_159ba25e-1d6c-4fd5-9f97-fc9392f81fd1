<template>
  <div class="rbac-container">
    <el-tabs v-model="activeTab" type="card" class="rbac-tabs">
      <!-- 用户管理 -->
      <el-tab-pane label="用户管理" name="users" v-permission="'system:user'">
        <div class="tab-content">
          <el-card class="filter-card">
            <el-form :model="userQuery" :inline="true" @submit.prevent>
              <el-form-item label="用户名:">
                <el-input v-model="userQuery.userName" placeholder="请输入用户名" clearable />
              </el-form-item>
              <el-form-item label="机构:">
                <el-select v-model="userQuery.orgId" placeholder="请选择机构" clearable>
                  <el-option
                    v-for="org in organizationOptions"
                    :key="org.id"
                    :label="org.orgName"
                    :value="org.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getUserList">查询</el-button>
                <el-button @click="resetUserQuery">重置</el-button>
                <el-button type="success" @click="handleAddUser" v-permission="'system:user:add'">新增用户</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <el-card class="table-card">
            <el-table :data="userList" v-loading="userLoading" border>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="userName" label="用户名" />
              <el-table-column prop="loginName" label="登录名" />
              <el-table-column prop="email" label="邮箱" />
              <el-table-column prop="orgName" label="机构" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                    {{ scope.row.status === 1 ? '正常' : '锁定' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="300" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEditUser(scope.row)" v-permission="'system:user:edit'">
                    编辑
                  </el-button>
                  <el-button type="info" size="small" @click="handleUserRoles(scope.row)" v-permission="'system:user:role'">
                    角色
                  </el-button>
                  <el-button type="warning" size="small" @click="handleResetPassword(scope.row)" v-permission="'system:user:reset'">
                    重置密码
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteUser(scope.row)" v-permission="'system:user:delete'">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <Pagination
              v-show="userTotal > 0"
              :total="userTotal"
              v-model:page="userQuery.page"
              v-model:limit="userQuery.size"
              @pagination="getUserList"
            />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 角色管理 -->
      <el-tab-pane label="角色管理" name="roles" v-permission="'system:role'">
        <div class="tab-content">
          <el-card class="filter-card">
            <el-form :model="roleQuery" :inline="true" @submit.prevent>
              <el-form-item label="角色名:">
                <el-input v-model="roleQuery.roleName" placeholder="请输入角色名" clearable />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getRoleList">查询</el-button>
                <el-button @click="resetRoleQuery">重置</el-button>
                <el-button type="success" @click="handleAddRole" v-permission="'system:role:add'">新增角色</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <el-card class="table-card">
            <el-table :data="roleList" v-loading="roleLoading" border>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="roleName" label="角色名" />
              <el-table-column prop="roleKey" label="角色标识" />
              <el-table-column prop="roleSort" label="排序" width="100" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                    {{ scope.row.status === 1 ? '正常' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" show-overflow-tooltip />
              <el-table-column label="操作" width="250" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEditRole(scope.row)" v-permission="'system:role:edit'">
                    编辑
                  </el-button>
                  <el-button type="info" size="small" @click="handleRolePermissions(scope.row)" v-permission="'system:role:permission'">
                    权限
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteRole(scope.row)" v-permission="'system:role:delete'">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <Pagination
              v-show="roleTotal > 0"
              :total="roleTotal"
              v-model:page="roleQuery.page"
              v-model:limit="roleQuery.size"
              @pagination="getRoleList"
            />
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 权限管理 -->
      <el-tab-pane label="权限管理" name="permissions" v-permission="'system:permission'">
        <div class="tab-content">
          <el-card class="filter-card">
            <el-form :model="permissionQuery" :inline="true" @submit.prevent>
              <el-form-item label="权限名称:">
                <el-input v-model="permissionQuery.name" placeholder="请输入权限名称" clearable />
              </el-form-item>
              <el-form-item label="权限类型:">
                <el-select v-model="permissionQuery.type" placeholder="请选择权限类型" clearable>
                  <el-option label="菜单" value="menu" />
                  <el-option label="按钮" value="button" />
                  <el-option label="接口" value="api" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getPermissionList">查询</el-button>
                <el-button @click="resetPermissionQuery">重置</el-button>
                <el-button type="success" @click="handleAddPermission" v-permission="'system:permission:add'">新增权限</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <el-card class="table-card">
            <el-table
              :data="permissionList"
              v-loading="permissionLoading"
              row-key="id"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              border
            >
              <el-table-column prop="name" label="权限名称" width="200" />
              <el-table-column prop="code" label="权限标识" width="200" />
              <el-table-column prop="type" label="权限类型" width="100">
                <template #default="scope">
                  <el-tag :type="getPermissionTypeColor(scope.row.type)">
                    {{ getPermissionTypeText(scope.row.type) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="path" label="路径/URL" show-overflow-tooltip />
              <el-table-column prop="sort" label="排序" width="80" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                    {{ scope.row.status === 1 ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="250" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEditPermission(scope.row)" v-permission="'system:permission:edit'">
                    编辑
                  </el-button>
                  <el-button type="success" size="small" @click="handleAddChildPermission(scope.row)" v-if="scope.row.type === 'menu'" v-permission="'system:permission:add'">
                    新增子权限
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeletePermission(scope.row)" v-permission="'system:permission:delete'">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 机构管理 -->
      <el-tab-pane label="机构管理" name="organizations" v-permission="'system:organization'">
        <div class="tab-content">
          <el-card class="filter-card">
            <el-form :model="orgQuery" :inline="true" @submit.prevent>
              <el-form-item label="机构名称:">
                <el-input v-model="orgQuery.orgName" placeholder="请输入机构名称" clearable />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="getOrganizationList">查询</el-button>
                <el-button @click="resetOrgQuery">重置</el-button>
                <el-button type="success" @click="handleAddOrganization" v-permission="'system:organization:add'">新增机构</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <el-card class="table-card">
            <el-table :data="organizationList" v-loading="orgLoading" border>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="orgName" label="机构名称" />
              <el-table-column prop="orgCode" label="机构编码" />
              <el-table-column prop="orgLevel" label="机构层级" width="100" />
              <el-table-column prop="orgType" label="机构类型" width="100">
                <template #default="scope">
                  <el-tag :type="getOrgTypeColor(scope.row.orgType)">
                    {{ getOrgTypeText(scope.row.orgType) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
                    {{ scope.row.status === 1 ? '正常' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="handleEditOrganization(scope.row)" v-permission="'system:organization:edit'">
                    编辑
                  </el-button>
                  <el-button type="danger" size="small" @click="handleDeleteOrganization(scope.row)" v-permission="'system:organization:delete'">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <Pagination
              v-show="orgTotal > 0"
              :total="orgTotal"
              v-model:page="orgQuery.page"
              v-model:limit="orgQuery.size"
              @pagination="getOrganizationList"
            />
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import Pagination from '@/components/Pagination/index.vue'
import { getUserList as fetchUserList } from '@/api/user'
import { getRoleList as fetchRoleList } from '@/api/role'
import { getPermissionTree } from '@/api/permission'
import { getOrganizationList as fetchOrganizationList, getAllOrganizations } from '@/api/organization'

// 当前激活的标签页
const activeTab = ref('users')

// 用户管理相关数据
const userLoading = ref(false)
const userList = ref([])
const userTotal = ref(0)
const userQuery = reactive({
  page: 1,
  size: 10,
  userName: '',
  orgId: null
})

// 角色管理相关数据
const roleLoading = ref(false)
const roleList = ref([])
const roleTotal = ref(0)
const roleQuery = reactive({
  page: 1,
  size: 10,
  roleName: ''
})

// 权限管理相关数据
const permissionLoading = ref(false)
const permissionList = ref([])
const permissionQuery = reactive({
  name: '',
  type: ''
})

// 机构管理相关数据
const orgLoading = ref(false)
const organizationList = ref([])
const organizationOptions = ref([])
const orgTotal = ref(0)
const orgQuery = reactive({
  page: 1,
  size: 10,
  orgName: ''
})

// 获取用户列表
const getUserList = async () => {
  userLoading.value = true
  try {
    const response = await fetchUserList(userQuery)
    if (response.data.code === 200) {
      userList.value = response.data.data.records
      userTotal.value = response.data.data.total
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  } finally {
    userLoading.value = false
  }
}

// 获取角色列表
const getRoleList = async () => {
  roleLoading.value = true
  try {
    const response = await fetchRoleList(roleQuery)
    if (response.data.code === 200) {
      roleList.value = response.data.data.records
      roleTotal.value = response.data.data.total
    }
  } catch (error) {
    ElMessage.error('获取角色列表失败')
  } finally {
    roleLoading.value = false
  }
}

// 获取权限列表
const getPermissionList = async () => {
  permissionLoading.value = true
  try {
    const response = await getPermissionTree()
    if (response.data.code === 200) {
      permissionList.value = response.data.data
    }
  } catch (error) {
    ElMessage.error('获取权限列表失败')
  } finally {
    permissionLoading.value = false
  }
}

// 获取机构列表
const getOrganizationList = async () => {
  orgLoading.value = true
  try {
    const response = await fetchOrganizationList(orgQuery)
    if (response.data.code === 200) {
      organizationList.value = response.data.data.records
      orgTotal.value = response.data.data.total
    }
  } catch (error) {
    ElMessage.error('获取机构列表失败')
  } finally {
    orgLoading.value = false
  }
}

// 获取机构选项
const getOrganizationOptions = async () => {
  try {
    const response = await getAllOrganizations()
    if (response.data.code === 200) {
      organizationOptions.value = response.data.data
    }
  } catch (error) {
    ElMessage.error('获取机构选项失败')
  }
}

// 重置查询条件
const resetUserQuery = () => {
  userQuery.userName = ''
  userQuery.orgId = null
  getUserList()
}

const resetRoleQuery = () => {
  roleQuery.roleName = ''
  getRoleList()
}

const resetPermissionQuery = () => {
  permissionQuery.name = ''
  permissionQuery.type = ''
  getPermissionList()
}

const resetOrgQuery = () => {
  orgQuery.orgName = ''
  getOrganizationList()
}

// 获取权限类型文本
const getPermissionTypeText = (type) => {
  const typeMap = {
    menu: '菜单',
    button: '按钮',
    api: '接口'
  }
  return typeMap[type] || '未知'
}

// 获取权限类型颜色
const getPermissionTypeColor = (type) => {
  const colorMap = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return colorMap[type] || ''
}

// 获取机构类型文本
const getOrgTypeText = (type) => {
  const typeMap = {
    1: '总部',
    2: '分行',
    3: '部门',
    4: '组'
  }
  return typeMap[type] || '未知'
}

// 获取机构类型颜色
const getOrgTypeColor = (type) => {
  const colorMap = {
    1: 'success',
    2: 'primary',
    3: 'warning',
    4: 'info'
  }
  return colorMap[type] || ''
}

// 事件处理函数（这里只是占位，实际应该调用相应的组件或弹窗）
const handleAddUser = () => {
  ElMessage.info('请在用户管理页面进行操作')
}

const handleEditUser = (row) => {
  ElMessage.info('请在用户管理页面进行操作')
}

const handleUserRoles = (row) => {
  ElMessage.info('请在用户管理页面进行操作')
}

const handleResetPassword = (row) => {
  ElMessage.info('请在用户管理页面进行操作')
}

const handleDeleteUser = (row) => {
  ElMessage.info('请在用户管理页面进行操作')
}

const handleAddRole = () => {
  ElMessage.info('请在角色管理页面进行操作')
}

const handleEditRole = (row) => {
  ElMessage.info('请在角色管理页面进行操作')
}

const handleRolePermissions = (row) => {
  ElMessage.info('请在角色管理页面进行操作')
}

const handleDeleteRole = (row) => {
  ElMessage.info('请在角色管理页面进行操作')
}

const handleAddPermission = () => {
  ElMessage.info('请在权限管理页面进行操作')
}

const handleEditPermission = (row) => {
  ElMessage.info('请在权限管理页面进行操作')
}

const handleAddChildPermission = (row) => {
  ElMessage.info('请在权限管理页面进行操作')
}

const handleDeletePermission = (row) => {
  ElMessage.info('请在权限管理页面进行操作')
}

const handleAddOrganization = () => {
  ElMessage.info('请在机构管理页面进行操作')
}

const handleEditOrganization = (row) => {
  ElMessage.info('请在机构管理页面进行操作')
}

const handleDeleteOrganization = (row) => {
  ElMessage.info('请在机构管理页面进行操作')
}

// 组件挂载时加载数据
onMounted(() => {
  getUserList()
  getRoleList()
  getPermissionList()
  getOrganizationList()
  getOrganizationOptions()
})
</script>

<style scoped>
.rbac-container {
  padding: 20px;
}

.rbac-tabs {
  background: #fff;
}

.tab-content {
  padding: 20px 0;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  background: #fff;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tab-pane) {
  background: #f5f5f5;
  min-height: 600px;
}
</style>