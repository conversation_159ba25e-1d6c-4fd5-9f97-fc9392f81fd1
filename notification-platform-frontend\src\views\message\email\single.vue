<template>
  <div class="single-email-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>单条邮件发送</span>
        </div>
      </template>
      
      <el-form
        ref="emailFormRef"
        :model="emailForm"
        :rules="emailRules"
        label-width="120px"
        class="email-form"
      >
        <el-form-item label="收件人" prop="to">
          <el-input
            v-model="emailForm.to"
            placeholder="请输入收件人邮箱地址"
            clearable
          />
        </el-form-item>

        <el-form-item label="抄送" prop="cc">
          <el-input
            v-model="emailForm.cc"
            placeholder="多个邮箱用逗号分隔"
            clearable
          />
        </el-form-item>

        <el-form-item label="密送" prop="bcc">
          <el-input
            v-model="emailForm.bcc"
            placeholder="多个邮箱用逗号分隔"
            clearable
          />
        </el-form-item>

        <el-form-item label="邮件主题" prop="subject">
          <el-input
            v-model="emailForm.subject"
            placeholder="请输入邮件主题"
            clearable
          />
        </el-form-item>

        <el-form-item label="邮件内容" prop="content">
          <el-input
            v-model="emailForm.content"
            type="textarea"
            :rows="8"
            placeholder="请输入邮件内容，支持HTML格式"
          />
        </el-form-item>

        <el-form-item label="附件">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :auto-upload="false"
            multiple
            :limit="5"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                最多上传5个文件，单个文件不超过10MB，总大小不超过25MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="发送渠道" prop="channelCode">
          <el-select
            v-model="emailForm.channelCode"
            placeholder="请选择发送渠道（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.channelCode"
              :label="channel.channelName"
              :value="channel.channelCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendEmail"
          >
            {{ sending ? '发送中...' : '发送邮件' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="previewEmail">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="邮件预览"
      width="60%"
    >
      <div class="email-preview">
        <div class="preview-item">
          <strong>收件人：</strong>{{ emailForm.to }}
        </div>
        <div v-if="emailForm.cc" class="preview-item">
          <strong>抄送：</strong>{{ emailForm.cc }}
        </div>
        <div v-if="emailForm.bcc" class="preview-item">
          <strong>密送：</strong>{{ emailForm.bcc }}
        </div>
        <div class="preview-item">
          <strong>主题：</strong>{{ emailForm.subject }}
        </div>
        <div class="preview-item">
          <strong>内容：</strong>
          <div class="content-preview" v-html="emailForm.content"></div>
        </div>
        <div v-if="fileList.length > 0" class="preview-item">
          <strong>附件：</strong>
          <ul>
            <li v-for="file in fileList" :key="file.uid">
              {{ file.name }} ({{ formatFileSize(file.size) }})
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { sendSingleEmail, sendEmailWithAttachments, sendEmailWithChannel } from '@/api/email'
import { getSendChannelsByType } from '@/api/channel'

// 表单数据
const emailForm = reactive({
  to: '',
  cc: '',
  bcc: '',
  subject: '',
  content: '',
  channelCode: ''
})

// 表单验证规则
const emailRules = {
  to: [
    { required: true, message: '请输入收件人邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请输入邮件主题', trigger: 'blur' },
    { max: 200, message: '邮件主题不能超过200个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入邮件内容', trigger: 'blur' }
  ]
}

// 响应式数据
const emailFormRef = ref()
const uploadRef = ref()
const sending = ref(false)
const previewVisible = ref(false)
const fileList = ref([])
const channelList = ref([])

// 获取发送渠道列表
const getChannelList = async () => {
  try {
    const response = await getSendChannelsByType(2) // 2-邮件
    if (response.code === 200) {
      channelList.value = response.data || []
      // 默认选择第一个渠道
      if (channelList.value.length > 0) {
        emailForm.channelCode = channelList.value[0].channelCode
      }
    }
  } catch (error) {
    console.error('获取发送渠道失败:', error)
  }
}

// 文件上传处理
const handleFileChange = (file, files) => {
  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('单个文件大小不能超过10MB')
    files.splice(files.indexOf(file), 1)
    return
  }

  // 检查总文件大小
  const totalSize = files.reduce((total, f) => total + f.size, 0)
  if (totalSize > 25 * 1024 * 1024) {
    ElMessage.error('附件总大小不能超过25MB')
    files.splice(files.indexOf(file), 1)
    return
  }

  fileList.value = files
}

// 文件移除处理
const handleFileRemove = (file, files) => {
  fileList.value = files
}

// 文件数量超限处理
const handleExceed = () => {
  ElMessage.warning('最多只能上传5个文件')
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  }
}

// 发送邮件
const sendEmail = async () => {
  try {
    // 表单验证
    await emailFormRef.value.validate()

    sending.value = true

    let response
    if (fileList.value.length > 0) {
      // 有附件的情况
      const formData = new FormData()
      formData.append('to', emailForm.to)
      formData.append('subject', emailForm.subject)
      formData.append('content', emailForm.content)
      
      // 添加附件
      fileList.value.forEach(file => {
        formData.append('attachments', file.raw)
      })

      response = await sendEmailWithAttachments(formData)
    } else {
      // 如果指定了渠道，使用指定渠道发送
      if (emailForm.channelCode) {
        const channelEmailData = {
          channelCode: emailForm.channelCode,
          to: emailForm.to,
          subject: emailForm.subject,
          content: emailForm.content
        }
        response = await sendEmailWithChannel(channelEmailData)
      } else {
        // 无附件的情况
        const emailData = {
          to: emailForm.to,
          subject: emailForm.subject,
          content: emailForm.content
        }
        response = await sendSingleEmail(emailData)
      }
    }

    if (response.code === 200) {
      ElMessage.success('邮件发送成功')
      resetForm()
    } else {
      ElMessage.error(response.message || '邮件发送失败')
    }
  } catch (error) {
    console.error('发送邮件失败:', error)
    ElMessage.error('发送邮件失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  emailFormRef.value.resetFields()
  fileList.value = []
  uploadRef.value.clearFiles()
}

// 预览邮件
const previewEmail = async () => {
  try {
    await emailFormRef.value.validate()
    previewVisible.value = true
  } catch (error) {
    ElMessage.warning('请先完善邮件信息')
  }
}

// 组件挂载时获取渠道列表
onMounted(() => {
  getChannelList()
})
</script>

<style scoped>
.single-email-container {
  padding: 20px;
}

.box-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.email-form {
  margin-top: 20px;
}

.email-preview {
  padding: 20px;
}

.preview-item {
  margin-bottom: 15px;
  line-height: 1.6;
}

.content-preview {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 100px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}
</style>