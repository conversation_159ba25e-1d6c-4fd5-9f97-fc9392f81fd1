import * as XLSX from 'xlsx';

/**
 * Excel导出工具类
 */
export class ExcelExporter {
  
  /**
   * 导出数据到Excel文件
   * @param {Array} data - 要导出的数据数组
   * @param {Array} columns - 列配置数组
   * @param {string} filename - 文件名（不含扩展名）
   * @param {string} sheetName - 工作表名称
   */
  static exportToExcel(data, columns, filename = 'export', sheetName = 'Sheet1') {
    try {
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      
      // 准备表头
      const headers = columns.map(col => col.label || col.prop);
      
      // 准备数据
      const excelData = [
        headers, // 表头
        ...data.map(row => 
          columns.map(col => {
            let value = row[col.prop];
            
            // 处理特殊格式
            if (col.formatter && typeof col.formatter === 'function') {
              value = col.formatter(value, row);
            }
            
            return value || '';
          })
        )
      ];
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(excelData);
      
      // 设置列宽
      const colWidths = columns.map(col => ({
        wch: col.width ? Math.floor(col.width / 8) : 15
      }));
      worksheet['!cols'] = colWidths;
      
      // 设置表头样式
      const headerRange = XLSX.utils.decode_range(worksheet['!ref']);
      for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
        if (!worksheet[cellAddress]) continue;
        
        worksheet[cellAddress].s = {
          font: { bold: true, color: { rgb: "FFFFFF" } },
          fill: { fgColor: { rgb: "409EFF" } },
          alignment: { horizontal: "center", vertical: "center" }
        };
      }
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
      
      // 导出文件
      const fileName = `${filename}_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      return true;
    } catch (error) {
      console.error('Excel导出失败:', error);
      throw new Error('Excel导出失败: ' + error.message);
    }
  }
  
  /**
   * 导出多个工作表到一个Excel文件
   * @param {Array} sheets - 工作表配置数组 [{data, columns, sheetName}]
   * @param {string} filename - 文件名
   */
  static exportMultipleSheets(sheets, filename = 'export') {
    try {
      const workbook = XLSX.utils.book_new();
      
      sheets.forEach(sheet => {
        const { data, columns, sheetName } = sheet;
        
        // 准备表头
        const headers = columns.map(col => col.label || col.prop);
        
        // 准备数据
        const excelData = [
          headers,
          ...data.map(row => 
            columns.map(col => {
              let value = row[col.prop];
              if (col.formatter && typeof col.formatter === 'function') {
                value = col.formatter(value, row);
              }
              return value || '';
            })
          )
        ];
        
        // 创建工作表
        const worksheet = XLSX.utils.aoa_to_sheet(excelData);
        
        // 设置列宽
        const colWidths = columns.map(col => ({
          wch: col.width ? Math.floor(col.width / 8) : 15
        }));
        worksheet['!cols'] = colWidths;
        
        // 添加到工作簿
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
      });
      
      // 导出文件
      const fileName = `${filename}_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      return true;
    } catch (error) {
      console.error('多工作表Excel导出失败:', error);
      throw new Error('Excel导出失败: ' + error.message);
    }
  }
  
  /**
   * 导出统计报表（带汇总信息）
   * @param {Object} summary - 汇总信息
   * @param {Array} data - 详细数据
   * @param {Array} columns - 列配置
   * @param {string} filename - 文件名
   * @param {string} title - 报表标题
   */
  static exportStatisticsReport(summary, data, columns, filename = 'statistics', title = '统计报表') {
    try {
      const workbook = XLSX.utils.book_new();
      
      // 准备数据
      const excelData = [];
      
      // 添加标题
      excelData.push([title]);
      excelData.push([]); // 空行
      
      // 添加汇总信息
      excelData.push(['汇总信息']);
      Object.entries(summary).forEach(([key, value]) => {
        excelData.push([key, value]);
      });
      excelData.push([]); // 空行
      
      // 添加详细数据表头
      excelData.push(['详细数据']);
      const headers = columns.map(col => col.label || col.prop);
      excelData.push(headers);
      
      // 添加详细数据
      data.forEach(row => {
        const rowData = columns.map(col => {
          let value = row[col.prop];
          if (col.formatter && typeof col.formatter === 'function') {
            value = col.formatter(value, row);
          }
          return value || '';
        });
        excelData.push(rowData);
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(excelData);
      
      // 设置样式和格式
      const range = XLSX.utils.decode_range(worksheet['!ref']);
      
      // 合并标题单元格
      worksheet['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: columns.length - 1 } }
      ];
      
      // 设置列宽
      const colWidths = columns.map(() => ({ wch: 15 }));
      worksheet['!cols'] = colWidths;
      
      // 添加到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '统计报表');
      
      // 导出文件
      const fileName = `${filename}_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      return true;
    } catch (error) {
      console.error('统计报表导出失败:', error);
      throw new Error('统计报表导出失败: ' + error.message);
    }
  }
  
  /**
   * 格式化日期为文件名格式
   * @param {Date} date - 日期对象
   * @returns {string} 格式化后的日期字符串
   */
  static formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
  }
  
  /**
   * 状态格式化器
   * @param {string} status - 状态值
   * @returns {string} 格式化后的状态
   */
  static formatStatus(status) {
    const statusMap = {
      'SUCCESS': '发送成功',
      'FAILED': '发送失败',
      'SENDING': '发送中',
      'QUEUED': '队列中'
    };
    return statusMap[status] || status;
  }
  
  /**
   * 百分比格式化器
   * @param {number} value - 数值
   * @returns {string} 格式化后的百分比
   */
  static formatPercentage(value) {
    return `${value}%`;
  }
  
  /**
   * 日期时间格式化器
   * @param {string} dateTime - 日期时间字符串
   * @returns {string} 格式化后的日期时间
   */
  static formatDateTime(dateTime) {
    if (!dateTime) return '';
    return new Date(dateTime).toLocaleString('zh-CN');
  }
}

export default ExcelExporter;