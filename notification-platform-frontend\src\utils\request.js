import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '',
  timeout: 60000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.baseURL + config.url)
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    if (res.code !== 200) {
      if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
        ElMessageBox.confirm('你已被登出，可以取消继续留在该页面，或者重新登录', '确定登出', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          removeToken()
          location.reload()
        })
      } else {
        ElMessage({
          message: res.message || 'Error',
          type: 'error',
          duration: 5 * 1000
        })
      }
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      if (status === 401) {
        ElMessage({
          message: '登录已过期，请重新登录',
          type: 'error',
          duration: 5 * 1000
        })
        removeToken()
        router.push('/login')
      } else if (status === 403) {
        ElMessage({
          message: '没有权限访问该资源',
          type: 'error',
          duration: 5 * 1000
        })
      } else if (status === 404) {
        ElMessage({
          message: '请求的资源不存在',
          type: 'error',
          duration: 5 * 1000
        })
      } else if (status >= 500) {
        ElMessage({
          message: data?.message || '服务器内部错误',
          type: 'error',
          duration: 5 * 1000
        })
      } else {
        ElMessage({
          message: data?.message || `请求失败 (${status})`,
          type: 'error',
          duration: 5 * 1000
        })
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage({
        message: '请求超时，请稍后重试',
        type: 'error',
        duration: 5 * 1000
      })
    } else {
      ElMessage({
        message: '网络错误，请检查网络连接',
        type: 'error',
        duration: 5 * 1000
      })
    }
    
    return Promise.reject(error)
  }
)

export default service
