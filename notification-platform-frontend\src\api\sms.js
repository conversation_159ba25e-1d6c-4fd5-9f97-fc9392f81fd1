import request from '@/utils/request'

/**
 * 发送单条短信
 * @param {Object} data 短信数据
 */
export function sendSingleSms(data) {
  return request({
    url: '/api/single-sms/send-with-content',
    method: 'post',
    data
  })
}

/**
 * 使用指定渠道发送短信
 * @param {Object} data 短信数据
 */
export function sendSmsWithChannel(data) {
  return request({
    url: '/api/single-sms/send-with-channel',
    method: 'post',
    data
  })
}

/**
 * 批量发送短信
 * @param {Object} data 批量短信数据
 */
export function sendBatchSms(data) {
  return request({
    url: '/api/sms/send-batch',
    method: 'post',
    data
  })
}

/**
 * 查询短信发送状态
 * @param {String} channelCode 渠道编码
 * @param {String} messageId 消息ID
 */
export function querySmsStatus(channelCode, messageId) {
  return request({
    url: '/api/sms/status',
    method: 'get',
    params: { channelCode, messageId }
  })
}

/**
 * 获取发送渠道列表（按类型）
 * @param {Object} params 查询参数，包含channelType
 */
export function getSendChannelList(params) {
  return request({
    url: `/api/send-channels/type/${params.channelType}`,
    method: 'get'
  })
}