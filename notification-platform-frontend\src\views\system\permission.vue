<template>
  <div class="permission-container">
    <!-- 查询表单 -->
    <el-card class="filter-card">
      <el-form :model="queryParams" :inline="true" @submit.prevent>
        <el-form-item label="权限名称:">
          <el-input v-model="queryParams.name" placeholder="请输入权限名称" clearable @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="权限类型:">
          <el-select v-model="queryParams.type" placeholder="请选择权限类型" clearable style="width: 200px">
            <el-option label="菜单" :value="1" />
            <el-option label="按钮" :value="2" />
            <el-option label="接口" :value="3" />
            </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="success" @click="handleAdd">新增</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 权限树表格 -->
    <el-card class="table-card">
      <el-table :data="permissionList" v-loading="loading" row-key="id" :tree-props="{ children: 'children' }" border
        default-expand-all>
        <el-table-column prop="permissionName" label="权限名称" width="200" />
        <el-table-column prop="permissionCode" label="权限标识" width="200" />
        <el-table-column prop="resourceType" label="权限类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeColor(scope.row.resourceType)">
              {{ getTypeText(scope.row.resourceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径/URL" show-overflow-tooltip />
        <el-table-column prop="icon" label="图标" width="80">
          <template #default="scope">
            <i :class="scope.row.icon" v-if="scope.row.icon"></i>
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleAddChild(scope.row)"
              v-if="scope.row.resourceType === 1">
              新增子权限
            </el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑权限对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="700px" :before-close="handleClose">
      <el-form ref="permissionFormRef" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权限名称" prop="permissionName">
              <el-input v-model="form.permissionName" placeholder="请输入权限名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权限标识" prop="permissionCode">
              <el-input v-model="form.permissionCode" placeholder="请输入权限标识" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="权限类型" prop="resourceType">
              <el-select v-model="form.resourceType" placeholder="请选择权限类型" @change="handleTypeChange">
                <el-option label="菜单" :value="1" />
                <el-option label="按钮" :value="2" />
                <el-option label="接口" :value="3" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上级权限" prop="parentId">
              <el-tree-select v-model="form.parentId" :data="parentPermissionOptions" :props="treeSelectProps"
                placeholder="请选择上级权限" clearable check-strictly />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="form.resourceType === 1">
          <el-col :span="12">
            <el-form-item label="路由路径" prop="path">
              <el-input v-model="form.path" placeholder="请输入路由路径" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组件路径" prop="component">
              <el-input v-model="form.component" placeholder="请输入组件路径" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="图标" prop="icon" v-if="form.resourceType === 1">
              <el-input v-model="form.icon" placeholder="请输入图标类名">
                <template #prefix>
                  <i :class="form.icon" v-if="form.icon"></i>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sortOrder">
              <el-input-number v-model="form.sortOrder" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio :label="1">启用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">

          </el-col>
        </el-row>


      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPermissionTree,
  getPermissionById,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api/permission'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const permissionList = ref([])
const parentPermissionOptions = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const permissionFormRef = ref()

// 存储原始数据
let originalPermissionData = []

// 查询参数
const queryParams = reactive({
  name: '',
  type: null
})

// 表单数据
const form = reactive({
  id: null,
  permissionName: '',
  permissionCode: '',
  resourceType: 1,
  parentId: null,
  path: '',
  component: '',
  icon: '',
  sortOrder: 0,
  status: 1
})

// 树选择器属性
const treeSelectProps = {
  value: 'id',
  label: 'permissionName',
  children: 'children'
}

// 表单验证规则
const rules = {
  permissionName: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 50, message: '权限名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  permissionCode: [
    { required: true, message: '请输入权限标识', trigger: 'blur' },
    { min: 2, max: 100, message: '权限标识长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  resourceType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 获取权限类型文本
const getTypeText = (type) => {
  const typeMap = {
    1: '菜单',
    2: '按钮',
    3: '接口'
  }
  return typeMap[type] || '未知'
}

// 获取权限类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  }
  return colorMap[type] || ''
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取权限树
const getList = async () => {
  loading.value = true
  try {
    // 使用后端真实API
    const response = await getPermissionTree()

    console.log('API响应:', response)
    console.log('响应数据:', response.data)

    if (response.data && response.data.code === 200) {
      // 保存原始数据
      originalPermissionData = JSON.parse(JSON.stringify(response.data.data))
      // 直接使用后端返回的树结构数据
      permissionList.value = response.data.data

      // 调试信息：打印数据结构
      console.log('权限数据结构:', JSON.stringify(response.data.data, null, 2))
      console.log('权限数据数量:', response.data.data.length)

      // 构建父级权限选项（只包含菜单类型）
      buildParentOptions(permissionList.value)
    } else {
      const errorMsg = response.data?.message || '获取权限列表失败'
      console.error('API返回错误:', errorMsg)
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('获取权限列表失败:', error)
    console.error('错误详情:', error.response || error.message)
    ElMessage.error('获取权限列表失败: ' + (error.message || '网络错误'))
  } finally {
    loading.value = false
  }
}

// 构建父级权限选项
const buildParentOptions = (permissions) => {
  const options = [{
    id: 0,
    permissionName: '根权限',
    children: []
  }]

  const traverse = (items, level = 0) => {
    items.forEach(item => {
      // 只有菜单类型才能作为父级权限
      if (item.resourceType === 1) {
        const option = {
          id: item.id,
          permissionName: '　'.repeat(level) + item.permissionName,
          children: []
        }
        options.push(option)

        // 递归处理子权限
        if (item.children && item.children.length > 0) {
          traverse(item.children, level + 1)
        }
      }
    })
  }

  traverse(permissions)
  parentPermissionOptions.value = options
}

// 查询
const handleQuery = () => {
  // 如果有查询条件，进行过滤
  if (queryParams.name || queryParams.type) {
    permissionList.value = filterPermissions(originalPermissionData, queryParams.name, queryParams.type)
  } else {
    permissionList.value = [...originalPermissionData]
  }
}

// 过滤权限数据
const filterPermissions = (permissions, name, type) => {
  if (!name && !type) return permissions

  const filtered = []

  const filterNode = (item) => {
    // 检查当前节点是否匹配
    let currentMatch = true

    if (name && !item.permissionName.includes(name)) {
      currentMatch = false
    }

    if (type && item.resourceType !== type) {
      currentMatch = false
    }

    // 递归过滤子节点
    let filteredChildren = []
    if (item.children && item.children.length > 0) {
      filteredChildren = item.children.map(child => filterNode(child)).filter(child => child !== null)
    }

    // 如果当前节点匹配或有匹配的子节点，则保留
    if (currentMatch || filteredChildren.length > 0) {
      return {
        ...item,
        children: filteredChildren
      }
    }

    return null
  }

  permissions.forEach(item => {
    const filteredItem = filterNode(item)
    if (filteredItem) {
      filtered.push(filteredItem)
    }
  })

  return filtered
}

// 重置查询
const resetQuery = () => {
  queryParams.name = ''
  queryParams.type = null
  // 恢复原始数据
  permissionList.value = [...originalPermissionData]
}

// 新增
const handleAdd = () => {
  resetForm()
  dialogTitle.value = '新增权限'
  dialogVisible.value = true
}

// 新增子权限
const handleAddChild = (row) => {
  resetForm()
  form.parentId = row.id
  dialogTitle.value = '新增子权限'
  dialogVisible.value = true
}

// 编辑
const handleEdit = async (row) => {
  try {
    const response = await getPermissionById(row.id)
    if (response.data.code === 200) {
      const permissionData = response.data.data
      Object.assign(form, permissionData)
      dialogTitle.value = '编辑权限'
      dialogVisible.value = true
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    ElMessage.error('获取权限详情失败')
  }
}

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除权限 "${row.permissionName}" 吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      const response = await deletePermission(row.id)
      if (response.data.code === 200) {
        ElMessage.success('删除成功')
        getList()
      } else {
        ElMessage.error(response.data.message)
      }
    } catch (error) {
      ElMessage.error('删除失败')
    }
  })
}

// 权限类型变化处理
const handleTypeChange = (type) => {
  // 根据类型重置相关字段
  if (type !== 1) {
    form.path = ''
    form.component = ''
    form.icon = ''
  }
}

// 提交表单
const submitForm = async () => {
  if (!permissionFormRef.value) return

  try {
    await permissionFormRef.value.validate()

    submitting.value = true

    let response
    if (form.id) {
      response = await updatePermission(form.id, form)
    } else {
      response = await createPermission(form)
    }

    if (response.data.code === 200) {
      ElMessage.success(form.id ? '更新成功' : '创建成功')
      dialogVisible.value = false
      getList()
    } else {
      ElMessage.error(response.data.message)
    }
  } catch (error) {
    if (error !== 'validation failed') {
      ElMessage.error('操作失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  form.id = null
  form.permissionName = ''
  form.permissionCode = ''
  form.resourceType = 1
  form.parentId = null
  form.path = ''
  form.component = ''
  form.icon = ''
  form.sortOrder = 0
  form.status = 1
  if (permissionFormRef.value) {
    permissionFormRef.value.resetFields()
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 组件挂载时加载数据
onMounted(() => {
  getList()
})
</script>

<style scoped>
.permission-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  background: #fff;
}

.dialog-footer {
  text-align: right;
}
</style>