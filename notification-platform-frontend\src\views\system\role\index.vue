<template>
  <div class="role-management">
    <div class="page-header">
      <h2>角色管理</h2>
      <div class="actions">
        <el-button 
          v-if="permissions.add" 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增角色
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="角色名称">
          <el-input v-model="searchForm.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="roleName" label="角色名称" width="150" show-overflow-tooltip />
        <el-table-column prop="remark" label="描述" width="200" show-overflow-tooltip />
        <el-table-column prop="roleSort" label="排序" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="140" show-overflow-tooltip />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="permissions.view"
              type="primary" 
              size="small" 
              plain
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              v-if="permissions.edit"
              type="success" 
              size="small" 
              plain
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              plain
              @click="handleAssignPermissions(row)"
            >
              分配权限
            </el-button>
            <el-button 
              v-if="permissions.delete"
              type="danger" 
              size="small" 
              plain
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 角色详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="roleFormRef"
        :model="roleForm"
        :rules="roleFormRules"
        label-width="100px"
        :disabled="dialogMode === 'view'"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="roleForm.roleName" />
        </el-form-item>
        <el-form-item label="排序" prop="roleSort">
          <el-input-number v-model="roleForm.roleSort" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="roleForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="roleForm.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      
      <template #footer v-if="dialogMode !== 'view'">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 分配权限对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="分配权限"
      width="800px"
      top="5vh"
    >
      <div class="permission-dialog-content">
        <div class="role-info">
          <el-tag type="primary" size="large">
            <i class="el-icon-user"></i>
            {{ currentRole.roleName }}
          </el-tag>
        </div>
        
        <div class="permission-tree-container">
          <div class="tree-header">
            <span class="tree-title">权限列表</span>
            <el-button size="small" @click="expandAll">展开全部</el-button>
            <el-button size="small" @click="collapseAll">折叠全部</el-button>
          </div>
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTree"
            show-checkbox
            node-key="id"
            :props="{ children: 'children', label: 'permissionName' }"
            :default-checked-keys="selectedPermissions"
            :default-expand-all="false"
            check-strictly
            class="permission-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <span class="node-label">{{ data.permissionName }}</span>
                <el-tag 
                  v-if="data.resourceType" 
                  :type="getResourceTypeColor(data.resourceType)" 
                  size="small"
                  class="node-tag"
                >
                  {{ getResourceTypeName(data.resourceType) }}
                </el-tag>
              </div>
            </template>
          </el-tree>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermissions">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserInfo } from '@/utils/auth';
import { getPageButtonPermissions } from '@/utils/permission-buttons';
import { 
  getRoleList, 
  createRole, 
  updateRole, 
  deleteRole,
  assignPermissions,
  getRolePermissions
} from '@/api/role';
import { getPermissionTree } from '@/api/permission';

// 权限控制
const userInfo = getUserInfo();
const permissions = computed(() => {
  return getPageButtonPermissions('system:role', userInfo?.permissions || []);
});

// 数据定义
const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const permissionDialogVisible = ref(false);
const dialogMode = ref('add'); // add, edit, view
const roleFormRef = ref();
const permissionTreeRef = ref();
const currentRole = ref({});
const permissionTree = ref([]);
const selectedPermissions = ref([]);

// 搜索表单
const searchForm = reactive({
  roleName: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 角色表单
const roleForm = reactive({
  id: null,
  roleName: '',
  roleSort: 1,
  status: 1,
  remark: ''
});

// 表单验证规则
const roleFormRules = {
  roleName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ]
};

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    add: '新增角色',
    edit: '编辑角色',
    view: '查看角色'
  };
  return titles[dialogMode.value];
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getRoleList({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    });
    tableData.value = response.data.records;
    pagination.total = response.data.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const loadPermissionTree = async () => {
  try {
    const response = await getPermissionTree();
    permissionTree.value = response.data;
  } catch (error) {
    ElMessage.error('加载权限树失败');
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    roleName: ''
  });
  handleSearch();
};

const handleAdd = () => {
  dialogMode.value = 'add';
  resetRoleForm();
  dialogVisible.value = true;
};

const handleView = (row) => {
  dialogMode.value = 'view';
  Object.assign(roleForm, row);
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  dialogMode.value = 'edit';
  Object.assign(roleForm, row);
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色"${row.roleName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteRole(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleAssignPermissions = async (row) => {
  currentRole.value = row;
  try {
    const response = await getRolePermissions(row.id);
    selectedPermissions.value = response.data;
    permissionDialogVisible.value = true;
  } catch (error) {
    ElMessage.error('获取角色权限失败');
  }
};

const expandAll = () => {
  const allKeys = [];
  const collectKeys = (nodes) => {
    nodes.forEach(node => {
      allKeys.push(node.id);
      if (node.children) {
        collectKeys(node.children);
      }
    });
  };
  collectKeys(permissionTree.value);
  permissionTreeRef.value.setExpandedKeys(allKeys);
};

const collapseAll = () => {
  permissionTreeRef.value.setExpandedKeys([]);
};

const getResourceTypeName = (type) => {
  const types = {
    1: '菜单',
    2: '按钮',
    3: '接口'
  };
  return types[type] || '未知';
};

const getResourceTypeColor = (type) => {
  const colors = {
    1: 'primary',
    2: 'success',
    3: 'warning'
  };
  return colors[type] || '';
};

const handleSavePermissions = async () => {
  try {
    const checkedKeys = permissionTreeRef.value.getCheckedKeys();
    const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys();
    const allKeys = [...checkedKeys, ...halfCheckedKeys];
    
    await assignPermissions(currentRole.value.id, allKeys);
    ElMessage.success('权限分配成功');
    permissionDialogVisible.value = false;
  } catch (error) {
    ElMessage.error('权限分配失败');
  }
};

const handleSubmit = async () => {
  try {
    await roleFormRef.value.validate();
    
    if (dialogMode.value === 'add') {
      await createRole(roleForm);
      ElMessage.success('新增成功');
    } else {
      await updateRole(roleForm.id, roleForm);
      ElMessage.success('更新成功');
    }
    
    dialogVisible.value = false;
    loadData();
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

const handleDialogClose = () => {
  resetRoleForm();
};

const resetRoleForm = () => {
  Object.assign(roleForm, {
    id: null,
    roleName: '',
    roleSort: 1,
    status: 1,
    remark: ''
  });
  roleFormRef.value?.clearValidate();
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
  loadPermissionTree();
});
</script>

<style scoped>
.role-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.el-tree {
  max-height: 300px;
  overflow-y: auto;
}

.el-button + .el-button {
  margin-left: 6px;
}

.el-table .el-button {
  padding: 5px 8px;
  font-size: 12px;
}

.permission-dialog-content {
  padding: 10px 0;
}

.role-info {
  margin-bottom: 20px;
  text-align: center;
}

.permission-tree-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fafafa;
}

.tree-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.tree-title {
  font-weight: 500;
  color: #303133;
}

.permission-tree {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
  background: white;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-right: 8px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-tag {
  margin-left: 8px;
  font-size: 12px;
}
</style>