/**
 * RSA加密功能测试工具
 */
import rsaUtil from './rsa'
import { getPublicKey } from '@/api/auth'

/**
 * 测试RSA加密功能
 */
export async function testRSAEncryption() {
  console.log('开始测试RSA加密功能...')
  
  try {
    // 1. 测试获取公钥
    console.log('1. 获取RSA公钥...')
    const response = await getPublicKey()
    
    if (!response.data || response.data.code !== 200) {
      throw new Error('获取公钥失败: ' + (response.data?.message || '未知错误'))
    }
    
    const publicKey = response.data.data
    console.log('✓ 公钥获取成功')
    
    // 2. 测试设置公钥
    console.log('2. 设置RSA公钥...')
    rsaUtil.setPublicKey(publicKey)
    console.log('✓ 公钥设置成功')
    
    // 3. 测试初始化检查
    console.log('3. 检查初始化状态...')
    if (!rsaUtil.checkInitialized()) {
      throw new Error('RSA初始化检查失败')
    }
    console.log('✓ 初始化检查通过')
    
    // 4. 测试加密功能
    console.log('4. 测试密码加密...')
    const testPassword = 'test123456'
    const encryptedPassword = rsaUtil.encryptPassword(testPassword)
    
    if (!encryptedPassword || encryptedPassword === testPassword) {
      throw new Error('密码加密失败')
    }
    console.log('✓ 密码加密成功')
    console.log('原始密码:', testPassword)
    console.log('加密后密码:', encryptedPassword.substring(0, 50) + '...')
    
    console.log('✅ RSA加密功能测试全部通过')
    return true
    
  } catch (error) {
    console.error('❌ RSA加密功能测试失败:', error)
    return false
  }
}

/**
 * 诊断RSA加密问题
 */
export function diagnoseRSAIssues() {
  console.log('开始诊断RSA加密问题...')
  
  const issues = []
  
  // 检查jsencrypt库
  try {
    if (typeof JSEncrypt === 'undefined') {
      issues.push('jsencrypt库未正确加载')
    }
  } catch (error) {
    issues.push('jsencrypt库检查失败: ' + error.message)
  }
  
  // 检查RSA工具类状态
  if (!rsaUtil.checkInitialized()) {
    issues.push('RSA工具类未初始化')
  }
  
  // 检查网络连接
  if (!navigator.onLine) {
    issues.push('网络连接断开')
  }
  
  if (issues.length === 0) {
    console.log('✅ 未发现明显问题')
  } else {
    console.log('❌ 发现以下问题:')
    issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`)
    })
  }
  
  return issues
}