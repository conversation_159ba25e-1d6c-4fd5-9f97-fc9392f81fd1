import { 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  hasRole, 
  hasAnyRole,
  getUserPermissions,
  getUserRoles
} from '@/utils/permission'

/**
 * 权限混入
 * 在组件中提供权限检查方法
 */
export default {
  methods: {
    /**
     * 检查是否有指定权限
     */
    $hasPermission(permission) {
      return hasPermission(permission)
    },

    /**
     * 检查是否有任意一个权限
     */
    $hasAnyPermission(permissions) {
      return hasAnyPermission(permissions)
    },

    /**
     * 检查是否拥有所有权限
     */
    $hasAllPermissions(permissions) {
      return hasAllPermissions(permissions)
    },

    /**
     * 检查是否有指定角色
     */
    $hasRole(role) {
      return hasRole(role)
    },

    /**
     * 检查是否有任意一个角色
     */
    $hasAnyRole(roles) {
      return hasAnyRole(roles)
    },

    /**
     * 获取用户权限列表
     */
    $getUserPermissions() {
      return getUserPermissions()
    },

    /**
     * 获取用户角色列表
     */
    $getUserRoles() {
      return getUserRoles()
    }
  }
}