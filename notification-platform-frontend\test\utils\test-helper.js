const { chromium } = require('playwright');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class TestHelper {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testData = testData;
    this.selectors = selectors;
  }

  /**
   * 初始化浏览器和页面
   */
  async init() {
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 100 
    });
    this.page = await this.browser.newPage();
    
    // 设置视口大小
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    
    // 设置默认超时时间
    this.page.setDefaultTimeout(testData.timeouts.long);
    
    return this.page;
  }

  /**
   * 关闭浏览器
   */
  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  /**
   * 登录系统
   * @param {Object} credentials - 登录凭据
   */
  async login(credentials = testData.auth.validUser) {
    await this.page.goto(testData.environment.baseUrl + '/login');
    await this.page.waitForSelector(selectors.login.container);
    
    await this.page.fill(selectors.login.usernameInput, credentials.username);
    await this.page.fill(selectors.login.passwordInput, credentials.password);
    await this.page.click(selectors.login.loginButton);
    
    // 等待登录完成
    try {
      await this.page.waitForURL('**/dashboard', { timeout: testData.timeouts.navigation });
      return { success: true, message: '登录成功' };
    } catch (error) {
      const errorMsg = await this.getErrorMessage();
      return { success: false, message: errorMsg || '登录失败' };
    }
  }

  /**
   * 注销登录
   */
  async logout() {
    try {
      await this.page.click(selectors.navigation.userAvatar);
      await this.page.click(selectors.navigation.logoutButton);
      await this.page.waitForURL('**/login', { timeout: testData.timeouts.medium });
      return true;
    } catch (error) {
      await this.page.goto(testData.environment.baseUrl + '/login');
      return false;
    }
  }

  /**
   * 等待页面加载完成
   * @param {string} selector - 页面标识选择器
   */
  async waitForPageLoad(selector) {
    await this.page.waitForSelector(selector, { timeout: testData.timeouts.long });
    await this.page.waitForLoadState('networkidle');
    await this.waitForLoadingComplete();
  }

  /**
   * 等待加载动画完成
   */
  async waitForLoadingComplete() {
    try {
      await this.page.waitForSelector(selectors.common.loading, { 
        state: 'hidden', 
        timeout: testData.timeouts.medium 
      });
    } catch (error) {
      // 如果没有加载动画，继续执行
    }
  }

  /**
   * 填写表单
   * @param {Object} formData - 表单数据
   * @param {Object} fieldSelectors - 字段选择器映射
   */
  async fillForm(formData, fieldSelectors) {
    for (const [field, value] of Object.entries(formData)) {
      if (fieldSelectors[field] && value !== undefined && value !== null) {
        await this.page.fill(fieldSelectors[field], String(value));
        await this.page.waitForTimeout(100); // 短暂等待
      }
    }
  }

  /**
   * 验证表单值
   * @param {Object} expectedData - 期望的表单数据
   * @param {Object} fieldSelectors - 字段选择器映射
   */
  async verifyFormValues(expectedData, fieldSelectors) {
    const results = {};
    for (const [field, expectedValue] of Object.entries(expectedData)) {
      if (fieldSelectors[field] && expectedValue !== undefined) {
        try {
          const actualValue = await this.page.inputValue(fieldSelectors[field]);
          results[field] = {
            expected: String(expectedValue),
            actual: actualValue,
            match: actualValue === String(expectedValue)
          };
        } catch (error) {
          results[field] = {
            expected: String(expectedValue),
            actual: null,
            match: false,
            error: error.message
          };
        }
      }
    }
    return results;
  }

  /**
   * 点击按钮并等待响应
   * @param {string} selector - 按钮选择器
   * @param {number} waitTime - 等待时间
   */
  async clickAndWait(selector, waitTime = testData.timeouts.short) {
    await this.page.click(selector);
    await this.page.waitForTimeout(waitTime);
    await this.waitForLoadingComplete();
  }

  /**
   * 获取成功消息
   */
  async getSuccessMessage() {
    try {
      await this.page.waitForSelector(selectors.common.message, { timeout: testData.timeouts.medium });
      return await this.page.textContent(selectors.common.message);
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取错误消息
   */
  async getErrorMessage() {
    try {
      const errorSelectors = [
        selectors.login.errorMessage,
        selectors.sms.errorMessage,
        selectors.email.errorMessage,
        selectors.common.message
      ];
      
      for (const selector of errorSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          return await this.page.textContent(selector);
        } catch (e) {
          continue;
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 验证页面URL
   * @param {string} expectedPath - 期望的URL路径
   */
  async verifyURL(expectedPath) {
    const currentUrl = this.page.url();
    return currentUrl.includes(expectedPath);
  }

  /**
   * 获取表格数据
   * @param {string} tableSelector - 表格选择器
   */
  async getTableData(tableSelector = selectors.common.table) {
    await this.page.waitForSelector(tableSelector);
    
    const rows = await this.page.locator(`${tableSelector} tbody tr`).all();
    const data = [];
    
    for (const row of rows) {
      const cells = await row.locator('td').all();
      const rowData = [];
      
      for (const cell of cells) {
        const text = await cell.textContent();
        rowData.push(text.trim());
      }
      
      data.push(rowData);
    }
    
    return data;
  }

  /**
   * 搜索功能
   * @param {Object} searchParams - 搜索参数
   * @param {Object} searchSelectors - 搜索字段选择器
   */
  async search(searchParams, searchSelectors) {
    // 填写搜索条件
    await this.fillForm(searchParams, searchSelectors);
    
    // 点击搜索按钮
    await this.clickAndWait(selectors.template.searchButton);
  }

  /**
   * 分页操作
   * @param {number} pageNumber - 页码
   */
  async goToPage(pageNumber) {
    const pageSelector = `.el-pagination .el-pager li:has-text("${pageNumber}")`;
    await this.page.click(pageSelector);
    await this.waitForLoadingComplete();
  }

  /**
   * 文件上传
   * @param {string} fileInputSelector - 文件输入选择器
   * @param {string} filePath - 文件路径
   */
  async uploadFile(fileInputSelector, filePath) {
    await this.page.setInputFiles(fileInputSelector, filePath);
    await this.page.waitForTimeout(testData.timeouts.short);
  }

  /**
   * 验证元素可见性
   * @param {string} selector - 元素选择器
   * @param {boolean} shouldBeVisible - 是否应该可见
   */
  async verifyElementVisibility(selector, shouldBeVisible = true) {
    try {
      if (shouldBeVisible) {
        await this.page.waitForSelector(selector, { state: 'visible', timeout: testData.timeouts.medium });
        return true;
      } else {
        await this.page.waitForSelector(selector, { state: 'hidden', timeout: testData.timeouts.medium });
        return true;
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取元素文本内容
   * @param {string} selector - 元素选择器
   */
  async getElementText(selector) {
    try {
      await this.page.waitForSelector(selector);
      return await this.page.textContent(selector);
    } catch (error) {
      return null;
    }
  }

  /**
   * 验证元素文本内容
   * @param {string} selector - 元素选择器
   * @param {string} expectedText - 期望的文本内容
   */
  async verifyElementText(selector, expectedText) {
    const actualText = await this.getElementText(selector);
    return actualText && actualText.includes(expectedText);
  }

  /**
   * 获取仪表板统计数据
   */
  async getDashboardStats() {
    await this.waitForPageLoad(selectors.dashboard.container);
    
    const stats = {};
    
    try {
      const smsCount = await this.getElementText(selectors.dashboard.smsCard + ' ' + selectors.dashboard.cardValue);
      stats.smsCount = parseInt(smsCount) || 0;
    } catch (error) {
      stats.smsCount = 0;
    }
    
    try {
      const emailCount = await this.getElementText(selectors.dashboard.emailCard + ' ' + selectors.dashboard.cardValue);
      stats.emailCount = parseInt(emailCount) || 0;
    } catch (error) {
      stats.emailCount = 0;
    }
    
    return stats;
  }

  /**
   * 导航到指定页面
   * @param {string} path - 页面路径
   */
  async navigateTo(path) {
    const fullUrl = testData.environment.baseUrl + path;
    await this.page.goto(fullUrl);
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * 生成随机字符串
   * @param {number} length - 字符串长度
   */
  generateRandomString(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成随机手机号
   */
  generateRandomPhone() {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
    return prefix + suffix;
  }

  /**
   * 生成随机邮箱
   */
  generateRandomEmail() {
    const username = this.generateRandomString(8);
    const domains = ['example.com', 'test.com', 'demo.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    return `${username}@${domain}`;
  }

  /**
   * 等待指定时间
   * @param {number} ms - 等待时间（毫秒）
   */
  async wait(ms) {
    await this.page.waitForTimeout(ms);
  }

  /**
   * 截图
   * @param {string} path - 截图保存路径
   * @param {Object} options - 截图选项
   */
  async screenshot(path, options = {}) {
    const defaultOptions = {
      path: path,
      fullPage: true,
      ...options
    };
    
    await this.page.screenshot(defaultOptions);
  }
}

module.exports = TestHelper;
