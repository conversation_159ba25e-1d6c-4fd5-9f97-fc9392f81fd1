/**
 * T221-T230: 密码修改功能测试
 * 基于需求文档中的系统设置功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class PasswordChangeTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T221: 密码修改页面加载测试
   */
  async testT221_PasswordChangePageLoad() {
    const testId = 'T221';
    console.log(`\n🧪 执行测试 ${testId}: 密码修改页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到密码修改页面
      await this.testHelper.navigateTo('/system/password');
      await this.testHelper.waitForPageLoad(selectors.system.passwordChangeContainer);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isOldPasswordVisible = await this.testHelper.verifyElementVisibility(selectors.system.oldPasswordInput);
      const isNewPasswordVisible = await this.testHelper.verifyElementVisibility(selectors.system.newPasswordInput);
      const isConfirmPasswordVisible = await this.testHelper.verifyElementVisibility(selectors.system.confirmPasswordInput);
      const isChangeButtonVisible = await this.testHelper.verifyElementVisibility(selectors.system.changePasswordButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '密码修改页面加载测试',
        testContent: '验证密码修改页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的密码修改界面',
        testInput: '访问密码修改页面URL: /system/password',
        expectedOutput: '页面正常加载，显示原密码、新密码、确认密码输入框和修改按钮',
        actualOutput: `原密码输入框: ${isOldPasswordVisible ? '✅显示' : '❌隐藏'}, 新密码输入框: ${isNewPasswordVisible ? '✅显示' : '❌隐藏'}, 确认密码输入框: ${isConfirmPasswordVisible ? '✅显示' : '❌隐藏'}, 修改按钮: ${isChangeButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isOldPasswordVisible && isNewPasswordVisible && isConfirmPasswordVisible && isChangeButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '密码修改页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T222: 有效密码修改测试
   */
  async testT222_ValidPasswordChange() {
    const testId = 'T222';
    console.log(`\n🧪 执行测试 ${testId}: 有效密码修改测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到密码修改页面
      await this.testHelper.navigateTo('/system/password');
      await this.testHelper.waitForPageLoad(selectors.system.passwordChangeContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写密码修改信息
      const passwordData = testData.system.passwordChange;
      await this.testHelper.page.fill(selectors.system.oldPasswordInput, passwordData.oldPassword);
      await this.testHelper.page.fill(selectors.system.newPasswordInput, passwordData.newPassword);
      await this.testHelper.page.fill(selectors.system.confirmPasswordInput, passwordData.confirmPassword);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 提交密码修改
      await this.testHelper.page.click(selectors.system.changePasswordButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '有效密码修改测试',
        testContent: '使用有效的原密码和新密码进行密码修改',
        testPurpose: '验证密码修改功能能够正常工作',
        testInput: `原密码: ${passwordData.oldPassword}, 新密码: ${passwordData.newPassword}, 确认密码: ${passwordData.confirmPassword}`,
        expectedOutput: '密码修改成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '有效密码修改测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T223: 错误原密码测试
   */
  async testT223_WrongOldPassword() {
    const testId = 'T223';
    console.log(`\n🧪 执行测试 ${testId}: 错误原密码测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到密码修改页面
      await this.testHelper.navigateTo('/system/password');
      await this.testHelper.waitForPageLoad(selectors.system.passwordChangeContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写错误的原密码
      await this.testHelper.page.fill(selectors.system.oldPasswordInput, 'wrongpassword');
      await this.testHelper.page.fill(selectors.system.newPasswordInput, testData.system.passwordChange.newPassword);
      await this.testHelper.page.fill(selectors.system.confirmPasswordInput, testData.system.passwordChange.confirmPassword);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试提交密码修改
      await this.testHelper.page.click(selectors.system.changePasswordButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '错误原密码测试',
        testContent: '使用错误的原密码进行密码修改',
        testPurpose: '验证系统对错误原密码的验证机制',
        testInput: `原密码: wrongpassword (错误), 新密码: ${testData.system.passwordChange.newPassword}`,
        expectedOutput: '密码修改失败，显示原密码错误的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '错误原密码测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T224: 密码不一致测试
   */
  async testT224_PasswordMismatch() {
    const testId = 'T224';
    console.log(`\n🧪 执行测试 ${testId}: 密码不一致测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到密码修改页面
      await this.testHelper.navigateTo('/system/password');
      await this.testHelper.waitForPageLoad(selectors.system.passwordChangeContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写不一致的确认密码
      await this.testHelper.page.fill(selectors.system.oldPasswordInput, testData.system.passwordChange.oldPassword);
      await this.testHelper.page.fill(selectors.system.newPasswordInput, testData.system.passwordChange.newPassword);
      await this.testHelper.page.fill(selectors.system.confirmPasswordInput, 'DifferentPassword123!');
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试提交密码修改
      await this.testHelper.page.click(selectors.system.changePasswordButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '密码不一致测试',
        testContent: '新密码和确认密码不一致时的验证',
        testPurpose: '验证系统对密码一致性的验证机制',
        testInput: `新密码: ${testData.system.passwordChange.newPassword}, 确认密码: DifferentPassword123! (不一致)`,
        expectedOutput: '密码修改失败，显示密码不一致的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '密码不一致测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T225: 弱密码测试
   */
  async testT225_WeakPassword() {
    const testId = 'T225';
    console.log(`\n🧪 执行测试 ${testId}: 弱密码测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到密码修改页面
      await this.testHelper.navigateTo('/system/password');
      await this.testHelper.waitForPageLoad(selectors.system.passwordChangeContainer);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写弱密码
      const weakPassword = '123456';
      await this.testHelper.page.fill(selectors.system.oldPasswordInput, testData.system.passwordChange.oldPassword);
      await this.testHelper.page.fill(selectors.system.newPasswordInput, weakPassword);
      await this.testHelper.page.fill(selectors.system.confirmPasswordInput, weakPassword);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试提交密码修改
      await this.testHelper.page.click(selectors.system.changePasswordButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '弱密码测试',
        testContent: '使用不符合安全要求的弱密码进行修改',
        testPurpose: '验证系统对密码强度的验证机制',
        testInput: `新密码: ${weakPassword} (弱密码)`,
        expectedOutput: '密码修改失败，显示密码强度不足的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '弱密码测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有密码修改测试
   */
  async runAllTests() {
    console.log('🚀 开始执行密码修改功能测试套件 (T221-T225)');
    
    const startTime = Date.now();
    
    await this.testT221_PasswordChangePageLoad();
    await this.testT222_ValidPasswordChange();
    await this.testT223_WrongOldPassword();
    await this.testT224_PasswordMismatch();
    await this.testT225_WeakPassword();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 密码修改功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const passwordTest = new PasswordChangeTest();
  passwordTest.runAllTests().catch(console.error);
}

module.exports = PasswordChangeTest;
