<template>
  <div class="sms-statistics">
    <div class="page-header">
      <h2>短信统计</h2>
      <div class="actions">
        <el-button @click="handleExport" icon="Download">导出报表</el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="发送状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="发送成功" value="SUCCESS" />
            <el-option label="发送失败" value="FAILED" />
            <el-option label="发送中" value="SENDING" />
          </el-select>
        </el-form-item>
        <el-form-item label="统计时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ totalSms }}</div>
            <div class="stat-label">短信总数</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card success">
            <div class="stat-number">{{ successSms }}</div>
            <div class="stat-label">发送成功</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card warning">
            <div class="stat-number">{{ sendingSms }}</div>
            <div class="stat-label">发送中</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card danger">
            <div class="stat-number">{{ failedSms }}</div>
            <div class="stat-label">发送失败</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="content" label="短信内容" show-overflow-tooltip />
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="channelName" label="发送渠道" />
        <el-table-column prop="status" label="发送状态">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusName(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" />
        <el-table-column prop="receiveTime" label="接收时间" />
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              type="text" 
              size="small" 
              @click="handleViewDetail(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { ExcelExporter } from '@/utils/excel-export';

// 数据定义
const loading = ref(false);
const tableData = ref([]);
const totalSms = ref(0);
const successSms = ref(0);
const sendingSms = ref(0);
const failedSms = ref(0);

// 搜索表单
const searchForm = reactive({
  phone: '',
  status: '',
  dateRange: []
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        phone: '138****8888',
        content: '您的验证码是123456，5分钟内有效。',
        templateName: '验证码短信模板',
        channelName: '阿里云短信',
        status: 'SUCCESS',
        sendTime: '2025-01-27 14:30:00',
        receiveTime: '2025-01-27 14:30:02'
      },
      {
        id: 2,
        phone: '139****9999',
        content: '您有一笔订单待支付，请及时处理。',
        templateName: '订单提醒模板',
        channelName: '腾讯云短信',
        status: 'FAILED',
        sendTime: '2025-01-27 14:25:00',
        receiveTime: null
      }
    ];
    
    // 统计数据
    totalSms.value = 15000;
    successSms.value = 14850;
    sendingSms.value = 50;
    failedSms.value = 100;
    
    pagination.total = 2;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getStatusName = (status) => {
  const statusMap = {
    SUCCESS: '发送成功',
    FAILED: '发送失败',
    SENDING: '发送中'
  };
  return statusMap[status] || '未知';
};

const getStatusColor = (status) => {
  const colorMap = {
    SUCCESS: 'success',
    FAILED: 'danger',
    SENDING: 'warning'
  };
  return colorMap[status] || '';
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    phone: '',
    status: '',
    dateRange: []
  });
  handleSearch();
};

const handleExport = async () => {
  try {
    if (tableData.value.length === 0) {
      ElMessage.warning('暂无数据可导出');
      return;
    }
    
    // 定义导出列配置
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'phone', label: '手机号', width: 120 },
      { prop: 'content', label: '短信内容', width: 300 },
      { prop: 'templateName', label: '模板名称', width: 150 },
      { prop: 'channelName', label: '发送渠道', width: 120 },
      { 
        prop: 'status', 
        label: '发送状态', 
        width: 100,
        formatter: ExcelExporter.formatStatus
      },
      { 
        prop: 'sendTime', 
        label: '发送时间', 
        width: 180,
        formatter: ExcelExporter.formatDateTime
      },
      { 
        prop: 'receiveTime', 
        label: '接收时间', 
        width: 180,
        formatter: ExcelExporter.formatDateTime
      }
    ];
    
    // 汇总信息
    const summary = {
      '短信总数': totalSms.value,
      '发送成功': successSms.value,
      '发送中': sendingSms.value,
      '发送失败': failedSms.value,
      '成功率': `${((successSms.value / totalSms.value) * 100).toFixed(2)}%`,
      '导出时间': new Date().toLocaleString('zh-CN'),
      '查询条件': `${searchForm.phone ? '手机号: ' + searchForm.phone : ''}${searchForm.status ? ', 状态: ' + getStatusName(searchForm.status) : ''}${searchForm.dateRange && searchForm.dateRange.length ? ', 时间范围: ' + searchForm.dateRange.join(' 至 ') : ''}`
    };
    
    // 导出Excel
    await ExcelExporter.exportStatisticsReport(
      summary,
      tableData.value,
      columns,
      'sms_statistics',
      '短信统计报表'
    );
    
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败: ' + error.message);
  }
};

const handleViewDetail = (row) => {
  ElMessage.info(`查看短信详情: ${row.id}`);
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.sms-statistics {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card.success .stat-number {
  color: #67c23a;
}

.stat-card.warning .stat-number {
  color: #e6a23c;
}

.stat-card.danger .stat-number {
  color: #f56c6c;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}
</style>