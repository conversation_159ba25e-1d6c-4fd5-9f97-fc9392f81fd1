/**
 * T001-T010: 登录认证功能测试
 * 基于需求文档中的用户认证与权限管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class LoginTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T001: 登录页面加载测试
   */
  async testT001_LoginPageLoad() {
    const testId = 'T001';
    console.log(`\n🧪 执行测试 ${testId}: 登录页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 访问登录页面
      await this.testHelper.navigateTo('/login');
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isUsernameVisible = await this.testHelper.verifyElementVisibility(selectors.login.usernameInput);
      const isPasswordVisible = await this.testHelper.verifyElementVisibility(selectors.login.passwordInput);
      const isLoginButtonVisible = await this.testHelper.verifyElementVisibility(selectors.login.loginButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '登录页面加载测试',
        testContent: '验证登录页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的登录界面，为后续登录操作提供基础',
        testInput: '访问登录页面URL: http://localhost:3000/login',
        expectedOutput: '页面正常加载，显示用户名输入框、密码输入框和登录按钮',
        actualOutput: `用户名输入框: ${isUsernameVisible ? '✅显示' : '❌隐藏'}, 密码输入框: ${isPasswordVisible ? '✅显示' : '❌隐藏'}, 登录按钮: ${isLoginButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isUsernameVisible && isPasswordVisible && isLoginButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '登录页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T002: 有效用户登录测试
   */
  async testT002_ValidUserLogin() {
    const testId = 'T002';
    console.log(`\n🧪 执行测试 ${testId}: 有效用户登录测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行登录
      const loginResult = await this.testHelper.login(testData.auth.validUser);
      
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 验证登录结果
      const isOnDashboard = await this.testHelper.verifyURL('/dashboard');
      
      const result = {
        testId: testId,
        testName: '有效用户登录测试',
        testContent: '使用有效的用户名和密码进行登录操作',
        testPurpose: '验证系统能够正确处理有效的登录凭据并成功登录',
        testInput: `用户名: ${testData.auth.validUser.username}, 密码: ${testData.auth.validUser.password}`,
        expectedOutput: '登录成功，跳转到仪表板页面',
        actualOutput: `登录结果: ${loginResult.success ? '✅成功' : '❌失败'}, 页面跳转: ${isOnDashboard ? '✅正确' : '❌错误'}`,
        result: loginResult.success && isOnDashboard ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '有效用户登录测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T003: 无效用户登录测试
   */
  async testT003_InvalidUserLogin() {
    const testId = 'T003';
    console.log(`\n🧪 执行测试 ${testId}: 无效用户登录测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 执行登录
      const loginResult = await this.testHelper.login(testData.auth.invalidUser);
      
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      await this.screenshotHelper.takeErrorScreenshot();
      
      const result = {
        testId: testId,
        testName: '无效用户登录测试',
        testContent: '使用无效的用户名和密码进行登录操作',
        testPurpose: '验证系统能够正确识别和拒绝无效的登录凭据',
        testInput: `用户名: ${testData.auth.invalidUser.username}, 密码: ${testData.auth.invalidUser.password}`,
        expectedOutput: '登录失败，显示错误提示信息',
        actualOutput: `登录结果: ${!loginResult.success ? '✅失败' : '❌成功'}, 错误消息: ${errorMessage || '无'}`,
        result: !loginResult.success && errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '无效用户登录测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T004: 空用户名登录测试
   */
  async testT004_EmptyUsernameLogin() {
    const testId = 'T004';
    console.log(`\n🧪 执行测试 ${testId}: 空用户名登录测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      await this.testHelper.navigateTo('/login');
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 只填写密码
      await this.testHelper.page.fill(selectors.login.passwordInput, 'testpass');
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 点击登录按钮
      await this.testHelper.page.click(selectors.login.loginButton);
      await this.testHelper.wait(testData.timeouts.short);
      
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 验证是否仍在登录页面
      const isOnLoginPage = await this.testHelper.verifyURL('/login');
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空用户名登录测试',
        testContent: '在用户名为空的情况下尝试登录',
        testPurpose: '验证系统对空用户名的验证机制',
        testInput: '用户名: (空), 密码: testpass',
        expectedOutput: '登录失败，显示用户名不能为空的提示',
        actualOutput: `页面状态: ${isOnLoginPage ? '✅停留在登录页' : '❌跳转了'}, 错误消息: ${errorMessage || '无'}`,
        result: isOnLoginPage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空用户名登录测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T005: 空密码登录测试
   */
  async testT005_EmptyPasswordLogin() {
    const testId = 'T005';
    console.log(`\n🧪 执行测试 ${testId}: 空密码登录测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      await this.testHelper.navigateTo('/login');
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 只填写用户名
      await this.testHelper.page.fill(selectors.login.usernameInput, 'testuser');
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 点击登录按钮
      await this.testHelper.page.click(selectors.login.loginButton);
      await this.testHelper.wait(testData.timeouts.short);
      
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 验证是否仍在登录页面
      const isOnLoginPage = await this.testHelper.verifyURL('/login');
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空密码登录测试',
        testContent: '在密码为空的情况下尝试登录',
        testPurpose: '验证系统对空密码的验证机制',
        testInput: '用户名: testuser, 密码: (空)',
        expectedOutput: '登录失败，显示密码不能为空的提示',
        actualOutput: `页面状态: ${isOnLoginPage ? '✅停留在登录页' : '❌跳转了'}, 错误消息: ${errorMessage || '无'}`,
        result: isOnLoginPage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空密码登录测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有登录测试
   */
  async runAllTests() {
    console.log('🚀 开始执行登录功能测试套件 (T001-T005)');
    
    const startTime = Date.now();
    
    await this.testT001_LoginPageLoad();
    await this.testT002_ValidUserLogin();
    await this.testT003_InvalidUserLogin();
    await this.testT004_EmptyUsernameLogin();
    await this.testT005_EmptyPasswordLogin();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    
    console.log('\n📊 登录功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const loginTest = new LoginTest();
  loginTest.runAllTests().catch(console.error);
}

module.exports = LoginTest;
