<template>
  <div class="access-channel">
    <div class="page-header">
      <h2>接入渠道</h2>
      <div class="actions">
        <el-button 
          v-if="permissions.add" 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增渠道
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="渠道名称">
          <el-input v-model="searchForm.channelName" placeholder="请输入渠道名称" />
        </el-form-item>
        <el-form-item label="渠道类型">
          <el-select v-model="searchForm.channelType" placeholder="请选择渠道类型">
            <el-option label="短信渠道" value="SMS" />
            <el-option label="邮件渠道" value="EMAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="channelName" label="渠道名称" />
        <el-table-column prop="channelCode" label="渠道编码" />
        <el-table-column prop="channelType" label="渠道类型">
          <template #default="{ row }">
            <el-tag :type="row.channelType === 'SMS' ? 'primary' : 'success'">
              {{ row.channelType === 'SMS' ? '短信' : '邮件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="provider" label="服务商" />
        <el-table-column prop="priority" label="优先级" width="80" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" />
        <el-table-column label="操作" width="200" v-if="permissions.edit || permissions.delete">
          <template #default="{ row }">
            <el-button 
              v-if="permissions.view"
              type="text" 
              size="small" 
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              v-if="permissions.edit"
              type="text" 
              size="small" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="permissions.delete"
              type="text" 
              size="small" 
              @click="handleDelete(row)"
              class="danger"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserInfo } from '@/utils/auth';
import { getPageButtonPermissions } from '@/utils/permission-buttons';

// 权限控制
const userInfo = getUserInfo();
const permissions = computed(() => {
  return getPageButtonPermissions('channel:access', userInfo?.permissions || []);
});

// 数据定义
const loading = ref(false);
const tableData = ref([]);

// 搜索表单
const searchForm = reactive({
  channelName: '',
  channelType: '',
  status: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        channelName: '阿里云短信',
        channelCode: 'ALIYUN_SMS',
        channelType: 'SMS',
        provider: '阿里云',
        priority: 1,
        status: 1,
        createdTime: '2025-01-01 10:00:00'
      },
      {
        id: 2,
        channelName: '腾讯云邮件',
        channelCode: 'TENCENT_EMAIL',
        channelType: 'EMAIL',
        provider: '腾讯云',
        priority: 2,
        status: 1,
        createdTime: '2025-01-01 11:00:00'
      }
    ];
    pagination.total = 2;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    channelName: '',
    channelType: '',
    status: ''
  });
  handleSearch();
};

const handleAdd = () => {
  ElMessage.info('新增渠道功能待实现');
};

const handleView = (row) => {
  ElMessage.info(`查看渠道: ${row.channelName}`);
};

const handleEdit = (row) => {
  ElMessage.info(`编辑渠道: ${row.channelName}`);
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除渠道"${row.channelName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.access-channel {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.danger {
  color: #f56c6c;
}
</style>