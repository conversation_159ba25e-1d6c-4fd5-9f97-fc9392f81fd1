<template>
  <div class="log-container">
    <el-tabs v-model="activeTab" class="log-tabs">
      <!-- 操作日志 -->
      <el-tab-pane label="操作日志" name="operation">
        <div class="search-form">
          <el-form :model="operationQuery" inline>
            <el-form-item label="用户名">
              <el-input v-model="operationQuery.username" placeholder="请输入用户名" clearable />
            </el-form-item>
            <el-form-item label="操作类型">
              <el-input v-model="operationQuery.operation" placeholder="请输入操作类型" clearable />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="operationQuery.status" placeholder="请选择状态" clearable>
                <el-option label="成功" :value="1" />
                <el-option label="失败" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="operationTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchOperationLogs">查询</el-button>
              <el-button @click="resetOperationQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <el-table :data="operationLogs" v-loading="operationLoading" stripe>
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="operation" label="操作类型" width="150" />
          <el-table-column prop="method" label="请求方法" width="100" />
          <el-table-column prop="params" label="请求参数" width="200" show-overflow-tooltip />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="browser" label="浏览器" width="120" show-overflow-tooltip />
          <el-table-column prop="os" label="操作系统" width="120" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="costTime" label="执行时间(ms)" width="120" />
          <el-table-column prop="createTime" label="操作时间" width="180" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }">
              <el-button type="text" @click="viewOperationDetail(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <el-pagination
          v-model:current-page="operationPagination.current"
          v-model:page-size="operationPagination.size"
          :total="operationPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleOperationSizeChange"
          @current-change="handleOperationCurrentChange"
        />
      </el-tab-pane>
      
      <!-- 登录日志 -->
      <el-tab-pane label="登录日志" name="login">
        <div class="search-form">
          <el-form :model="loginQuery" inline>
            <el-form-item label="用户名">
              <el-input v-model="loginQuery.username" placeholder="请输入用户名" clearable />
            </el-form-item>
            <el-form-item label="登录类型">
              <el-input v-model="loginQuery.loginType" placeholder="请输入登录类型" clearable />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="loginQuery.status" placeholder="请选择状态" clearable>
                <el-option label="成功" :value="1" />
                <el-option label="失败" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <el-date-picker
                v-model="loginTimeRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchLoginLogs">查询</el-button>
              <el-button @click="resetLoginQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <el-table :data="loginLogs" v-loading="loginLoading" stripe>
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="loginType" label="登录类型" width="100" />
          <el-table-column prop="ip" label="IP地址" width="120" />
          <el-table-column prop="location" label="登录地点" width="150" />
          <el-table-column prop="browser" label="浏览器" width="120" show-overflow-tooltip />
          <el-table-column prop="os" label="操作系统" width="120" show-overflow-tooltip />
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="登录信息" width="200" show-overflow-tooltip />
          <el-table-column prop="loginTime" label="登录时间" width="180" />
        </el-table>
        
        <el-pagination
          v-model:current-page="loginPagination.current"
          v-model:page-size="loginPagination.size"
          :total="loginPagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleLoginSizeChange"
          @current-change="handleLoginCurrentChange"
        />
      </el-tab-pane>
    </el-tabs>
    
    <!-- 操作日志详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="操作日志详情" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户名">{{ currentDetail.username }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ currentDetail.operation }}</el-descriptions-item>
        <el-descriptions-item label="请求方法">{{ currentDetail.method }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentDetail.ip }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ currentDetail.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ currentDetail.os }}</el-descriptions-item>
        <el-descriptions-item label="执行时间">{{ currentDetail.costTime }}ms</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentDetail.status === 1 ? 'success' : 'danger'">
            {{ currentDetail.status === 1 ? '成功' : '失败' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ currentDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="地理位置">{{ currentDetail.location || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="请求参数" :span="2">
          <pre>{{ currentDetail.params || '无' }}</pre>
        </el-descriptions-item>
        <el-descriptions-item label="响应结果" :span="2">
          <pre>{{ currentDetail.result || '无' }}</pre>
        </el-descriptions-item>
        <el-descriptions-item label="异常信息" :span="2" v-if="currentDetail.errorMsg">
          <pre class="error-message">{{ currentDetail.errorMsg }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getOperationLogs, getLoginLogs } from '@/api/log'

const activeTab = ref('operation')
const operationLoading = ref(false)
const loginLoading = ref(false)
const detailDialogVisible = ref(false)

const operationLogs = ref([])
const loginLogs = ref([])
const currentDetail = ref({})

const operationQuery = reactive({
  username: '',
  operation: '',
  status: null
})

const loginQuery = reactive({
  username: '',
  loginType: '',
  status: null
})

const operationTimeRange = ref([])
const loginTimeRange = ref([])

const operationPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const loginPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 查询操作日志
const searchOperationLogs = async () => {
  operationLoading.value = true
  try {
    const params = {
      current: operationPagination.current,
      size: operationPagination.size,
      ...operationQuery
    }
    
    if (operationTimeRange.value && operationTimeRange.value.length === 2) {
      params.startTime = operationTimeRange.value[0]
      params.endTime = operationTimeRange.value[1]
    }
    
    const response = await getOperationLogs(params)
    if (response.code === 200) {
      operationLogs.value = response.data.records
      operationPagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    operationLoading.value = false
  }
}

// 查询登录日志
const searchLoginLogs = async () => {
  loginLoading.value = true
  try {
    const params = {
      current: loginPagination.current,
      size: loginPagination.size,
      ...loginQuery
    }
    
    if (loginTimeRange.value && loginTimeRange.value.length === 2) {
      params.startTime = loginTimeRange.value[0]
      params.endTime = loginTimeRange.value[1]
    }
    
    const response = await getLoginLogs(params)
    if (response.code === 200) {
      loginLogs.value = response.data.records
      loginPagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loginLoading.value = false
  }
}

// 重置操作日志查询
const resetOperationQuery = () => {
  Object.assign(operationQuery, {
    username: '',
    operation: '',
    status: null
  })
  operationTimeRange.value = []
  operationPagination.current = 1
  searchOperationLogs()
}

// 重置登录日志查询
const resetLoginQuery = () => {
  Object.assign(loginQuery, {
    username: '',
    loginType: '',
    status: null
  })
  loginTimeRange.value = []
  loginPagination.current = 1
  searchLoginLogs()
}

// 查看操作日志详情
const viewOperationDetail = (row) => {
  currentDetail.value = { ...row }
  detailDialogVisible.value = true
}

// 操作日志分页处理
const handleOperationSizeChange = (size) => {
  operationPagination.size = size
  operationPagination.current = 1
  searchOperationLogs()
}

const handleOperationCurrentChange = (current) => {
  operationPagination.current = current
  searchOperationLogs()
}

// 登录日志分页处理
const handleLoginSizeChange = (size) => {
  loginPagination.size = size
  loginPagination.current = 1
  searchLoginLogs()
}

const handleLoginCurrentChange = (current) => {
  loginPagination.current = current
  searchLoginLogs()
}

onMounted(() => {
  searchOperationLogs()
})
</script>

<style scoped>
.log-container {
  padding: 20px;
}

.log-tabs {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.search-form {
  background: #f5f7fa;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
}

.error-message {
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}
</style>