import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/api/auth/login',
    method: 'post',
    data
  })
}

// 用户登出
export function logout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/api/auth/change-password',
    method: 'post',
    data
  })
}

// 验证令牌
export function validateToken() {
  return request({
    url: '/api/auth/validate',
    method: 'get'
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/api/auth/user-info',
    method: 'get'
  })
}

// 获取RSA公钥
export function getPublicKey() {
  return request({
    url: '/api/auth/public-key',
    method: 'get'
  })
}