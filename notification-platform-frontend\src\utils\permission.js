import { getUserInfo } from '@/utils/auth'

/**
 * 检查用户是否有指定权限
 * @param {string} permission 权限标识
 * @returns {boolean}
 */
export function hasPermission(permission) {
  try {
    const userInfo = getUserInfo()
    if (!userInfo || !userInfo.permissions) {
      return false
    }
    
    // 超级管理员拥有所有权限
    if (userInfo.roles && userInfo.roles.includes('admin')) {
      return true
    }
    
    // 检查用户权限列表
    return userInfo.permissions.includes(permission)
  } catch (error) {
    console.error('检查权限失败:', error)
    return false
  }
}

/**
 * 检查用户是否有任意一个权限
 * @param {Array} permissions 权限标识数组
 * @returns {boolean}
 */
export function hasAnyPermission(permissions) {
  if (!Array.isArray(permissions)) {
    return false
  }
  
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查用户是否拥有所有权限
 * @param {Array} permissions 权限标识数组
 * @returns {boolean}
 */
export function hasAllPermissions(permissions) {
  if (!Array.isArray(permissions)) {
    return false
  }
  
  return permissions.every(permission => hasPermission(permission))
}

/**
 * 检查用户是否有指定角色
 * @param {string} role 角色标识
 * @returns {boolean}
 */
export function hasRole(role) {
  try {
    const userInfo = getUserInfo()
    if (!userInfo || !userInfo.roles) {
      return false
    }
    
    return userInfo.roles.includes(role)
  } catch (error) {
    console.error('检查角色失败:', error)
    return false
  }
}

/**
 * 检查用户是否有任意一个角色
 * @param {Array} roles 角色标识数组
 * @returns {boolean}
 */
export function hasAnyRole(roles) {
  if (!Array.isArray(roles)) {
    return false
  }
  
  return roles.some(role => hasRole(role))
}

/**
 * 过滤用户有权限的菜单
 * @param {Array} menus 菜单数组
 * @returns {Array}
 */
export function filterMenusByPermission(menus) {
  return menus.filter(menu => {
    // 如果菜单没有权限要求，则显示
    if (!menu.permission) {
      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = filterMenusByPermission(menu.children)
      }
      return true
    }
    
    // 检查权限
    if (hasPermission(menu.permission)) {
      // 递归过滤子菜单
      if (menu.children && menu.children.length > 0) {
        menu.children = filterMenusByPermission(menu.children)
      }
      return true
    }
    
    return false
  })
}

/**
 * 获取用户权限列表
 * @returns {Array}
 */
export function getUserPermissions() {
  try {
    const userInfo = getUserInfo()
    return userInfo?.permissions || []
  } catch (error) {
    console.error('获取用户权限失败:', error)
    return []
  }
}

/**
 * 获取用户角色列表
 * @returns {Array}
 */
export function getUserRoles() {
  try {
    const userInfo = getUserInfo()
    return userInfo?.roles || []
  } catch (error) {
    console.error('获取用户角色失败:', error)
    return []
  }
}