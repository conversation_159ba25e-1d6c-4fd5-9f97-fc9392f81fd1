import { hasPermission, hasAnyPermission, hasRole, hasAnyRole } from '@/utils/permission'

/**
 * 权限指令
 * 用法：v-permission="'system:user'"
 */
export const permission = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      const hasAuth = hasPermission(value)
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('权限指令需要权限标识')
    }
  }
}

/**
 * 任意权限指令
 * 用法：v-permission-any="['system:user', 'system:role']"
 */
export const permissionAny = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value && Array.isArray(value)) {
      const hasAuth = hasAnyPermission(value)
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('任意权限指令需要权限标识数组')
    }
  }
}

/**
 * 角色指令
 * 用法：v-role="'admin'"
 */
export const role = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value) {
      const hasAuth = hasRole(value)
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('角色指令需要角色标识')
    }
  }
}

/**
 * 任意角色指令
 * 用法：v-role-any="['admin', 'manager']"
 */
export const roleAny = {
  mounted(el, binding) {
    const { value } = binding
    
    if (value && Array.isArray(value)) {
      const hasAuth = hasAnyRole(value)
      if (!hasAuth) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error('任意角色指令需要角色标识数组')
    }
  }
}

// 导出所有指令
export default {
  permission,
  permissionAny,
  role,
  roleAny
}