<testsuites id="" name="" tests="9" failures="0" skipped="0" errors="0" time="27.864738000000003">
<testsuite name="login.spec.js" timestamp="2025-07-29T10:05:34.121Z" hostname="chromium" tests="5" failures="0" skipped="0" time="9.48" errors="0">
<testcase name="登录功能测试 › T001-登录页面加载测试" classname="login.spec.js" time="1.668">
</testcase>
<testcase name="登录功能测试 › T002-用户名密码填写测试" classname="login.spec.js" time="1.443">
</testcase>
<testcase name="登录功能测试 › T003-登录提交测试" classname="login.spec.js" time="3.377">
</testcase>
<testcase name="登录功能测试 › T004-空用户名登录测试" classname="login.spec.js" time="1.572">
</testcase>
<testcase name="登录功能测试 › T005-空密码登录测试" classname="login.spec.js" time="1.42">
</testcase>
</testsuite>
<testsuite name="sms.spec.js" timestamp="2025-07-29T10:05:34.121Z" hostname="chromium" tests="4" failures="0" skipped="0" time="16.874" errors="0">
<testcase name="短信功能测试 › T006-短信发送功能完整流程测试" classname="sms.spec.js" time="7.559">
<system-out>
<![CDATA[发送前今日短信发送量: 21
发送后今日短信发送量: 21
发送量对比: 21 -> 21
]]>
</system-out>
</testcase>
<testcase name="短信功能测试 › T007-短信页面元素验证" classname="sms.spec.js" time="1.741">
</testcase>
<testcase name="短信功能测试 › T008-无效手机号码测试" classname="sms.spec.js" time="3.831">
</testcase>
<testcase name="短信功能测试 › T009-空短信内容测试" classname="sms.spec.js" time="3.743">
</testcase>
</testsuite>
</testsuites>