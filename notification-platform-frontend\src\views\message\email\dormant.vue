<template>
  <div class="dormant-account-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>休眠账户通知</span>
        </div>
      </template>
      
      <el-form
        ref="notificationFormRef"
        :model="notificationForm"
        :rules="notificationRules"
        label-width="120px"
        class="notification-form"
      >
        <el-form-item label="客户信息文件" prop="customerFile">
          <el-upload
            ref="uploadRef"
            :file-list="fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :auto-upload="false"
            :limit="1"
            accept=".xlsx,.xls"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">选择Excel文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                只能上传xlsx/xls文件，且不超过10MB
                <el-link type="primary" @click="downloadTemplate">下载模板</el-link>
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 显示解析结果 -->
        <el-form-item v-if="parsedCustomers.length > 0" label="解析结果">
          <div class="parsed-result">
            <div class="result-summary">
              共解析到 <strong>{{ parsedCustomers.length }}</strong> 个客户信息
              <el-button type="text" @click="showParsedCustomers = !showParsedCustomers">
                {{ showParsedCustomers ? '隐藏' : '查看' }}详情
              </el-button>
            </div>
            <div v-if="showParsedCustomers" class="customer-table">
              <el-table :data="parsedCustomers.slice(0, 10)" size="small" max-height="300">
                <el-table-column prop="customerName" label="客户姓名" width="120" />
                <el-table-column prop="email" label="邮箱地址" width="200" />
                <el-table-column prop="accountNumber" label="账户号码" width="150" />
                <el-table-column prop="lastLoginDate" label="最后登录" width="120" />
                <el-table-column prop="dormantDays" label="休眠天数" width="100" />
              </el-table>
              <div v-if="parsedCustomers.length > 10" class="more-tip">
                还有 {{ parsedCustomers.length - 10 }} 个客户...
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="通知模板" prop="templateId">
          <el-select
            v-model="notificationForm.templateId"
            placeholder="请选择通知模板"
            style="width: 100%"
            @change="handleTemplateChange"
          >
            <el-option
              v-for="template in templateList"
              :key="template.id"
              :label="template.templateName"
              :value="template.id"
            />
          </el-select>
        </el-form-item>

        <!-- 模板预览 -->
        <el-form-item v-if="selectedTemplate" label="模板预览">
          <div class="template-preview">
            <div class="preview-header">
              <strong>{{ selectedTemplate.templateName }}</strong>
              <el-button type="text" @click="showTemplatePreview = !showTemplatePreview">
                {{ showTemplatePreview ? '隐藏' : '显示' }}预览
              </el-button>
            </div>
            <div v-if="showTemplatePreview" class="template-content">
              <div class="content-item">
                <strong>邮件主题：</strong>{{ selectedTemplate.subject }}
              </div>
              <div class="content-item">
                <strong>邮件内容：</strong>
                <div class="content-body" v-html="selectedTemplate.content"></div>
              </div>
              <div class="content-item">
                <strong>参数说明：</strong>
                <ul class="parameter-list">
                  <li><code>{{customerName}}</code> - 客户姓名</li>
                  <li><code>{{accountNumber}}</code> - 账户号码</li>
                  <li><code>{{lastLoginDate}}</code> - 最后登录日期</li>
                  <li><code>{{dormantDays}}</code> - 休眠天数</li>
                </ul>
              </div>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="发送时间" prop="sendTime">
          <el-radio-group v-model="notificationForm.sendTimeType">
            <el-radio :label="1">立即发送</el-radio>
            <el-radio :label="2">定时发送</el-radio>
          </el-radio-group>
          <el-date-picker
            v-if="notificationForm.sendTimeType === 2"
            v-model="notificationForm.sendTime"
            type="datetime"
            placeholder="选择发送时间"
            style="margin-left: 20px;"
            :disabled-date="disabledDate"
          />
        </el-form-item>

        <el-form-item label="发送渠道" prop="channelCode">
          <el-select
            v-model="notificationForm.channelCode"
            placeholder="请选择发送渠道（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.channelCode"
              :label="channel.channelName"
              :value="channel.channelCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendDormantNotification"
          >
            {{ sending ? '发送中...' : (notificationForm.sendTimeType === 2 ? '定时发送' : '立即发送') }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="previewNotification">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="休眠账户通知预览"
      width="70%"
    >
      <div class="notification-preview">
        <div class="preview-item">
          <strong>通知数量：</strong>{{ parsedCustomers.length }} 个客户
        </div>
        <div class="preview-item">
          <strong>发送时间：</strong>
          {{ notificationForm.sendTimeType === 1 ? '立即发送' : formatDateTime(notificationForm.sendTime) }}
        </div>
        <div class="preview-item">
          <strong>模板名称：</strong>{{ selectedTemplate?.templateName }}
        </div>
        <div class="preview-item">
          <strong>示例内容：</strong>
          <div class="example-content">
            <div class="example-subject">
              <strong>主题：</strong>{{ renderTemplate(selectedTemplate?.subject, parsedCustomers[0]) }}
            </div>
            <div class="example-body" v-html="renderTemplate(selectedTemplate?.content, parsedCustomers[0])"></div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 发送结果对话框 -->
    <el-dialog
      v-model="resultVisible"
      title="发送结果"
      width="50%"
    >
      <div class="send-result">
        <div class="result-item">
          <strong>任务ID：</strong>{{ sendResult.taskId }}
        </div>
        <div class="result-item">
          <strong>发送状态：</strong>
          <el-tag :type="sendResult.status === 'SUCCESS' ? 'success' : 'warning'">
            {{ sendResult.status === 'SUCCESS' ? '发送成功' : '已提交' }}
          </el-tag>
        </div>
        <div class="result-item">
          <strong>总数量：</strong>{{ sendResult.totalCount }}
        </div>
        <div v-if="sendResult.status === 'SUCCESS'" class="result-item">
          <strong>成功数量：</strong>
          <span class="success-count">{{ sendResult.successCount }}</span>
        </div>
        <div v-if="sendResult.status === 'SUCCESS'" class="result-item">
          <strong>失败数量：</strong>
          <span class="failure-count">{{ sendResult.failureCount }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { sendDormantNotification as sendDormantNotificationApi, getEmailTemplateList } from '@/api/email'
import { getSendChannelsByType } from '@/api/channel'

// 表单数据
const notificationForm = reactive({
  templateId: '',
  sendTimeType: 1, // 1-立即发送，2-定时发送
  sendTime: null,
  channelCode: ''
})

// 表单验证规则
const notificationRules = {
  customerFile: [
    { 
      validator: (rule, value, callback) => {
        if (fileList.value.length === 0) {
          callback(new Error('请选择客户信息文件'))
        } else if (parsedCustomers.value.length === 0) {
          callback(new Error('文件中没有解析到有效的客户信息'))
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  templateId: [
    { required: true, message: '请选择通知模板', trigger: 'change' }
  ]
}

// 响应式数据
const notificationFormRef = ref()
const uploadRef = ref()
const sending = ref(false)
const previewVisible = ref(false)
const resultVisible = ref(false)
const showParsedCustomers = ref(false)
const showTemplatePreview = ref(false)
const fileList = ref([])
const parsedCustomers = ref([])
const templateList = ref([])
const channelList = ref([])
const sendResult = reactive({
  taskId: '',
  status: '',
  totalCount: 0,
  successCount: 0,
  failureCount: 0
})

// 计算属性
const selectedTemplate = computed(() => {
  return templateList.value.find(t => t.id === notificationForm.templateId)
})

// 获取发送渠道列表
const getChannelList = async () => {
  try {
    const response = await getSendChannelsByType(2) // 2-邮件
    if (response.code === 200) {
      channelList.value = response.data || []
      // 默认选择第一个渠道
      if (channelList.value.length > 0) {
        notificationForm.channelCode = channelList.value[0].channelCode
      }
    }
  } catch (error) {
    console.error('获取发送渠道失败:', error)
  }
}

// 获取邮件模板列表
const getTemplateList = async () => {
  try {
    const response = await getEmailTemplateList({ templateType: 'DORMANT_ACCOUNT' })
    if (response.code === 200) {
      templateList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取邮件模板失败:', error)
  }
}

// 文件上传处理
const handleFileChange = (file, files) => {
  // 检查文件类型
  const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    files.splice(files.indexOf(file), 1)
    return
  }

  // 检查文件大小
  if (file.size > 10 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过10MB')
    files.splice(files.indexOf(file), 1)
    return
  }

  fileList.value = files
  parseExcelFile(file.raw)
}

// 文件移除处理
const handleFileRemove = (file, files) => {
  fileList.value = files
  parsedCustomers.value = []
}

// 文件数量超限处理
const handleExceed = () => {
  ElMessage.warning('只能上传一个Excel文件')
}

// 解析Excel文件
const parseExcelFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      const customers = []
      // 跳过表头
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i]
        if (row && row.length >= 5) {
          const customer = {
            customerName: String(row[0] || '').trim(),
            email: String(row[1] || '').trim(),
            accountNumber: String(row[2] || '').trim(),
            lastLoginDate: String(row[3] || '').trim(),
            dormantDays: parseInt(row[4]) || 0
          }
          
          // 验证必要字段
          if (customer.customerName && customer.email && customer.accountNumber) {
            // 验证邮箱格式
            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
            if (emailPattern.test(customer.email)) {
              customers.push(customer)
            }
          }
        }
      }
      
      parsedCustomers.value = customers
      
      if (parsedCustomers.value.length === 0) {
        ElMessage.warning('Excel文件中没有找到有效的客户信息')
      } else {
        ElMessage.success(`成功解析到${parsedCustomers.value.length}个客户信息`)
      }
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      ElMessage.error('解析Excel文件失败')
    }
  }
  reader.readAsArrayBuffer(file)
}

// 下载模板
const downloadTemplate = () => {
  const data = [
    ['客户姓名', '邮箱地址', '账户号码', '最后登录日期', '休眠天数'],
    ['张三', '<EMAIL>', '**********', '2024-01-15', '90'],
    ['李四', '<EMAIL>', '**********', '2024-02-20', '60'],
    ['王五', '<EMAIL>', '1234567892', '2024-03-10', '45']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '客户信息')
  XLSX.writeFile(workbook, '休眠账户通知模板.xlsx')
}

// 模板选择处理
const handleTemplateChange = (templateId) => {
  // 可以在这里处理模板选择后的逻辑
}

// 渲染模板
const renderTemplate = (template, customer) => {
  if (!template || !customer) return ''
  
  return template
    .replace(/\{\{customerName\}\}/g, customer.customerName)
    .replace(/\{\{email\}\}/g, customer.email)
    .replace(/\{\{accountNumber\}\}/g, customer.accountNumber)
    .replace(/\{\{lastLoginDate\}\}/g, customer.lastLoginDate)
    .replace(/\{\{dormantDays\}\}/g, customer.dormantDays)
}

// 禁用过去的日期
const disabledDate = (time) => {
  return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
}

// 格式化日期时间
const formatDateTime = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleString('zh-CN')
}

// 发送休眠账户通知
const sendDormantNotification = async () => {
  try {
    // 表单验证
    await notificationFormRef.value.validate()

    if (parsedCustomers.value.length === 0) {
      ElMessage.error('请上传客户信息文件')
      return
    }

    // 验证定时发送时间
    if (notificationForm.sendTimeType === 2) {
      if (!notificationForm.sendTime) {
        ElMessage.error('请选择发送时间')
        return
      }
      if (new Date(notificationForm.sendTime).getTime() <= Date.now()) {
        ElMessage.error('发送时间必须是未来时间')
        return
      }
    }

    sending.value = true

    const notificationData = {
      customers: parsedCustomers.value,
      templateId: notificationForm.templateId,
      sendTimeType: notificationForm.sendTimeType
    }

    // 定时发送
    if (notificationForm.sendTimeType === 2) {
      notificationData.sendTime = notificationForm.sendTime
    }

    // 如果指定了渠道
    if (notificationForm.channelCode) {
      notificationData.channelCode = notificationForm.channelCode
    }

    const response = await sendDormantNotificationApi(notificationData)

    if (response.code === 200) {
      // 显示发送结果
      Object.assign(sendResult, response.data)
      resultVisible.value = true
      
      if (notificationForm.sendTimeType === 1) {
        ElMessage.success('休眠账户通知发送完成')
      } else {
        ElMessage.success('休眠账户通知已提交定时发送')
      }
    } else {
      ElMessage.error(response.message || '休眠账户通知发送失败')
    }
  } catch (error) {
    console.error('发送休眠账户通知失败:', error)
    ElMessage.error('发送休眠账户通知失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  notificationFormRef.value.resetFields()
  fileList.value = []
  parsedCustomers.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 预览通知
const previewNotification = async () => {
  try {
    await notificationFormRef.value.validate()
    if (parsedCustomers.value.length === 0) {
      ElMessage.warning('请先上传客户信息文件')
      return
    }
    previewVisible.value = true
  } catch (error) {
    ElMessage.warning('请先完善通知信息')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  getChannelList()
  getTemplateList()
})
</script>

<style scoped>
.dormant-account-container {
  padding: 20px;
}

.box-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.notification-form {
  margin-top: 20px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.parsed-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.result-summary {
  margin-bottom: 10px;
}

.customer-table {
  margin-top: 10px;
}

.more-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
  text-align: center;
}

.template-preview {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.template-content {
  margin-top: 10px;
}

.content-item {
  margin-bottom: 15px;
}

.content-body {
  margin-top: 5px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  max-height: 200px;
  overflow-y: auto;
}

.parameter-list {
  margin: 5px 0;
  padding-left: 20px;
}

.parameter-list li {
  margin: 5px 0;
}

.parameter-list code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.notification-preview {
  padding: 20px;
}

.preview-item {
  margin-bottom: 15px;
  line-height: 1.6;
}

.example-content {
  margin-top: 10px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.example-subject {
  margin-bottom: 10px;
  font-size: 16px;
}

.example-body {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  max-height: 300px;
  overflow-y: auto;
}

.send-result {
  padding: 20px;
}

.result-item {
  margin-bottom: 15px;
  line-height: 1.6;
  font-size: 16px;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
}

.failure-count {
  color: #f56c6c;
  font-weight: bold;
}
</style>