<template>
  <div class="template-type-container">
    <!-- 查询表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板类型编号">
          <el-input v-model="searchForm.typeCode" placeholder="请输入模板类型编号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="模板类型名称">
          <el-input v-model="searchForm.typeName" placeholder="请输入模板类型名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">模板类型新增</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="typeCode" label="模板类型编号" width="150" />
        <el-table-column prop="typeName" label="模板类型名称" width="200" />
        <el-table-column prop="description" label="描述" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="修改时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">修改</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 操作按钮 -->
      <div class="table-actions">
        <el-button type="primary" :disabled="selectedRows.length === 0" @click="handleBatchEdit">修改</el-button>
        <el-button type="danger" :disabled="selectedRows.length === 0" @click="handleBatchDelete">删除</el-button>
        <el-button @click="handleReturn">返回</el-button>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="500px" @close="handleDialogClose">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="模板类型编号" prop="typeCode">
          <el-input v-model="formData.typeCode" placeholder="请输入模板类型编号" :disabled="isEdit" />
        </el-form-item>
        <el-form-item label="模板类型名称" prop="typeName">
          <el-input v-model="formData.typeName" placeholder="请输入模板类型名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入描述" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>

    <!-- 批量修改对话框 -->
    <el-dialog v-model="batchEditVisible" title="批量修改" width="400px">
      <el-form :model="batchForm" label-width="100px">
        <el-form-item label="状态">
          <el-radio-group v-model="batchForm.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="batchEditVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchEditSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getTemplateTypePage, createTemplateType, updateTemplateType, deleteTemplateType, batchDeleteTemplateTypes, updateTemplateTypeStatus } from '@/api/templateType'

const loading = ref(false)
const dialogVisible = ref(false)
const batchEditVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()
const selectedRows = ref([])

const searchForm = reactive({
  typeCode: '',
  typeName: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const tableData = ref([])

const formData = reactive({
  id: null,
  typeCode: '',
  typeName: '',
  description: '',
  status: 1
})

const batchForm = reactive({
  status: 1
})

const formRules = {
  typeCode: [{ required: true, message: '请输入模板类型编号', trigger: 'blur' }],
  typeName: [{ required: true, message: '请输入模板类型名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getTemplateTypePage(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    typeCode: '',
    typeName: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogTitle.value = '模板类型新增'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '模板类型修改'
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除模板类型“${row.typeName}”吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteTemplateType(row.id)
    if (response.code === 200) {
      ElMessage.success({
        message: '✓ 删除成功',
        showClose: false
      })
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleBatchEdit = () => {
  if (selectedRows.value.length === 1) {
    handleEdit(selectedRows.value[0])
  } else {
    batchEditVisible.value = true
  }
}

const handleBatchEditSubmit = async () => {
  try {
    const promises = selectedRows.value.map(row => 
      updateTemplateTypeStatus(row.id, batchForm.status)
    )
    await Promise.all(promises)
    
    ElMessage.success({
      message: '✓ 批量修改成功',
      showClose: false
    })
    batchEditVisible.value = false
    loadData()
  } catch (error) {
    ElMessage.error('批量修改失败')
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的${selectedRows.value.length}个模板类型吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const ids = selectedRows.value.map(row => row.id)
    const response = await batchDeleteTemplateTypes(ids)
    if (response.code === 200) {
      ElMessage.success({
        message: '✓ 批量删除成功',
        showClose: false
      })
      loadData()
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const handleReturn = () => {
  // 返回上一级页面的逻辑
  window.history.back()
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const apiCall = isEdit.value ? updateTemplateType : createTemplateType
    const params = isEdit.value ? [formData.id, formData] : [formData]
    const response = await apiCall(...params)
    
    if (response.code === 200) {
      ElMessage.success({
        message: isEdit.value ? '✓ 修改成功' : '✓ 新增成功',
        showClose: false
      })
      dialogVisible.value = false
      loadData()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败', error)
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    typeCode: '',
    typeName: '',
    description: '',
    status: 1
  })
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.template-type-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-actions {
  margin-top: 10px;
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>