# 通知平台前端自动化测试

这个项目包含了完整的Playwright自动化测试套件，用于测试通知平台前端的各项功能。

## 🚀 快速开始

### 方法一：使用批处理文件（推荐）
```bash
# 双击运行或在命令行执行
run-tests.bat
```

### 方法二：使用npm命令（推荐）
```bash
# 运行标准Playwright测试（推荐）
npm run test:playwright

# 运行所有测试并生成汇总报告
npm run test:all

# 运行测试（显示浏览器界面）
npm run test:all:headed

# 查看测试报告
npm run test:report
```

### 方法三：直接使用Playwright
```bash
# 运行所有测试
npx playwright test

# 运行特定测试文件
npx playwright test tests/login.spec.js
npx playwright test tests/sms.spec.js

# 运行测试并显示浏览器
npx playwright test --headed

# 运行测试UI模式
npx playwright test --ui
```

## 📁 项目结构

```
notification-platform-frontend/
├── tests/                          # 标准化测试文件
│   ├── login.spec.js               # 登录功能测试
│   └── sms.spec.js                 # 短信功能测试
├── test/                           # 原始测试脚本（保留）
│   ├── login-test/
│   └── sms-test/
├── test-results/                   # 测试结果输出目录
│   ├── screenshots/                # 操作截图
│   ├── html-report/               # Playwright HTML报告
│   ├── detailed-report.html       # 详细汇总报告
│   ├── test-summary.json          # JSON格式测试汇总
│   └── results.json               # 原始测试结果
├── playwright.config.js           # Playwright配置文件
├── run-all-tests.js              # 测试运行脚本
├── run-tests.bat                 # Windows批处理文件
└── TEST-README.md                # 测试说明文档
```

## 🧪 测试内容

### 登录功能测试 (login.spec.js)
- ✅ 登录页面加载测试
- ✅ 用户名密码填写测试
- ✅ 登录提交测试
- ✅ 空用户名登录测试
- ✅ 空密码登录测试

### 短信功能测试 (sms.spec.js)
- ✅ 短信发送功能完整流程测试
- ✅ 短信页面元素验证
- ✅ 无效手机号码测试
- ✅ 空短信内容测试

## 📊 测试报告

测试完成后会生成多种格式的报告：

### 1. 详细汇总报告 (`test-results/detailed-report.html`)
- 📈 测试结果统计
- 📋 详细测试列表
- 📸 操作截图展示
- 🎨 美观的HTML界面

### 2. Playwright官方报告 (`test-results/html-report/index.html`)
- 🔍 详细的测试执行信息
- 📹 测试录像（失败时）
- 🐛 错误堆栈信息
- 📊 时间线分析

### 3. JSON汇总 (`test-results/test-summary.json`)
- 📄 机器可读的测试结果
- 🔢 统计数据
- 📝 测试详情

## 📸 截图功能

测试过程中会自动截图，包括：
- 🖼️ 关键操作步骤截图
- ❌ 测试失败时的错误截图
- 📱 页面状态变化截图

所有截图保存在 `test-results/screenshots/` 目录中。

## ⚙️ 配置说明

### Playwright配置 (`playwright.config.js`)
- 🌐 基础URL: `http://localhost:3000`
- 🖥️ 浏览器: Chromium (Desktop Chrome)
- 📹 录像: 失败时保留
- 📸 截图: 失败时截图
- 🔄 重试: CI环境2次，本地0次

### 测试运行配置
- 🚫 非并行执行（避免数据冲突）
- 👀 非无头模式（可观察测试过程）
- 🔧 自动启动开发服务器

## 🛠️ 自定义测试

### 添加新测试
1. 在 `tests/` 目录下创建新的 `.spec.js` 文件
2. 使用Playwright测试语法编写测试
3. 运行 `npm run test:all` 执行所有测试

### 修改测试配置
- 编辑 `playwright.config.js` 修改全局配置
- 编辑 `run-all-tests.js` 修改报告生成逻辑

## 🔧 故障排除

### 常见问题

1. **测试失败：页面加载超时**
   - 确保前端服务正在运行 (`npm run dev`)
   - 检查端口3000是否被占用

2. **登录测试失败**
   - 确认用户名密码是否正确
   - 检查登录接口是否正常

3. **截图文件夹不存在**
   - 脚本会自动创建必要的目录
   - 确保有写入权限

### 调试模式
```bash
# 使用调试模式运行特定测试
npx playwright test tests/login.spec.js --debug

# 使用UI模式
npx playwright test --ui
```

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js版本 (建议16+)
2. 依赖是否正确安装
3. 前端服务是否正常运行
4. 网络连接是否正常

---

**注意**: 首次运行时，Playwright会自动下载所需的浏览器，这可能需要一些时间。
