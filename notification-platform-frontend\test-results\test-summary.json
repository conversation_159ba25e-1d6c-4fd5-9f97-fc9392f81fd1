{"summary": {"total": 9, "passed": 9, "failed": 0, "skipped": 0, "startTime": "2025-07-29T10:05:32.649Z", "endTime": "2025-07-29T10:06:01.963Z", "duration": 29314}, "tests": [{"title": "T001-登录页面加载测试", "testId": "T001", "fullTitle": "登录功能测试 > T001-登录页面加载测试", "status": "passed", "duration": 1668, "error": null, "screenshots": [{"name": "T001-login-initial.png", "path": "screenshots/T001-login-initial.png", "size": 202378, "description": "登录页面初始状态", "createdTime": "2025-07-29T10:05:36.560Z", "createdTimeStr": "2025/7/29 18:05:36"}, {"name": "T001-elements-verified.png", "path": "screenshots/T001-elements-verified.png", "size": 201965, "description": "页面元素验证完成", "createdTime": "2025-07-29T10:05:36.730Z", "createdTimeStr": "2025/7/29 18:05:36"}], "testDetails": {"content": "验证登录页面能够正常加载并显示所有必要的UI元素", "purpose": "确保用户能够看到完整的登录界面，为后续登录操作提供基础", "input": "访问登录页面URL: http://localhost:3000/login", "expectedOutput": "页面正常加载，显示用户名输入框、密码输入框和登录按钮", "testSteps": ["1. 访问登录页面", "2. 等待页面加载完成", "3. 验证登录容器存在", "4. 验证用户名输入框可见", "5. 验证密码输入框可见", "6. 验证登录按钮可见"]}}, {"title": "T002-用户名密码填写测试", "testId": "T002", "fullTitle": "登录功能测试 > T002-用户名密码填写测试", "status": "passed", "duration": 1443, "error": null, "screenshots": [{"name": "T002-username-filled.png", "path": "screenshots/T002-username-filled.png", "size": 202499, "description": "用户名填写完成", "createdTime": "2025-07-29T10:05:37.959Z", "createdTimeStr": "2025/7/29 18:05:37"}, {"name": "T002-form-completed.png", "path": "screenshots/T002-form-completed.png", "size": 201106, "description": "表单填写完成", "createdTime": "2025-07-29T10:05:38.128Z", "createdTimeStr": "2025/7/29 18:05:38"}], "testDetails": {"content": "验证用户名和密码输入框的填写功能是否正常工作", "purpose": "确保用户能够正常输入登录凭据，验证表单输入功能", "input": "用户名: testuser, 密码: testpass", "expectedOutput": "输入框能够正常接收和显示输入内容", "testSteps": ["1. 访问登录页面", "2. 在用户名输入框中输入\"testuser\"", "3. 验证用户名输入成功", "4. 在密码输入框中输入\"testpass\"", "5. 验证密码输入成功", "6. 确认表单填写完整"]}}, {"title": "T003-登录提交测试", "testId": "T003", "fullTitle": "登录功能测试 > T003-登录提交测试", "status": "passed", "duration": 3377, "error": null, "screenshots": [{"name": "T003-before-submit.png", "path": "screenshots/T003-before-submit.png", "size": 201112, "description": "提交前状态", "createdTime": "2025-07-29T10:05:39.391Z", "createdTimeStr": "2025/7/29 18:05:39"}, {"name": "T003-after-submit.png", "path": "screenshots/T003-after-submit.png", "size": 47458, "description": "提交后状态", "createdTime": "2025-07-29T10:05:41.540Z", "createdTimeStr": "2025/7/29 18:05:41"}], "testDetails": {"content": "验证登录提交功能，测试用户点击登录按钮后的系统响应", "purpose": "确保登录流程能够正常执行，验证系统对登录请求的处理", "input": "用户名: admin, 密码: Admin123!", "expectedOutput": "系统处理登录请求，根据凭据有效性给出相应响应", "testSteps": ["1. 访问登录页面", "2. 输入有效的用户名和密码", "3. 点击登录按钮", "4. 等待系统响应", "5. 验证登录结果（成功跳转或错误提示）"]}}, {"title": "T004-空用户名登录测试", "testId": "T004", "fullTitle": "登录功能测试 > T004-空用户名登录测试", "status": "passed", "duration": 1572, "error": null, "screenshots": [{"name": "T004-empty-username-state.png", "path": "screenshots/T004-empty-username-state.png", "size": 202522, "description": "用户名为空状态", "createdTime": "2025-07-29T10:05:42.818Z", "createdTimeStr": "2025/7/29 18:05:42"}, {"name": "T004-validation-result.png", "path": "screenshots/T004-validation-result.png", "size": 201112, "description": "验证结果", "createdTime": "2025-07-29T10:05:43.017Z", "createdTimeStr": "2025/7/29 18:05:43"}], "testDetails": {"content": "验证空用户名情况下的登录验证机制", "purpose": "确保系统能够正确处理用户名为空的异常情况，提供适当的错误提示", "input": "用户名: (空), 密码: testpass", "expectedOutput": "系统显示用户名不能为空的错误提示或阻止提交", "testSteps": ["1. 访问登录页面", "2. 保持用户名输入框为空", "3. 在密码输入框中输入密码", "4. 点击登录按钮", "5. 验证系统的错误处理机制"]}}, {"title": "T005-空密码登录测试", "testId": "T005", "fullTitle": "登录功能测试 > T005-空密码登录测试", "status": "passed", "duration": 1420, "error": null, "screenshots": [{"name": "T005-empty-password-state.png", "path": "screenshots/T005-empty-password-state.png", "size": 202503, "description": "密码为空状态", "createdTime": "2025-07-29T10:05:44.291Z", "createdTimeStr": "2025/7/29 18:05:44"}, {"name": "T005-validation-result.png", "path": "screenshots/T005-validation-result.png", "size": 201640, "description": "验证结果", "createdTime": "2025-07-29T10:05:44.476Z", "createdTimeStr": "2025/7/29 18:05:44"}], "testDetails": {"content": "验证空密码情况下的登录验证机制", "purpose": "确保系统能够正确处理密码为空的异常情况，提供适当的错误提示", "input": "用户名: testuser, 密码: (空)", "expectedOutput": "系统显示密码不能为空的错误提示或阻止提交", "testSteps": ["1. 访问登录页面", "2. 在用户名输入框中输入用户名", "3. 保持密码输入框为空", "4. 点击登录按钮", "5. 验证系统的错误处理机制"]}}, {"title": "T006-短信发送功能完整流程测试", "testId": "T006", "fullTitle": "短信功能测试 > T006-短信发送功能完整流程测试", "status": "passed", "duration": 7559, "error": null, "screenshots": [{"name": "T006-dashboard-before-sms.png", "path": "screenshots/T006-dashboard-before-sms.png", "size": 41492, "description": "发送短信前仪表板", "createdTime": "2025-07-29T10:05:46.331Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-sms-page-loaded.png", "path": "screenshots/T006-sms-page-loaded.png", "size": 36239, "description": "短信页面加载完成", "createdTime": "2025-07-29T10:05:46.605Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-form-filled.png", "path": "screenshots/T006-form-filled.png", "size": 34017, "description": "表单填写完成", "createdTime": "2025-07-29T10:05:46.815Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-after-send.png", "path": "screenshots/T006-after-send.png", "size": 38447, "description": "发送完成后状态", "createdTime": "2025-07-29T10:05:49.965Z", "createdTimeStr": "2025/7/29 18:05:49"}, {"name": "T006-dashboard-after-sms.png", "path": "screenshots/T006-dashboard-after-sms.png", "size": 45249, "description": "发送短信后仪表板", "createdTime": "2025-07-29T10:05:52.185Z", "createdTimeStr": "2025/7/29 18:05:52"}], "testDetails": {"content": "验证短信发送功能的完整业务流程，包括发送前后的数据变化", "purpose": "确保短信发送功能正常工作，验证发送量统计的准确性", "input": "手机号: 13636367233, 短信内容: 测试短信内容", "expectedOutput": "短信发送成功，今日发送量增加1", "testSteps": ["1. 登录系统", "2. 查看发送前的今日短信发送量", "3. 进入短信单发页面", "4. 填写手机号码和短信内容", "5. 点击发送短信", "6. 返回仪表板查看发送量变化", "7. 验证发送量是否正确增加"]}}, {"title": "T007-短信页面元素验证", "testId": "T007", "fullTitle": "短信功能测试 > T007-短信页面元素验证", "status": "passed", "duration": 1741, "error": null, "screenshots": [{"name": "T007-page-elements.png", "path": "screenshots/T007-page-elements.png", "size": 36284, "description": "页面元素展示", "createdTime": "2025-07-29T10:05:53.764Z", "createdTimeStr": "2025/7/29 18:05:53"}, {"name": "T007-elements-verified.png", "path": "screenshots/T007-elements-verified.png", "size": 35690, "description": "页面元素验证完成", "createdTime": "2025-07-29T10:05:53.912Z", "createdTimeStr": "2025/7/29 18:05:53"}], "testDetails": {"content": "验证短信发送页面的UI元素完整性和可用性", "purpose": "确保短信发送页面的所有必要元素都正确显示和可用", "input": "访问短信单发页面", "expectedOutput": "页面显示手机号输入框、短信内容输入框和发送按钮", "testSteps": ["1. 登录系统", "2. 导航到短信单发页面", "3. 验证手机号输入框存在且可见", "4. 验证短信内容输入框存在且可见", "5. 验证发送短信按钮存在且可见"]}}, {"title": "T008-无效手机号码测试", "testId": "T008", "fullTitle": "短信功能测试 > T008-无效手机号码测试", "status": "passed", "duration": 3831, "error": null, "screenshots": [{"name": "T008-invalid-phone-filled.png", "path": "screenshots/T008-invalid-phone-filled.png", "size": 34472, "description": "无效手机号填写", "createdTime": "2025-07-29T10:05:55.561Z", "createdTimeStr": "2025/7/29 18:05:55"}, {"name": "T008-validation-result.png", "path": "screenshots/T008-validation-result.png", "size": 36681, "description": "验证结果", "createdTime": "2025-07-29T10:05:57.717Z", "createdTimeStr": "2025/7/29 18:05:57"}], "testDetails": {"content": "验证系统对无效手机号码的验证和错误处理机制", "purpose": "确保系统能够识别和拒绝无效的手机号码，保护系统安全", "input": "手机号: 123 (无效), 短信内容: 测试短信", "expectedOutput": "系统显示手机号格式错误的提示信息", "testSteps": ["1. 登录系统", "2. 进入短信单发页面", "3. 输入无效的手机号码\"123\"", "4. 输入短信内容", "5. 点击发送按钮", "6. 验证系统的错误提示"]}}, {"title": "T009-空短信内容测试", "testId": "T009", "fullTitle": "短信功能测试 > T009-空短信内容测试", "status": "passed", "duration": 3743, "error": null, "screenshots": [{"name": "T009-empty-content-state.png", "path": "screenshots/T009-empty-content-state.png", "size": 35628, "description": "短信内容为空状态", "createdTime": "2025-07-29T10:05:59.344Z", "createdTimeStr": "2025/7/29 18:05:59"}, {"name": "T009-validation-result.png", "path": "screenshots/T009-validation-result.png", "size": 37799, "description": "验证结果", "createdTime": "2025-07-29T10:06:01.480Z", "createdTimeStr": "2025/7/29 18:06:01"}], "testDetails": {"content": "验证系统对空短信内容的验证和错误处理机制", "purpose": "确保系统不允许发送空内容的短信，维护短信质量", "input": "手机号: 13636367233, 短信内容: (空)", "expectedOutput": "系统显示短信内容不能为空的提示信息", "testSteps": ["1. 登录系统", "2. 进入短信单发页面", "3. 输入有效的手机号码", "4. 保持短信内容为空", "5. 点击发送按钮", "6. 验证系统的错误提示"]}}], "screenshots": [{"name": "T001-login-initial.png", "path": "screenshots/T001-login-initial.png", "size": 202378, "description": "登录页面初始状态", "createdTime": "2025-07-29T10:05:36.560Z", "createdTimeStr": "2025/7/29 18:05:36"}, {"name": "T001-elements-verified.png", "path": "screenshots/T001-elements-verified.png", "size": 201965, "description": "页面元素验证完成", "createdTime": "2025-07-29T10:05:36.730Z", "createdTimeStr": "2025/7/29 18:05:36"}, {"name": "T002-username-filled.png", "path": "screenshots/T002-username-filled.png", "size": 202499, "description": "用户名填写完成", "createdTime": "2025-07-29T10:05:37.959Z", "createdTimeStr": "2025/7/29 18:05:37"}, {"name": "T002-form-completed.png", "path": "screenshots/T002-form-completed.png", "size": 201106, "description": "表单填写完成", "createdTime": "2025-07-29T10:05:38.128Z", "createdTimeStr": "2025/7/29 18:05:38"}, {"name": "T003-before-submit.png", "path": "screenshots/T003-before-submit.png", "size": 201112, "description": "提交前状态", "createdTime": "2025-07-29T10:05:39.391Z", "createdTimeStr": "2025/7/29 18:05:39"}, {"name": "T003-after-submit.png", "path": "screenshots/T003-after-submit.png", "size": 47458, "description": "提交后状态", "createdTime": "2025-07-29T10:05:41.540Z", "createdTimeStr": "2025/7/29 18:05:41"}, {"name": "T004-empty-username-state.png", "path": "screenshots/T004-empty-username-state.png", "size": 202522, "description": "用户名为空状态", "createdTime": "2025-07-29T10:05:42.818Z", "createdTimeStr": "2025/7/29 18:05:42"}, {"name": "T004-validation-result.png", "path": "screenshots/T004-validation-result.png", "size": 201112, "description": "验证结果", "createdTime": "2025-07-29T10:05:43.017Z", "createdTimeStr": "2025/7/29 18:05:43"}, {"name": "T005-empty-password-state.png", "path": "screenshots/T005-empty-password-state.png", "size": 202503, "description": "密码为空状态", "createdTime": "2025-07-29T10:05:44.291Z", "createdTimeStr": "2025/7/29 18:05:44"}, {"name": "T005-validation-result.png", "path": "screenshots/T005-validation-result.png", "size": 201640, "description": "验证结果", "createdTime": "2025-07-29T10:05:44.476Z", "createdTimeStr": "2025/7/29 18:05:44"}, {"name": "T006-dashboard-before-sms.png", "path": "screenshots/T006-dashboard-before-sms.png", "size": 41492, "description": "发送短信前仪表板", "createdTime": "2025-07-29T10:05:46.331Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-sms-page-loaded.png", "path": "screenshots/T006-sms-page-loaded.png", "size": 36239, "description": "短信页面加载完成", "createdTime": "2025-07-29T10:05:46.605Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-form-filled.png", "path": "screenshots/T006-form-filled.png", "size": 34017, "description": "表单填写完成", "createdTime": "2025-07-29T10:05:46.815Z", "createdTimeStr": "2025/7/29 18:05:46"}, {"name": "T006-after-send.png", "path": "screenshots/T006-after-send.png", "size": 38447, "description": "发送完成后状态", "createdTime": "2025-07-29T10:05:49.965Z", "createdTimeStr": "2025/7/29 18:05:49"}, {"name": "T006-dashboard-after-sms.png", "path": "screenshots/T006-dashboard-after-sms.png", "size": 45249, "description": "发送短信后仪表板", "createdTime": "2025-07-29T10:05:52.185Z", "createdTimeStr": "2025/7/29 18:05:52"}, {"name": "T007-page-elements.png", "path": "screenshots/T007-page-elements.png", "size": 36284, "description": "页面元素展示", "createdTime": "2025-07-29T10:05:53.764Z", "createdTimeStr": "2025/7/29 18:05:53"}, {"name": "T007-elements-verified.png", "path": "screenshots/T007-elements-verified.png", "size": 35690, "description": "页面元素验证完成", "createdTime": "2025-07-29T10:05:53.912Z", "createdTimeStr": "2025/7/29 18:05:53"}, {"name": "T008-invalid-phone-filled.png", "path": "screenshots/T008-invalid-phone-filled.png", "size": 34472, "description": "无效手机号填写", "createdTime": "2025-07-29T10:05:55.561Z", "createdTimeStr": "2025/7/29 18:05:55"}, {"name": "T008-validation-result.png", "path": "screenshots/T008-validation-result.png", "size": 36681, "description": "验证结果", "createdTime": "2025-07-29T10:05:57.717Z", "createdTimeStr": "2025/7/29 18:05:57"}, {"name": "T009-empty-content-state.png", "path": "screenshots/T009-empty-content-state.png", "size": 35628, "description": "短信内容为空状态", "createdTime": "2025-07-29T10:05:59.344Z", "createdTimeStr": "2025/7/29 18:05:59"}, {"name": "T009-validation-result.png", "path": "screenshots/T009-validation-result.png", "size": 37799, "description": "验证结果", "createdTime": "2025-07-29T10:06:01.480Z", "createdTimeStr": "2025/7/29 18:06:01"}]}