import request from '@/utils/request'

// 获取模板关联的参数列表
export function getParametersByTemplate(templateId, templateType) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}`,
    method: 'get'
  })
}

// 获取模板参数关联列表
export function getTemplateParameterRelations(templateId, templateType) {
  return request({
    url: `/api/template-parameters/relations/${templateId}/${templateType}`,
    method: 'get'
  })
}

// 为模板添加参数关联
export function addParameterToTemplate(templateId, templateType, parameterId, isRequired = false, defaultValue = null, sortOrder = 0) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/parameter/${parameterId}`,
    method: 'post',
    params: {
      isRequired,
      defaultValue,
      sortOrder
    }
  })
}

// 从模板中移除参数关联
export function removeParameterFromTemplate(templateId, templateType, parameterId) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/parameter/${parameterId}`,
    method: 'delete'
  })
}

// 批量设置模板参数关联
export function batchSetTemplateParameters(templateId, templateType, relations) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/batch`,
    method: 'post',
    data: relations
  })
}

// 更新模板参数关联
export function updateTemplateParameterRelation(templateId, templateType, parameterId, isRequired = null, defaultValue = null, sortOrder = null) {
  const params = {}
  if (isRequired !== null) params.isRequired = isRequired
  if (defaultValue !== null) params.defaultValue = defaultValue
  if (sortOrder !== null) params.sortOrder = sortOrder
  
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/parameter/${parameterId}`,
    method: 'put',
    params
  })
}

// 检查参数是否被模板使用
export function isParameterUsedByTemplate(templateId, templateType, parameterId) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/parameter/${parameterId}/exists`,
    method: 'get'
  })
}

// 统计模板的参数数量
export function countParametersByTemplate(templateId, templateType) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}/count`,
    method: 'get'
  })
}

// 统计参数被使用的模板数量
export function countTemplatesByParameter(parameterId) {
  return request({
    url: `/api/template-parameters/parameter/${parameterId}/template-count`,
    method: 'get'
  })
}

// 删除模板的所有参数关联
export function deleteByTemplateIdAndType(templateId, templateType) {
  return request({
    url: `/api/template-parameters/template/${templateId}/${templateType}`,
    method: 'delete'
  })
}