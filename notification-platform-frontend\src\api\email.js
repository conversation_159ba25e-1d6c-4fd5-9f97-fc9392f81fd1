import request from '@/utils/request'

/**
 * 发送单条邮件
 * @param {Object} data 邮件数据
 */
export function sendSingleEmail(data) {
  return request({
    url: '/api/single-email/send-with-content',
    method: 'post',
    data
  })
}

/**
 * 发送高级邮件（支持抄送密送）
 * @param {Object} data 邮件数据
 */
export function sendAdvancedEmail(data) {
  return request({
    url: '/api/email/send-advanced',
    method: 'post',
    data
  })
}

/**
 * 发送带附件的邮件
 * @param {FormData} data 表单数据
 */
export function sendEmailWithAttachments(data) {
  return request({
    url: '/api/email/send-with-attachments',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 批量发送邮件
 * @param {Object} data 批量邮件数据
 */
export function sendBatchEmail(data) {
  return request({
    url: '/api/email/batch-send',
    method: 'post',
    data
  })
}

/**
 * 使用指定渠道发送邮件
 * @param {Object} data 邮件数据
 */
export function sendEmailWithChannel(data) {
  return request({
    url: '/api/single-email/send-with-channel',
    method: 'post',
    data
  })
}

/**
 * 查询邮件发送状态
 * @param {String} messageId 消息ID
 */
export function queryEmailStatus(messageId) {
  return request({
    url: `/api/email/status/${messageId}`,
    method: 'get'
  })
}

/**
 * 验证邮箱地址
 * @param {String} email 邮箱地址
 */
export function validateEmail(email) {
  return request({
    url: '/api/email/validate-email',
    method: 'post',
    data: { email }
  })
}

/**
 * 获取发送渠道列表（按类型）
 * @param {Object} params 查询参数，包含channelType
 */
export function getSendChannelList(params) {
  return request({
    url: `/api/send-channels/type/${params.channelType}`,
    method: 'get'
  })
}

/**
 * 发送营销邮件
 * @param {Object} data 营销邮件数据
 */
export function sendMarketingEmail(data) {
  return request({
    url: '/api/email/marketing-send',
    method: 'post',
    data
  })
}

/**
 * 发送休眠账户通知
 * @param {Object} data 休眠账户通知数据
 */
export function sendDormantNotification(data) {
  return request({
    url: '/api/email/dormant-notification',
    method: 'post',
    data
  })
}

/**
 * 获取邮件模板列表
 * @param {Object} params 查询参数
 */
export function getEmailTemplateList(params) {
  return request({
    url: '/api/email-template/list',
    method: 'get',
    params
  })
}