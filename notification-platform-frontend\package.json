{"name": "notification-platform-frontend", "version": "1.0.0", "description": "通知平台前端应用", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "node test/reports/run-all-tests.js", "test:playwright": "node run-playwright-tests.js", "test:all:headed": "playwright test --headed", "test:report": "node test/reports/generate-report.js", "test:comprehensive": "node test/reports/run-all-tests.js", "test:auth": "node test/auth/login-test.js", "test:auth:user": "node test/auth/user-management-test.js", "test:auth:role": "node test/auth/role-management-test.js", "test:auth:channel": "node test/auth/channel-config-test.js", "test:api": "node test/api/api-interface-test.js", "test:message": "node test/message/sms-single-test.js", "test:message:sms-batch": "node test/message/sms-batch-test.js", "test:message:email": "node test/message/email-single-test.js", "test:message:email-batch": "node test/message/email-batch-test.js", "test:message:email-marketing": "node test/message/email-marketing-test.js", "test:message:dormant": "node test/message/dormant-account-test.js", "test:template": "node test/template/template-manage-test.js", "test:template:parameter": "node test/template/template-parameter-test.js", "test:template:type": "node test/template/template-type-test.js", "test:channel": "node test/channel/access-channel-test.js", "test:channel:send": "node test/channel/send-channel-test.js", "test:channel:esb": "node test/channel/esb-interface-test.js", "test:channel:queue": "node test/channel/message-queue-test.js", "test:template:version": "node test/template/template-version-test.js", "test:template:i18n": "node test/template/i18n-support-test.js", "test:statistics:search": "node test/statistics/advanced-search-test.js", "test:statistics:realtime": "node test/statistics/real-time-monitor-test.js", "test:system:backup": "node test/system/data-backup-test.js", "test:security": "node test/security/blacklist-test.js", "test:security:audit": "node test/security/security-audit-test.js", "test:security:whitelist": "node test/security/whitelist-test.js", "test:security:keyword": "node test/security/keyword-filter-test.js", "test:statistics": "node test/statistics/dashboard-test.js", "test:statistics:detail": "node test/statistics/send-detail-test.js", "test:statistics:template": "node test/statistics/template-statistics-test.js", "test:system": "node test/system/password-change-test.js", "test:system:log": "node test/system/system-log-test.js", "test:system:permission": "node test/system/permission-test.js", "test:system:config": "node test/system/system-config-test.js", "test:final": "node test/run-final-tests.js", "test:login": "playwright test --config=playwright-login.config.js", "test:login:ui": "playwright test --config=playwright-login.config.js --ui", "test:login:headed": "playwright test --config=playwright-login.config.js --headed", "test:login:debug": "playwright test --config=playwright-login.config.js --debug"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "echarts": "^5.4.3", "element-plus": "^2.3.8", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "playwright": "^1.54.1", "vue": "^3.3.4", "vue-echarts": "^6.6.1", "vue-router": "^4.2.4", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.40.0", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "sass": "^1.64.1", "vite": "^4.4.5"}}