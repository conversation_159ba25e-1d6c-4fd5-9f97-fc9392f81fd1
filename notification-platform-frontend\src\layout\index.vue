<template>
  <div class="app-wrapper">
    <!-- 侧边栏 -->
    <div class="sidebar-container">
      <div class="logo">
        <i class="el-icon-message" style="font-size: 24px; color: #409EFF; margin-right: 12px;"></i>
        <span>通知平台</span>
      </div>
      <el-menu
        :default-active="activeMenu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        :collapse="isCollapse"
        :unique-opened="true"
        router
      >
        <sidebar-item v-for="route in routes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </div>
    
    <!-- 主区域 -->
    <div class="main-container">
      <!-- 头部 -->
      <div class="navbar">
        <div class="hamburger" @click="toggleSideBar">
          <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
        </div>
        <breadcrumb />
        <div class="right-menu">
          <el-dropdown trigger="click">
            <div class="avatar-wrapper">
              <i class="el-icon-user-solid" style="font-size: 24px; color: #409EFF; margin-right: 8px;"></i>
              <span>{{ username }}</span>
              <i class="el-icon-caret-bottom"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="handlePassword">修改密码</el-dropdown-item>
                <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      
      <!-- 内容区 -->
      <div class="app-main">
        <router-view v-slot="{ Component }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { removeToken, getUserInfo, clearAuth } from '@/utils/auth';
import SidebarItem from './components/SidebarItem.vue';
import Breadcrumb from './components/Breadcrumb.vue';

const router = useRouter();
const routes = router.options.routes.filter(route => !route.meta?.isPublic);
const isCollapse = ref(false);
const username = ref('管理员');

// 获取用户信息
onMounted(() => {
  const userInfo = getUserInfo();
  if (userInfo && userInfo.username) {
    username.value = userInfo.username;
  }
});

const activeMenu = computed(() => {
  const route = router.currentRoute.value;
  return route.path;
});

const toggleSideBar = () => {
  isCollapse.value = !isCollapse.value;
};

const handlePassword = () => {
  router.push('/settings/password');
};

const logout = () => {
  clearAuth();
  router.push('/login');
};
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  display: flex;
}

.sidebar-container {
  width: 210px;
  height: 100%;
  background-color: #304156;
  transition: width 0.28s;
  overflow-y: auto;
  
  .logo {
    height: 50px;
    display: flex;
    align-items: center;
    padding-left: 15px;
    background-color: #2b3649;
    

    
    span {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.navbar {
  height: 50px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  
  .hamburger {
    cursor: pointer;
    font-size: 20px;
    margin-right: 15px;
  }
  
  .right-menu {
    margin-left: auto;
    
    .avatar-wrapper {
      display: flex;
      align-items: center;
      cursor: pointer;
      

    }
  }
}

.app-main {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f0f2f5;
}

.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from,
.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>