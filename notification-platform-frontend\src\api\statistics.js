import request from '@/utils/request'

// 获取消息统计信息
export function getMessageStatistics(params) {
  return request({
    url: '/api/message/statistics/overview',
    method: 'get',
    params
  })
}

// 获取首页统计数据
export function getDashboardStatistics() {
  const today = new Date()
  const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString().slice(0, 19).replace('T', ' ')
  const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString().slice(0, 19).replace('T', ' ')
  
  return request({
    url: '/api/message/statistics/overview',
    method: 'get',
    params: {
      startTime,
      endTime
    }
  })
}

// 分页查询消息记录
export function getMessageRecords(params) {
  return request({
    url: '/api/message/statistics/records',
    method: 'get',
    params
  })
}

// 根据消息ID查询记录详情
export function getMessageRecordById(messageId) {
  return request({
    url: `/api/message/statistics/records/${messageId}`,
    method: 'get'
  })
}

// 重发消息
export function resendMessage(messageId) {
  return request({
    url: `/api/message/statistics/resend/${messageId}`,
    method: 'post'
  })
}

// 批量重发消息
export function batchResendMessages(messageIds) {
  return request({
    url: '/api/message/statistics/batch-resend',
    method: 'post',
    data: messageIds
  })
}

// 获取发送失败的消息记录
export function getFailedMessages(params) {
  return request({
    url: '/api/message/statistics/failed-messages',
    method: 'get',
    params
  })
}