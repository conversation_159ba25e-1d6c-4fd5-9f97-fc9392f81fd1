import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { getToken, getUserInfo } from '@/utils/auth';
import { hasPermission } from '@/utils/permission';

// 路由配置 - 与后端权限完全对齐
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: { title: '登录', isPublic: true }
  },
  {
    path: '/',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: { title: '首页', icon: 'dashboard' }
      }
    ]
  },
  // 系统管理模块 - 对应 system:manage
  {
    path: '/system',
    component: () => import('@/layout/index.vue'),
    redirect: '/system/user',
    meta: { 
      title: '系统管理', 
      icon: 'system',
      permission: 'system:manage'
    },
    children: [
      {
        path: 'user',
        name: 'SystemUser',
        component: () => import('@/views/system/user/index.vue'),
        meta: { 
          title: '用户管理',
          icon: 'user',
          permission: 'system:user'
        }
      },
      {
        path: 'role',
        name: 'SystemRole',
        component: () => import('@/views/system/role/index.vue'),
        meta: { 
          title: '角色管理',
          icon: 'role',
          permission: 'system:role'
        }
      },

      {
        path: 'permission',
        name: 'SystemPermission',
        component: () => import('@/views/system/permission/index.vue'),
        meta: { 
          title: '权限管理',
          icon: 'permission',
          permission: 'system:permission'
        }
      },

    ]
  },
  // 消息发送模块 - 对应 message:manage
  {
    path: '/message',
    component: () => import('@/layout/index.vue'),
    redirect: '/message/sms/single',
    meta: { 
      title: '消息发送', 
      icon: 'message',
      permission: 'message:manage'
    },
    children: [
      {
        path: 'sms',
        redirect: '/message/sms/single',
        meta: { 
          title: '短信发送',
          icon: 'sms',
          permission: 'message:sms'
        },
        children: [
          {
            path: 'single',
            name: 'MessageSmsSingle',
            component: () => import('@/views/message/sms/single.vue'),
            meta: { 
              title: '短信单发',
              icon: 'single',
              permission: 'message:sms:single'
            }
          },
          {
            path: 'batch',
            name: 'MessageSmsBatch',
            component: () => import('@/views/message/sms/batch.vue'),
            meta: { 
              title: '短信群发',
              icon: 'batch',
              permission: 'message:sms:batch'
            }
          }
        ]
      },
      {
        path: 'email',
        redirect: '/message/email/single',
        meta: { 
          title: '邮件发送',
          icon: 'email',
          permission: 'message:email'
        },
        children: [
          {
            path: 'single',
            name: 'MessageEmailSingle',
            component: () => import('@/views/message/email/single.vue'),
            meta: { 
              title: '邮件单发',
              icon: 'single',
              permission: 'message:email:single'
            }
          },
          {
            path: 'batch',
            name: 'MessageEmailBatch',
            component: () => import('@/views/message/email/batch.vue'),
            meta: { 
              title: '邮件群发',
              icon: 'batch',
              permission: 'message:email:batch'
            }
          },
          {
            path: 'marketing',
            name: 'MessageEmailMarketing',
            component: () => import('@/views/message/email/marketing.vue'),
            meta: { 
              title: '营销邮件',
              icon: 'marketing',
              permission: 'message:email:marketing'
            }
          },
          {
            path: 'dormant',
            name: 'MessageEmailDormant',
            component: () => import('@/views/message/email/dormant.vue'),
            meta: { 
              title: '休眠邮件',
              icon: 'dormant',
              permission: 'message:email:dormant'
            }
          }
        ]
      }
    ]
  },
  // 渠道管理模块
  {
    path: '/channel',
    component: () => import('@/layout/index.vue'),
    redirect: '/channel/access',
    meta: { 
      title: '渠道管理', 
      icon: 'channel',
      permission: 'channel:manage'
    },
    children: [
      {
        path: 'access',
        name: 'AccessChannel',
        component: () => import('@/views/channel/access.vue'),
        meta: { 
          title: '接入渠道',
          permission: 'channel:access:view'
        }
      },
      {
        path: 'send',
        name: 'SendChannel',
        component: () => import('@/views/channel/send/index.vue'),
        meta: { 
          title: '发送渠道',
          permission: 'channel:send:view'
        }
      },
      {
        path: 'esb',
        name: 'EsbInterface',
        component: () => import('@/views/esb/interface.vue'),
        meta: { 
          title: 'ESB接口管理',
          permission: 'channel:esb:view'
        }
      }
    ]
  },
  // 模板管理模块
  {
    path: '/template',
    component: () => import('@/layout/index.vue'),
    redirect: '/template/manage',
    meta: { title: '模板管理', icon: 'template' },
    children: [
      {
        path: 'manage',
        name: 'TemplateManage',
        component: () => import('@/views/template/manage.vue'),
        meta: { title: '模板管理' }
      },
      {
        path: 'parameter',
        name: 'ParameterManage',
        component: () => import('@/views/template/parameter.vue'),
        meta: { title: '参数管理' }
      },
      {
        path: 'type',
        name: 'TemplateType',
        component: () => import('@/views/template/type.vue'),
        meta: { title: '模板类型查询' }
      }
    ]
  },
  // 统计监控模块
  {
    path: '/statistics',
    component: () => import('@/layout/index.vue'),
    redirect: '/statistics/template',
    meta: { title: '统计监控', icon: 'statistics' },
    children: [
      {
        path: 'template',
        name: 'TemplateStatistics',
        component: () => import('@/views/statistics/template.vue'),
        meta: { title: '按模板统计' }
      },
      {
        path: 'message/success',
        name: 'MessageSuccess',
        component: () => import('@/views/statistics/message.vue'),
        meta: { title: '发送成功', status: 'SUCCESS' }
      },
      {
        path: 'message/sending',
        name: 'MessageSending',
        component: () => import('@/views/statistics/message.vue'),
        meta: { title: '正在发送', status: 'SENDING' }
      },
      {
        path: 'message/failed',
        name: 'MessageFailed',
        component: () => import('@/views/statistics/message.vue'),
        meta: { title: '发送失败', status: 'FAILED' }
      }
    ]
  },

  // 安全管理模块
  {
    path: '/security',
    component: () => import('@/layout/index.vue'),
    redirect: '/security/blacklist',
    meta: { title: '安全管理', icon: 'security' },
    children: [
      {
        path: 'blacklist',
        name: 'Blacklist',
        component: () => import('@/views/security/blacklist.vue'),
        meta: { title: '黑名单管理' }
      },
      {
        path: 'whitelist',
        name: 'Whitelist',
        component: () => import('@/views/security/whitelist.vue'),
        meta: { title: '白名单管理' }
      },
      {
        path: 'keyword',
        name: 'Keyword',
        component: () => import('@/views/security/keyword.vue'),
        meta: { title: '敏感词管理' }
      }
    ]
  },

  // 系统设置模块
  {
    path: '/settings',
    component: () => import('@/layout/index.vue'),
    redirect: '/settings/password',
    meta: { title: '系统设置', icon: 'setting' },
    children: [
      {
        path: 'password',
        name: 'SettingsPassword',
        component: () => import('@/views/system/password.vue'),
        meta: { title: '密码修改' }
      },
      {
        path: 'log',
        name: 'SettingsLog',
        component: () => import('@/views/system/log.vue'),
        meta: { title: '系统日志' }
      }
    ]
  },
  // 403页面
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: { title: '无权限访问', isPublic: true }
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { isPublic: true }
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  document.title = to.meta.title ? `${to.meta.title} - 通知平台` : '通知平台';
  
  const hasToken = getToken();
  
  if (hasToken) {
    if (to.path === '/login') {
      next({ path: '/' });
      NProgress.done();
    } else {
      // 检查权限
      if (to.meta.permission) {
        try {
          const userInfo = getUserInfo();
          if (userInfo && hasPermission(to.meta.permission)) {
            next();
          } else {
            next({ path: '/403' });
            NProgress.done();
          }
        } catch (error) {
          next({ path: '/login' });
          NProgress.done();
        }
      } else {
        next();
      }
    }
  } else {
    if (to.meta.isPublic) {
      next();
    } else {
      next(`/login?redirect=${to.path}`);
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});

export default router;