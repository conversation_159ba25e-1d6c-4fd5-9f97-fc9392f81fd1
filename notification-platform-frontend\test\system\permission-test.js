/**
 * T241-T250: 权限测试功能测试
 * 基于需求文档中的权限管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class PermissionTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T241: 权限管理页面加载测试
   */
  async testT241_PermissionPageLoad() {
    const testId = 'T241';
    console.log(`\n🧪 执行测试 ${testId}: 权限管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到权限管理页面
      await this.testHelper.navigateTo('/system/permission');
      await this.testHelper.waitForPageLoad(selectors.permission.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isRoleListVisible = await this.testHelper.verifyElementVisibility(selectors.permission.roleList);
      const isPermissionTreeVisible = await this.testHelper.verifyElementVisibility(selectors.permission.permissionTree);
      const isAddRoleButtonVisible = await this.testHelper.verifyElementVisibility(selectors.permission.addRoleButton);
      const isSaveButtonVisible = await this.testHelper.verifyElementVisibility(selectors.permission.saveButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '权限管理页面加载测试',
        testContent: '验证权限管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的权限管理界面',
        testInput: '访问权限管理页面URL: /system/permission',
        expectedOutput: '页面正常加载，显示角色列表、权限树、新增角色按钮和保存按钮',
        actualOutput: `角色列表: ${isRoleListVisible ? '✅显示' : '❌隐藏'}, 权限树: ${isPermissionTreeVisible ? '✅显示' : '❌隐藏'}, 新增角色: ${isAddRoleButtonVisible ? '✅显示' : '❌隐藏'}, 保存按钮: ${isSaveButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isRoleListVisible || isPermissionTreeVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '权限管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T242: 角色权限分配测试
   */
  async testT242_RolePermissionAssign() {
    const testId = 'T242';
    console.log(`\n🧪 执行测试 ${testId}: 角色权限分配测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到权限管理页面
      await this.testHelper.navigateTo('/system/permission');
      await this.testHelper.waitForPageLoad(selectors.permission.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 选择一个角色
      try {
        await this.testHelper.page.click(`${selectors.permission.roleList} .role-item:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('角色选择完成');
        
        // 勾选一些权限
        const permissionCheckboxes = [
          '.permission-checkbox[data-permission="message:send"]',
          '.permission-checkbox[data-permission="template:manage"]',
          '.permission-checkbox[data-permission="statistics:view"]'
        ];
        
        for (const checkbox of permissionCheckboxes) {
          try {
            await this.testHelper.page.check(checkbox);
            await this.testHelper.wait(testData.timeouts.short);
          } catch (checkboxError) {
            // 如果权限复选框不存在，跳过
          }
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存权限设置
        await this.testHelper.page.click(selectors.permission.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '角色权限分配测试',
          testContent: '为角色分配权限',
          testPurpose: '验证角色权限分配功能能够正常工作',
          testInput: '选择角色并勾选权限复选框',
          expectedOutput: '权限分配成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (assignError) {
        // 如果没有权限分配功能，标记为跳过
        const result = {
          testId: testId,
          testName: '角色权限分配测试',
          testContent: '为角色分配权限',
          testPurpose: '验证角色权限分配功能能够正常工作',
          testInput: '查找权限分配功能',
          expectedOutput: '找到权限分配并成功设置',
          actualOutput: '未找到权限分配功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到权限分配功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '角色权限分配测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T243: 用户角色分配测试
   */
  async testT243_UserRoleAssign() {
    const testId = 'T243';
    console.log(`\n🧪 执行测试 ${testId}: 用户角色分配测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到用户管理页面（包含角色分配）
      await this.testHelper.navigateTo('/system/user');
      await this.testHelper.waitForPageLoad(selectors.user.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找第一个编辑按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.user.table} ${selectors.security.editButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeEditPageScreenshot();
        
        // 查找角色选择器
        try {
          await this.testHelper.page.click(selectors.user.roleSelect);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('角色选择器打开');
          
          // 选择一个角色
          await this.testHelper.page.click('.role-option:first-child');
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeFormFilledScreenshot();
          
          // 保存用户角色
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          // 获取成功消息
          const successMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '用户角色分配测试',
            testContent: '为用户分配角色',
            testPurpose: '验证用户角色分配功能能够正常工作',
            testInput: '选择用户并分配角色',
            expectedOutput: '用户角色分配成功，显示成功提示信息',
            actualOutput: `成功消息: ${successMessage || '无'}`,
            result: successMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } catch (roleSelectError) {
          throw new Error('未找到角色选择器');
        }
        
      } catch (editError) {
        // 如果没有用户角色分配功能，标记为跳过
        const result = {
          testId: testId,
          testName: '用户角色分配测试',
          testContent: '为用户分配角色',
          testPurpose: '验证用户角色分配功能能够正常工作',
          testInput: '查找用户角色分配功能',
          expectedOutput: '找到角色分配并成功设置',
          actualOutput: '未找到用户角色分配功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到用户角色分配功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '用户角色分配测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T244: 权限验证测试
   */
  async testT244_PermissionValidation() {
    const testId = 'T244';
    console.log(`\n🧪 执行测试 ${testId}: 权限验证测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 测试不同页面的访问权限
      const testPages = [
        { url: '/system/user', name: '用户管理' },
        { url: '/system/permission', name: '权限管理' },
        { url: '/security/blacklist', name: '黑名单管理' },
        { url: '/statistics/send-detail', name: '发送统计' }
      ];
      
      let accessResults = [];
      
      for (const testPage of testPages) {
        try {
          await this.testHelper.navigateTo(testPage.url);
          await this.testHelper.wait(testData.timeouts.medium);
          
          // 检查是否显示权限错误或成功加载
          const hasPermissionError = await this.testHelper.verifyElementVisibility('.permission-error');
          const hasContent = await this.testHelper.verifyElementVisibility('.main-content');
          
          accessResults.push({
            page: testPage.name,
            url: testPage.url,
            hasAccess: !hasPermissionError && hasContent,
            error: hasPermissionError
          });
          
          await this.screenshotHelper.takeCustomScreenshot(`${testPage.name}页面访问测试`);
          
        } catch (pageError) {
          accessResults.push({
            page: testPage.name,
            url: testPage.url,
            hasAccess: false,
            error: true
          });
        }
      }
      
      const accessiblePages = accessResults.filter(r => r.hasAccess).length;
      
      const result = {
        testId: testId,
        testName: '权限验证测试',
        testContent: '测试不同页面的访问权限验证',
        testPurpose: '验证权限验证机制能够正常工作',
        testInput: `测试${testPages.length}个页面的访问权限`,
        expectedOutput: '根据用户权限正确控制页面访问',
        actualOutput: `可访问页面数: ${accessiblePages}/${testPages.length}, 详情: ${accessResults.map(r => `${r.page}:${r.hasAccess ? '✅' : '❌'}`).join(', ')}`,
        result: accessResults.length > 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '权限验证测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T245: 菜单权限控制测试
   */
  async testT245_MenuPermissionControl() {
    const testId = 'T245';
    console.log(`\n🧪 执行测试 ${testId}: 菜单权限控制测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 检查主菜单的显示情况
      const menuItems = [
        { selector: '.menu-item[data-menu="message"]', name: '消息发送' },
        { selector: '.menu-item[data-menu="template"]', name: '模板管理' },
        { selector: '.menu-item[data-menu="channel"]', name: '渠道管理' },
        { selector: '.menu-item[data-menu="statistics"]', name: '统计分析' },
        { selector: '.menu-item[data-menu="security"]', name: '安全管理' },
        { selector: '.menu-item[data-menu="system"]', name: '系统设置' }
      ];
      
      let menuResults = [];
      
      for (const menuItem of menuItems) {
        try {
          const isVisible = await this.testHelper.verifyElementVisibility(menuItem.selector);
          menuResults.push({
            menu: menuItem.name,
            visible: isVisible
          });
        } catch (menuError) {
          menuResults.push({
            menu: menuItem.name,
            visible: false
          });
        }
      }
      
      await this.screenshotHelper.takeCustomScreenshot('菜单权限控制检查');
      
      const visibleMenus = menuResults.filter(r => r.visible).length;
      
      const result = {
        testId: testId,
        testName: '菜单权限控制测试',
        testContent: '检查菜单项的权限控制显示',
        testPurpose: '验证菜单权限控制机制能够正常工作',
        testInput: `检查${menuItems.length}个主菜单项的显示权限`,
        expectedOutput: '根据用户权限正确显示/隐藏菜单项',
        actualOutput: `可见菜单数: ${visibleMenus}/${menuItems.length}, 详情: ${menuResults.map(r => `${r.menu}:${r.visible ? '✅' : '❌'}`).join(', ')}`,
        result: menuResults.length > 0 ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '菜单权限控制测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有权限测试
   */
  async runAllTests() {
    console.log('🚀 开始执行权限测试功能测试套件 (T241-T245)');
    
    const startTime = Date.now();
    
    await this.testT241_PermissionPageLoad();
    await this.testT242_RolePermissionAssign();
    await this.testT243_UserRoleAssign();
    await this.testT244_PermissionValidation();
    await this.testT245_MenuPermissionControl();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 权限测试功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const permissionTest = new PermissionTest();
  permissionTest.runAllTests().catch(console.error);
}

module.exports = PermissionTest;
