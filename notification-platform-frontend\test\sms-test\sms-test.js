const { chromium } = require('playwright');

(async () => {
  const browser = await chromium.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    // 1. 登录系统
    await page.goto('http://localhost:3000/login');
    await page.waitForSelector('.login-container');
    
    await page.fill('input[placeholder="用户名"]', 'admin');
    await page.fill('input[placeholder="密码"]', 'Admin123!');
    await page.click('button:has-text("登录")');
    
    // 等待登录成功
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    
    // 记录发送前的今日发送量
    await page.screenshot({ path: 'dashboard-before-sms.png', fullPage: true });
    
    const beforeCount = await page.textContent('.dashboard-card:has-text("今日短信发送量") .card-value');
    console.log('发送前今日短信发送量:', beforeCount);
    
    // 2. 进入短信单发页面
    await page.goto('http://localhost:3000/message/sms/single');
    await page.waitForSelector('.single-sms-container');
    
    // 填写短信信息
    await page.fill('input[placeholder="请输入手机号码"]', '13636367233');
    await page.fill('textarea[placeholder="请输入短信内容"]', '测试短信');
    
    // 发送短信
    await page.click('button:has-text("发送短信")');
    
    // 等待发送完成
    await page.waitForTimeout(3000);
    
    // 3. 查看dashboard今日发送量是否增加
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForSelector('.dashboard-container');
    
    // 等待数据刷新
    await page.waitForTimeout(2000);
    
    // 记录发送后的今日发送量
    await page.screenshot({ path: 'dashboard-after-sms.png', fullPage: true });
    
    const afterCount = await page.textContent('.dashboard-card:has-text("今日短信发送量") .card-value');
    console.log('发送后今日短信发送量:', afterCount);
    
    // 比较发送量
    const before = parseInt(beforeCount);
    const after = parseInt(afterCount);
    
    if (after > before) {
      console.log('✅ 测试成功：今日发送量已增加', `${before} -> ${after}`);
    } else {
      console.log('❌ 测试失败：今日发送量未增加', `${before} -> ${after}`);
    }
    
  } catch (error) {
    console.error('测试失败:', error);
    await page.screenshot({ path: 'error-screenshot.png' });
  } finally {
    await browser.close();
  }
})();