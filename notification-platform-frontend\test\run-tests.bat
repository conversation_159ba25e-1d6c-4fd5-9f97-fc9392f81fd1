@echo off
echo ========================================
echo    通知平台前端自动化测试套件
echo ========================================
echo.

echo 正在检查依赖...
if not exist node_modules (
    echo 安装依赖中...
    npm install
)

echo.
echo 正在启动测试...
echo.

REM 运行所有测试并生成报告
node test/reports/run-all-tests.js

echo.
echo ========================================
echo 测试完成！
echo ========================================
echo.
echo 查看测试报告：
echo   - JSON报告: test-results\reports\test-report.json
echo   - HTML报告: test-results\reports\test-report.html
echo   - 测试截图: test-results\screenshots\
echo.

REM 询问是否打开报告
set /p choice="是否打开HTML测试报告？(y/n): "
if /i "%choice%"=="y" (
    start test-results\reports\test-report.html
)

pause
