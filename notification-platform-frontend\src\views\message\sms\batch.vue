<template>
  <div class="batch-sms-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>批量短信发送</span>
        </div>
      </template>
      
      <el-form
        ref="smsFormRef"
        :model="smsForm"
        :rules="smsRules"
        label-width="120px"
        class="sms-form"
      >
        <el-form-item label="发送方式" prop="sendType">
          <el-radio-group v-model="smsForm.sendType">
            <el-radio :label="1">手动输入</el-radio>
            <el-radio :label="2">Excel导入</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 手动输入方式 -->
        <template v-if="smsForm.sendType === 1">
          <el-form-item label="手机号码" prop="phoneNumbers">
            <el-input
              v-model="smsForm.phoneNumbers"
              type="textarea"
              :rows="6"
              placeholder="请输入手机号码，每行一个或用逗号分隔"
              show-word-limit
            />
            <div class="input-tip">
              支持每行一个号码或用逗号分隔，最多支持500个号码
            </div>
          </el-form-item>
        </template>

        <!-- Excel导入方式 -->
        <template v-if="smsForm.sendType === 2">
          <el-form-item label="Excel文件" prop="excelFile">
            <el-upload
              ref="uploadRef"
              :file-list="fileList"
              :on-change="handleFileChange"
              :on-remove="handleFileRemove"
              :auto-upload="false"
              :limit="1"
              accept=".xlsx,.xls"
              :on-exceed="handleExceed"
            >
              <el-button type="primary">选择Excel文件</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  只能上传xlsx/xls文件，且不超过5MB
                  <el-link type="primary" @click="downloadTemplate">下载模板</el-link>
                </div>
              </template>
            </el-upload>
          </el-form-item>

          <!-- 显示解析结果 -->
          <el-form-item v-if="parsedPhones.length > 0" label="解析结果">
            <div class="parsed-result">
              <div class="result-summary">
                共解析到 <strong>{{ parsedPhones.length }}</strong> 个手机号码
                <el-button type="text" @click="showParsedPhones = !showParsedPhones">
                  {{ showParsedPhones ? '隐藏' : '查看' }}详情
                </el-button>
              </div>
              <div v-if="showParsedPhones" class="phone-list">
                <el-tag
                  v-for="(phone, index) in parsedPhones.slice(0, 50)"
                  :key="index"
                  class="phone-tag"
                >
                  {{ phone }}
                </el-tag>
                <div v-if="parsedPhones.length > 50" class="more-tip">
                  还有 {{ parsedPhones.length - 50 }} 个号码...
                </div>
              </div>
            </div>
          </el-form-item>
        </template>

        <el-form-item label="短信内容" prop="content">
          <el-input
            v-model="smsForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入短信内容"
            show-word-limit
            maxlength="500"
          />
        </el-form-item>

        <el-form-item label="发送渠道" prop="channelCode">
          <el-select
            v-model="smsForm.channelCode"
            placeholder="请选择发送渠道（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="channel in channelList"
              :key="channel.channelCode"
              :label="channel.channelName"
              :value="channel.channelCode"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="sending"
            @click="sendBatchSms"
          >
            {{ sending ? '发送中...' : '批量发送' }}
          </el-button>
          <el-button @click="resetForm">重置</el-button>
          <el-button @click="previewSms">预览</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="previewVisible"
      title="批量短信预览"
      width="60%"
    >
      <div class="sms-preview">
        <div class="preview-item">
          <strong>发送数量：</strong>{{ getPhoneList().length }} 个
        </div>
        <div class="preview-item">
          <strong>短信内容：</strong>
          <div class="content-preview">{{ smsForm.content }}</div>
        </div>
        <div class="preview-item">
          <strong>字数统计：</strong>{{ smsForm.content.length }}/500
        </div>
        <div class="preview-item">
          <strong>手机号码：</strong>
          <div class="phone-preview">
            <el-tag
              v-for="(phone, index) in getPhoneList().slice(0, 20)"
              :key="index"
              class="phone-tag"
            >
              {{ phone }}
            </el-tag>
            <div v-if="getPhoneList().length > 20" class="more-tip">
              还有 {{ getPhoneList().length - 20 }} 个号码...
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 发送结果对话框 -->
    <el-dialog
      v-model="resultVisible"
      title="发送结果"
      width="50%"
    >
      <div class="send-result">
        <div class="result-item">
          <strong>批次ID：</strong>{{ sendResult.batchId }}
        </div>
        <div class="result-item">
          <strong>总数量：</strong>{{ sendResult.totalCount }}
        </div>
        <div class="result-item">
          <strong>成功数量：</strong>
          <span class="success-count">{{ sendResult.successCount }}</span>
        </div>
        <div class="result-item">
          <strong>失败数量：</strong>
          <span class="failure-count">{{ sendResult.failureCount }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as XLSX from 'xlsx'
import { sendBatchSms as sendBatchSmsApi } from '@/api/sms'
import { getSendChannelsByType } from '@/api/channel'

// 表单数据
const smsForm = reactive({
  sendType: 1, // 1-手动输入，2-Excel导入
  phoneNumbers: '',
  content: '',
  channelCode: ''
})

// 表单验证规则
const smsRules = {
  sendType: [
    { required: true, message: '请选择发送方式', trigger: 'change' }
  ],
  phoneNumbers: [
    { 
      validator: (rule, value, callback) => {
        if (smsForm.sendType === 1) {
          if (!value || value.trim() === '') {
            callback(new Error('请输入手机号码'))
          } else {
            const phones = getPhoneList()
            if (phones.length === 0) {
              callback(new Error('请输入有效的手机号码'))
            } else if (phones.length > 500) {
              callback(new Error('手机号码数量不能超过500个'))
            } else {
              callback()
            }
          }
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ],
  excelFile: [
    { 
      validator: (rule, value, callback) => {
        if (smsForm.sendType === 2) {
          if (fileList.value.length === 0) {
            callback(new Error('请选择Excel文件'))
          } else if (parsedPhones.value.length === 0) {
            callback(new Error('Excel文件中没有解析到有效的手机号码'))
          } else if (parsedPhones.value.length > 500) {
            callback(new Error('手机号码数量不能超过500个'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      }, 
      trigger: 'change' 
    }
  ],
  content: [
    { required: true, message: '请输入短信内容', trigger: 'blur' },
    { max: 500, message: '短信内容不能超过500个字符', trigger: 'blur' }
  ]
}

// 响应式数据
const smsFormRef = ref()
const uploadRef = ref()
const sending = ref(false)
const previewVisible = ref(false)
const resultVisible = ref(false)
const showParsedPhones = ref(false)
const fileList = ref([])
const parsedPhones = ref([])
const channelList = ref([])
const sendResult = reactive({
  batchId: '',
  totalCount: 0,
  successCount: 0,
  failureCount: 0
})

// 获取发送渠道列表
const getChannelList = async () => {
  try {
    const response = await getSendChannelsByType(1) // 1-短信
    if (response.code === 200) {
      channelList.value = response.data || []
      // 默认选择第一个渠道
      if (channelList.value.length > 0) {
        smsForm.channelCode = channelList.value[0].channelCode
      }
    }
  } catch (error) {
    console.error('获取发送渠道失败:', error)
  }
}

// 获取手机号码列表
const getPhoneList = () => {
  if (smsForm.sendType === 1) {
    // 手动输入方式
    if (!smsForm.phoneNumbers) return []
    
    const phones = smsForm.phoneNumbers
      .split(/[,，\n\r]/)
      .map(phone => phone.trim())
      .filter(phone => phone && /^1[3-9]\d{9}$/.test(phone))
    
    // 去重
    return [...new Set(phones)]
  } else {
    // Excel导入方式
    return parsedPhones.value
  }
}

// 文件上传处理
const handleFileChange = (file, files) => {
  // 检查文件类型
  const isExcel = file.name.endsWith('.xlsx') || file.name.endsWith('.xls')
  if (!isExcel) {
    ElMessage.error('只能上传Excel文件')
    files.splice(files.indexOf(file), 1)
    return
  }

  // 检查文件大小
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('文件大小不能超过5MB')
    files.splice(files.indexOf(file), 1)
    return
  }

  fileList.value = files
  parseExcelFile(file.raw)
}

// 文件移除处理
const handleFileRemove = (file, files) => {
  fileList.value = files
  parsedPhones.value = []
}

// 文件数量超限处理
const handleExceed = () => {
  ElMessage.warning('只能上传一个Excel文件')
}

// 解析Excel文件
const parseExcelFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      const phones = []
      jsonData.forEach(row => {
        if (row && row.length > 0) {
          const phone = String(row[0]).trim()
          if (/^1[3-9]\d{9}$/.test(phone)) {
            phones.push(phone)
          }
        }
      })
      
      // 去重
      parsedPhones.value = [...new Set(phones)]
      
      if (parsedPhones.value.length === 0) {
        ElMessage.warning('Excel文件中没有找到有效的手机号码')
      } else {
        ElMessage.success(`成功解析到${parsedPhones.value.length}个手机号码`)
      }
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      ElMessage.error('解析Excel文件失败')
    }
  }
  reader.readAsArrayBuffer(file)
}

// 下载模板
const downloadTemplate = () => {
  const data = [
    ['手机号码'],
    ['13800138000'],
    ['13800138001'],
    ['13800138002']
  ]
  
  const worksheet = XLSX.utils.aoa_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, '手机号码')
  XLSX.writeFile(workbook, '批量短信模板.xlsx')
}

// 批量发送短信
const sendBatchSms = async () => {
  try {
    // 表单验证
    await smsFormRef.value.validate()

    const phoneList = getPhoneList()
    if (phoneList.length === 0) {
      ElMessage.error('请输入有效的手机号码')
      return
    }

    sending.value = true

    const smsData = {
      phoneNumbers: phoneList,
      content: smsForm.content
    }

    // 如果指定了渠道
    if (smsForm.channelCode) {
      smsData.channelCode = smsForm.channelCode
    }

    const response = await sendBatchSmsApi(smsData)

    if (response.code === 200) {
      // 显示发送结果
      Object.assign(sendResult, response.data)
      resultVisible.value = true
      ElMessage.success('批量短信发送完成')
    } else {
      ElMessage.error(response.message || '批量短信发送失败')
    }
  } catch (error) {
    console.error('批量发送短信失败:', error)
    ElMessage.error('批量发送短信失败')
  } finally {
    sending.value = false
  }
}

// 重置表单
const resetForm = () => {
  smsFormRef.value.resetFields()
  fileList.value = []
  parsedPhones.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

// 预览短信
const previewSms = async () => {
  try {
    await smsFormRef.value.validate()
    previewVisible.value = true
  } catch (error) {
    ElMessage.warning('请先完善短信信息')
  }
}

// 组件挂载时获取渠道列表
onMounted(() => {
  getChannelList()
})
</script>

<style scoped>
.batch-sms-container {
  padding: 20px;
}

.box-card {
  max-width: 900px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.sms-form {
  margin-top: 20px;
}

.input-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.el-upload__tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.parsed-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.result-summary {
  margin-bottom: 10px;
}

.phone-list {
  max-height: 200px;
  overflow-y: auto;
}

.phone-tag {
  margin: 2px;
}

.more-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 10px;
}

.sms-preview {
  padding: 20px;
}

.preview-item {
  margin-bottom: 15px;
  line-height: 1.6;
}

.content-preview {
  margin-top: 10px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;
  min-height: 60px;
  white-space: pre-wrap;
}

.phone-preview {
  margin-top: 10px;
  max-height: 150px;
  overflow-y: auto;
}

.send-result {
  padding: 20px;
}

.result-item {
  margin-bottom: 15px;
  line-height: 1.6;
  font-size: 16px;
}

.success-count {
  color: #67c23a;
  font-weight: bold;
}

.failure-count {
  color: #f56c6c;
  font-weight: bold;
}
</style>