# 通知平台前端项目

## 项目介绍

通知平台前端项目是一个基于Vue 3、Element Plus的前端应用，用于实现通知平台的后台管理功能。

## 技术栈

- Vue 3
- Vue Router
- Pinia (状态管理)
- Element Plus (UI组件库)
- Axios (HTTP请求)
- Vite (构建工具)
- ESLint + Prettier (代码规范)

## 项目结构

```
notification-platform-frontend/
├── public/                 # 静态资源
├── src/
│   ├── assets/             # 资源文件
│   │   ├── images/         # 图片
│   │   └── styles/         # 样式
│   ├── components/         # 公共组件
│   ├── layout/             # 布局组件
│   ├── router/             # 路由配置
│   ├── stores/             # Pinia状态管理
│   ├── utils/              # 工具函数
│   ├── views/              # 页面组件
│   ├── App.vue             # 根组件
│   └── main.js             # 入口文件
├── .eslintrc.js            # ESLint配置
├── .prettierrc             # Prettier配置
├── index.html              # HTML模板
├── package.json            # 项目依赖
└── vite.config.js          # Vite配置
```

## 功能模块

1. 用户认证
   - 登录
   - 密码修改

2. 消息发送
   - 短信/邮件发送
   - 短信/邮件群发
   - 营销邮件发送
   - 休眠账户通知

3. 渠道管理
   - 接入渠道管理
   - 发送渠道管理

4. 模板管理
   - 模板管理
   - 域参数管理
   - 模板类型管理

5. 统计监控
   - 按模板统计
   - 短信/邮件统计

6. 安全管理
   - 黑名单管理
   - 白名单管理
   - 关键字管理

7. 用户管理
   - 操作员管理
   - 角色管理
   - 机构管理

8. 系统设置
   - 密码修改
   - 系统日志

## 开发环境启动

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 构建生产环境

```bash
# 构建生产环境
npm run build

# 预览生产构建
npm run serve
```

## 代码规范

```bash
# 运行ESLint检查
npm run lint

# 运行Prettier格式化
npm run format
```