const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
  constructor() {
    this.testResults = {
      summary: {
        total: 0,
        passed: 0,
        failed: 0,
        skipped: 0,
        startTime: new Date().toISOString(),
        endTime: null,
        duration: 0
      },
      tests: [],
      screenshots: []
    };
    
    this.setupDirectories();
  }

  setupDirectories() {
    // 创建测试结果目录
    const dirs = [
      'test-results',
      'test-results/screenshots',
      'test-results/html-report'
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  async runTests() {
    console.log('🚀 开始执行所有测试案例...\n');
    
    try {
      // 运行Playwright测试
      console.log('📋 执行Playwright测试...');
      const command = 'npx playwright test';

      try {
        const output = execSync(command, {
          encoding: 'utf8',
          stdio: 'pipe'
        });
        console.log('✅ 测试执行完成');
      } catch (error) {
        console.log('⚠️ 测试执行完成（可能有失败的测试）');
        // 即使有测试失败，我们也继续生成报告
      }
      
      // 解析测试结果
      await this.parseTestResults();
      
      // 收集截图
      await this.collectScreenshots();
      
      // 生成汇总报告
      await this.generateSummaryReport();
      
      // 生成HTML报告
      await this.generateHTMLReport();
      
      console.log('\n📊 测试报告生成完成！');
      console.log('📁 查看报告：');
      console.log('   - HTML报告: test-results/html-report/index.html');
      console.log('   - 汇总报告: test-results/test-summary.json');
      console.log('   - 详细报告: test-results/detailed-report.html');
      
    } catch (error) {
      console.error('❌ 测试执行失败:', error.message);
      throw error;
    }
  }

  async parseTestResults() {
    try {
      // 读取Playwright生成的JSON报告
      const resultsPath = 'test-results/results.json';
      if (fs.existsSync(resultsPath)) {
        const rawData = fs.readFileSync(resultsPath, 'utf8');
        const data = JSON.parse(rawData);

        console.log('📄 解析测试结果文件...');

        // 解析测试结果 - 新版Playwright JSON格式
        if (data.suites) {
          this.parseTestSuites(data.suites);
        }

        console.log(`✅ 解析完成: 总计${this.testResults.summary.total}个测试`);
      } else {
        console.log('⚠️ 未找到测试结果文件: test-results/results.json');
      }
    } catch (error) {
      console.log('⚠️ 解析测试结果文件失败:', error.message);
    }

    this.testResults.summary.endTime = new Date().toISOString();
    this.testResults.summary.duration = Date.now() - new Date(this.testResults.summary.startTime).getTime();
  }

  parseTestSuites(suites) {
    suites.forEach(suite => {
      if (suite.suites && suite.suites.length > 0) {
        // 递归处理嵌套的测试套件
        this.parseTestSuites(suite.suites);
      }

      if (suite.specs && suite.specs.length > 0) {
        suite.specs.forEach(spec => {
          if (spec.tests && spec.tests.length > 0) {
            spec.tests.forEach(test => {
              const result = test.results && test.results[0];
              if (result) {
                // 提取测试编号
                const testTitle = spec.title || 'Unknown Test';
                const testIdMatch = testTitle.match(/^(T\d+)-/);
                const testId = testIdMatch ? testIdMatch[1] : null;

                const testResult = {
                  title: testTitle,
                  testId: testId,
                  fullTitle: `${suite.title} > ${testTitle}`,
                  status: result.status || 'unknown',
                  duration: result.duration || 0,
                  error: result.error ? result.error.message || result.error : null,
                  screenshots: [], // 初始化截图数组
                  testDetails: this.getTestDetails(testId, testTitle) // 获取测试详细信息
                };

                this.testResults.tests.push(testResult);
                this.testResults.summary.total++;

                switch (testResult.status) {
                  case 'passed':
                    this.testResults.summary.passed++;
                    break;
                  case 'failed':
                    this.testResults.summary.failed++;
                    break;
                  case 'skipped':
                    this.testResults.summary.skipped++;
                    break;
                }
              }
            });
          }
        });
      }
    });
  }

  async collectScreenshots() {
    const screenshotDir = 'test-results/screenshots';

    if (fs.existsSync(screenshotDir)) {
      const files = fs.readdirSync(screenshotDir);

      console.log('📸 匹配截图到测试用例...');

      // 为每个测试用例匹配相关截图（基于编号）
      this.testResults.tests.forEach(test => {
        if (test.testId) {
          // 使用测试编号精确匹配截图
          const matchingFiles = files.filter(file =>
            file.endsWith('.png') && file.startsWith(test.testId + '-')
          );

          // 收集截图信息并获取创建时间
          const screenshots = matchingFiles.map(file => {
            const filePath = path.join(screenshotDir, file);
            const stats = fs.statSync(filePath);
            return {
              name: file,
              path: `screenshots/${file}`,
              size: stats.size,
              description: this.getScreenshotDescription(file),
              createdTime: stats.birthtime || stats.mtime, // 使用创建时间或修改时间
              createdTimeStr: (stats.birthtime || stats.mtime).toLocaleString('zh-CN')
            };
          });

          // 按创建时间排序
          screenshots.sort((a, b) => a.createdTime - b.createdTime);

          // 添加到测试结果
          test.screenshots.push(...screenshots);

          console.log(`  ${test.testId}: 找到 ${matchingFiles.length} 张截图 (按时间排序)`);
        } else {
          // 如果没有编号，使用关键词匹配（向后兼容）
          const testKeywords = this.getTestKeywords(test.title);
          const matchingScreenshots = [];

          files.forEach(file => {
            if (file.endsWith('.png')) {
              const fileName = file.toLowerCase();

              if (testKeywords.some(keyword => fileName.includes(keyword))) {
                const filePath = path.join(screenshotDir, file);
                const stats = fs.statSync(filePath);
                matchingScreenshots.push({
                  name: file,
                  path: `screenshots/${file}`,
                  size: stats.size,
                  description: this.getScreenshotDescription(file),
                  createdTime: stats.birthtime || stats.mtime,
                  createdTimeStr: (stats.birthtime || stats.mtime).toLocaleString('zh-CN')
                });
              }
            }
          });

          // 按创建时间排序
          matchingScreenshots.sort((a, b) => a.createdTime - b.createdTime);
          test.screenshots.push(...matchingScreenshots);
        }
      });

      // 收集所有截图到全局列表（保持向后兼容）
      const allScreenshots = files
        .filter(file => file.endsWith('.png'))
        .map(file => {
          const filePath = path.join(screenshotDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            path: `screenshots/${file}`,
            size: stats.size,
            description: this.getScreenshotDescription(file),
            createdTime: stats.birthtime || stats.mtime,
            createdTimeStr: (stats.birthtime || stats.mtime).toLocaleString('zh-CN')
          };
        });

      // 按创建时间排序
      allScreenshots.sort((a, b) => a.createdTime - b.createdTime);
      this.testResults.screenshots.push(...allScreenshots);

      console.log(`📸 截图匹配完成，共处理 ${files.filter(f => f.endsWith('.png')).length} 张截图 (已按时间排序)`);
    }
  }

  getTestDetails(testId, testTitle) {
    // 测试用例详细信息数据库
    const testDetailsDB = {
      'T001': {
        content: '验证登录页面能够正常加载并显示所有必要的UI元素',
        purpose: '确保用户能够看到完整的登录界面，为后续登录操作提供基础',
        input: '访问登录页面URL: http://localhost:3000/login',
        expectedOutput: '页面正常加载，显示用户名输入框、密码输入框和登录按钮',
        testSteps: [
          '1. 访问登录页面',
          '2. 等待页面加载完成',
          '3. 验证登录容器存在',
          '4. 验证用户名输入框可见',
          '5. 验证密码输入框可见',
          '6. 验证登录按钮可见'
        ]
      },
      'T002': {
        content: '验证用户名和密码输入框的填写功能是否正常工作',
        purpose: '确保用户能够正常输入登录凭据，验证表单输入功能',
        input: '用户名: testuser, 密码: testpass',
        expectedOutput: '输入框能够正常接收和显示输入内容',
        testSteps: [
          '1. 访问登录页面',
          '2. 在用户名输入框中输入"testuser"',
          '3. 验证用户名输入成功',
          '4. 在密码输入框中输入"testpass"',
          '5. 验证密码输入成功',
          '6. 确认表单填写完整'
        ]
      },
      'T003': {
        content: '验证登录提交功能，测试用户点击登录按钮后的系统响应',
        purpose: '确保登录流程能够正常执行，验证系统对登录请求的处理',
        input: '用户名: admin, 密码: Admin123!',
        expectedOutput: '系统处理登录请求，根据凭据有效性给出相应响应',
        testSteps: [
          '1. 访问登录页面',
          '2. 输入有效的用户名和密码',
          '3. 点击登录按钮',
          '4. 等待系统响应',
          '5. 验证登录结果（成功跳转或错误提示）'
        ]
      },
      'T004': {
        content: '验证空用户名情况下的登录验证机制',
        purpose: '确保系统能够正确处理用户名为空的异常情况，提供适当的错误提示',
        input: '用户名: (空), 密码: testpass',
        expectedOutput: '系统显示用户名不能为空的错误提示或阻止提交',
        testSteps: [
          '1. 访问登录页面',
          '2. 保持用户名输入框为空',
          '3. 在密码输入框中输入密码',
          '4. 点击登录按钮',
          '5. 验证系统的错误处理机制'
        ]
      },
      'T005': {
        content: '验证空密码情况下的登录验证机制',
        purpose: '确保系统能够正确处理密码为空的异常情况，提供适当的错误提示',
        input: '用户名: testuser, 密码: (空)',
        expectedOutput: '系统显示密码不能为空的错误提示或阻止提交',
        testSteps: [
          '1. 访问登录页面',
          '2. 在用户名输入框中输入用户名',
          '3. 保持密码输入框为空',
          '4. 点击登录按钮',
          '5. 验证系统的错误处理机制'
        ]
      },
      'T006': {
        content: '验证短信发送功能的完整业务流程，包括发送前后的数据变化',
        purpose: '确保短信发送功能正常工作，验证发送量统计的准确性',
        input: '手机号: 13636367233, 短信内容: 测试短信内容',
        expectedOutput: '短信发送成功，今日发送量增加1',
        testSteps: [
          '1. 登录系统',
          '2. 查看发送前的今日短信发送量',
          '3. 进入短信单发页面',
          '4. 填写手机号码和短信内容',
          '5. 点击发送短信',
          '6. 返回仪表板查看发送量变化',
          '7. 验证发送量是否正确增加'
        ]
      },
      'T007': {
        content: '验证短信发送页面的UI元素完整性和可用性',
        purpose: '确保短信发送页面的所有必要元素都正确显示和可用',
        input: '访问短信单发页面',
        expectedOutput: '页面显示手机号输入框、短信内容输入框和发送按钮',
        testSteps: [
          '1. 登录系统',
          '2. 导航到短信单发页面',
          '3. 验证手机号输入框存在且可见',
          '4. 验证短信内容输入框存在且可见',
          '5. 验证发送短信按钮存在且可见'
        ]
      },
      'T008': {
        content: '验证系统对无效手机号码的验证和错误处理机制',
        purpose: '确保系统能够识别和拒绝无效的手机号码，保护系统安全',
        input: '手机号: 123 (无效), 短信内容: 测试短信',
        expectedOutput: '系统显示手机号格式错误的提示信息',
        testSteps: [
          '1. 登录系统',
          '2. 进入短信单发页面',
          '3. 输入无效的手机号码"123"',
          '4. 输入短信内容',
          '5. 点击发送按钮',
          '6. 验证系统的错误提示'
        ]
      },
      'T009': {
        content: '验证系统对空短信内容的验证和错误处理机制',
        purpose: '确保系统不允许发送空内容的短信，维护短信质量',
        input: '手机号: 13636367233, 短信内容: (空)',
        expectedOutput: '系统显示短信内容不能为空的提示信息',
        testSteps: [
          '1. 登录系统',
          '2. 进入短信单发页面',
          '3. 输入有效的手机号码',
          '4. 保持短信内容为空',
          '5. 点击发送按钮',
          '6. 验证系统的错误提示'
        ]
      }
    };

    return testDetailsDB[testId] || {
      content: testTitle,
      purpose: '测试功能正常性',
      input: '待定',
      expectedOutput: '功能正常执行',
      testSteps: ['执行测试步骤']
    };
  }

  getScreenshotDescription(fileName) {
    // 根据文件名生成截图描述
    const descriptions = {
      'login-initial': '登录页面初始状态',
      'elements-verified': '页面元素验证完成',
      'username-filled': '用户名填写完成',
      'form-completed': '表单填写完成',
      'before-submit': '提交前状态',
      'after-submit': '提交后状态',
      'empty-username-state': '用户名为空状态',
      'empty-password-state': '密码为空状态',
      'validation-result': '验证结果',
      'dashboard-before-sms': '发送短信前仪表板',
      'sms-page-loaded': '短信页面加载完成',
      'form-filled': '表单填写完成',
      'after-send': '发送完成后状态',
      'dashboard-after-sms': '发送短信后仪表板',
      'page-elements': '页面元素展示',
      'invalid-phone-filled': '无效手机号填写',
      'empty-content-state': '短信内容为空状态'
    };

    // 提取文件名中的描述部分
    const parts = fileName.replace('.png', '').split('-');
    if (parts.length > 1) {
      const descKey = parts.slice(1).join('-');
      return descriptions[descKey] || descKey.replace(/-/g, ' ');
    }

    return fileName.replace('.png', '');
  }

  getTestKeywords(testTitle) {
    const keywords = [];

    if (testTitle.includes('登录')) {
      keywords.push('login');
    }
    if (testTitle.includes('短信') || testTitle.includes('sms')) {
      keywords.push('sms');
    }
    if (testTitle.includes('页面加载') || testTitle.includes('初始')) {
      keywords.push('initial');
    }
    if (testTitle.includes('填写') || testTitle.includes('表单')) {
      keywords.push('filled', 'form');
    }
    if (testTitle.includes('提交') || testTitle.includes('发送')) {
      keywords.push('submit', 'send', 'after');
    }
    if (testTitle.includes('空') || testTitle.includes('无效')) {
      keywords.push('empty', 'invalid');
    }
    if (testTitle.includes('元素验证') || testTitle.includes('页面元素')) {
      keywords.push('page', 'elements');
    }
    if (testTitle.includes('仪表板') || testTitle.includes('dashboard')) {
      keywords.push('dashboard');
    }

    return keywords;
  }

  async generateSummaryReport() {
    const summaryPath = 'test-results/test-summary.json';
    fs.writeFileSync(summaryPath, JSON.stringify(this.testResults, null, 2));
  }

  async generateHTMLReport() {
    const htmlContent = this.generateHTMLContent();
    const htmlPath = 'test-results/detailed-report.html';
    fs.writeFileSync(htmlPath, htmlContent);
  }

  generateHTMLContent() {
    const { summary, tests, screenshots } = this.testResults;
    
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试报告汇总</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card.passed { border-left-color: #28a745; }
        .summary-card.failed { border-left-color: #dc3545; }
        .summary-card.skipped { border-left-color: #ffc107; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .passed .number { color: #28a745; }
        .failed .number { color: #dc3545; }
        .skipped .number { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-item { background: #fff; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-item.passed { border-left: 4px solid #28a745; }
        .test-item.failed { border-left: 4px solid #dc3545; }
        .test-item.skipped { border-left: 4px solid #ffc107; }
        .test-title { font-weight: bold; margin-bottom: 5px; }
        .test-status { display: inline-block; padding: 6px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .detail-card { transition: transform 0.2s, box-shadow 0.2s; }
        .detail-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }
        .screenshot-item { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; }
        .screenshot-item img { max-width: 100%; height: auto; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 测试报告汇总</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>测试持续时间: ${Math.round(summary.duration / 1000)}秒</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">${summary.total}</div>
            </div>
            <div class="summary-card passed">
                <h3>通过</h3>
                <div class="number">${summary.passed}</div>
            </div>
            <div class="summary-card failed">
                <h3>失败</h3>
                <div class="number">${summary.failed}</div>
            </div>
            <div class="summary-card skipped">
                <h3>跳过</h3>
                <div class="number">${summary.skipped}</div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细测试报告</h2>
            ${tests.map(test => `
                <div class="test-item ${test.status}" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">${test.testId || 'N/A'} - ${test.title.replace(/^T\d+-/, '')}</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-${test.status}" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">${test.status.toUpperCase()}</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ ${test.duration}ms</span>
                            </div>
                        </div>
                        ${test.error ? `<div class="error-details" style="background: #f8d7da; color: #721c24; padding: 12px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em;">${test.error}</div>` : ''}
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${test.testDetails.content}</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${test.testDetails.purpose}</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">${test.testDetails.input}</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">${test.testDetails.expectedOutput}</p>
                            </div>
                        </div>

                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                ${test.testDetails.testSteps.map(step => `<li style="margin-bottom: 4px;">${step}</li>`).join('')}
                            </ol>
                        </div>

                        <div class="execution-result" style="background: ${test.status === 'passed' ? '#d4edda' : test.status === 'failed' ? '#f8d7da' : '#fff3cd'}; padding: 15px; border-radius: 6px; border-left: 4px solid ${test.status === 'passed' ? '#28a745' : test.status === 'failed' ? '#dc3545' : '#ffc107'};">
                            <h4 style="margin: 0 0 8px 0; color: ${test.status === 'passed' ? '#155724' : test.status === 'failed' ? '#721c24' : '#856404'}; font-size: 0.9em;">
                                ${test.status === 'passed' ? '✅ 执行结果 - 通过' : test.status === 'failed' ? '❌ 执行结果 - 失败' : '⚠️ 执行结果 - 跳过'}
                            </h4>
                            <p style="margin: 0; color: ${test.status === 'passed' ? '#155724' : test.status === 'failed' ? '#721c24' : '#856404'}; font-size: 0.85em; line-height: 1.4;">
                                ${test.status === 'passed' ? '测试执行成功，所有验证点均通过，功能正常工作。' :
                                  test.status === 'failed' ? `测试执行失败：${test.error || '未知错误'}` :
                                  '测试被跳过执行。'}
                            </p>
                        </div>
                    </div>


                    ${test.screenshots && test.screenshots.length > 0 ? `
                        <!-- 测试截图区域 -->
                        <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                            <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                                📸 执行截图记录
                                <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">${test.screenshots.length}张</span>
                            </h4>
                            <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                                ${test.screenshots.map((screenshot, index) => `
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="${screenshot.path}" alt="${screenshot.description}" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 ${index + 1}</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 ${screenshot.createdTimeStr}</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">${screenshot.description}</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">${screenshot.name}</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 ${Math.round(screenshot.size / 1024)}KB</p>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    ` : `
                        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404; font-size: 0.85em;">📷 本测试用例未生成截图记录</p>
                        </div>
                    `}
                </div>
            `).join('')}
        </div>

        <div class="section">
            <h2>📸 所有操作截图汇总 (按时间排序)</h2>
            <p style="color: #666; margin-bottom: 15px;">以下是本次测试生成的所有截图，按创建时间排序。详细截图已在上方各测试用例中展示。</p>
            <div class="screenshot-summary" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 12px;">
                ${screenshots.map((screenshot, index) => `
                    <div class="screenshot-summary-item" style="background: #f8f9fa; padding: 10px; border-radius: 6px; text-align: center; border: 1px solid #e9ecef;">
                        <div style="background: #fff; padding: 4px; border-radius: 4px; margin-bottom: 6px;">
                            <img src="${screenshot.path}" alt="${screenshot.name}" style="max-width: 100%; height: 80px; object-fit: cover; border-radius: 4px; cursor: pointer;" onclick="window.open(this.src)">
                        </div>
                        <p style="margin: 0 0 3px 0; font-size: 0.75em; font-weight: bold; color: #495057;">#${index + 1}</p>
                        <p style="margin: 0 0 3px 0; font-size: 0.7em; color: #666; word-break: break-all;">${screenshot.description}</p>
                        <p style="margin: 0 0 2px 0; font-size: 0.65em; color: #28a745;">⏰ ${screenshot.createdTimeStr}</p>
                        <p style="margin: 0; font-size: 0.6em; color: #999;">${Math.round(screenshot.size / 1024)}KB</p>
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  printSummary() {
    const { summary } = this.testResults;
    console.log('\n📊 测试结果汇总:');
    console.log(`   总计: ${summary.total}`);
    console.log(`   ✅ 通过: ${summary.passed}`);
    console.log(`   ❌ 失败: ${summary.failed}`);
    console.log(`   ⏭️ 跳过: ${summary.skipped}`);
    console.log(`   ⏱️ 耗时: ${Math.round(summary.duration / 1000)}秒`);
  }
}

// 主执行函数
async function main() {
  const runner = new TestRunner();
  
  try {
    await runner.runTests();
    runner.printSummary();
  } catch (error) {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = TestRunner;
