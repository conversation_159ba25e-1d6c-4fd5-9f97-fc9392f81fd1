{"timestamp": "2025-07-29T06:55:53.819Z", "summary": {"totalModules": 34, "successfulModules": 34, "failedModules": 0, "totalDuration": 1984984, "successRate": 100}, "results": [{"module": "用户登录测试", "script": "test/auth/login-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 38189}, {"module": "用户管理测试", "script": "test/auth/user-management-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 66256}, {"module": "角色管理测试", "script": "test/auth/role-management-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 105489}, {"module": "渠道配置测试", "script": "test/auth/channel-config-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 119188}, {"module": "API接口测试", "script": "test/api/api-interface-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 130795}, {"module": "短信单发测试", "script": "test/message/sms-single-test.js", "success": true, "exitCode": 0, "hasError": false, "duration": 193269}, {"module": "短信批量测试", "script": "test/message/sms-batch-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 261516}, {"module": "邮件单发测试", "script": "test/message/email-single-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 332043}, {"module": "邮件批量测试", "script": "test/message/email-batch-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 407468}, {"module": "营销邮件测试", "script": "test/message/email-marketing-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 476242}, {"module": "休眠账户通知测试", "script": "test/message/dormant-account-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 543931}, {"module": "数据备份测试", "script": "test/system/data-backup-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 577991}, {"module": "模板管理测试", "script": "test/template/template-manage-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 656566}, {"module": "模板参数测试", "script": "test/template/template-parameter-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 678988}, {"module": "模板类型测试", "script": "test/template/template-type-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 766845}, {"module": "模板版本管理测试", "script": "test/template/template-version-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 833622}, {"module": "国际化支持测试", "script": "test/template/i18n-support-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 900762}, {"module": "接入渠道测试", "script": "test/channel/access-channel-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 968448}, {"module": "发送渠道测试", "script": "test/channel/send-channel-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1036008}, {"module": "ESB接口测试", "script": "test/channel/esb-interface-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1102910}, {"module": "消息队列管理测试", "script": "test/channel/message-queue-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1169873}, {"module": "仪表板统计测试", "script": "test/statistics/dashboard-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1203832}, {"module": "发送详情统计测试", "script": "test/statistics/send-detail-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1270597}, {"module": "模板发送统计测试", "script": "test/statistics/template-statistics-test.js", "success": true, "exitCode": 0, "hasError": false, "duration": 1361124}, {"module": "高级搜索测试", "script": "test/statistics/advanced-search-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1428165}, {"module": "实时监控测试", "script": "test/statistics/real-time-monitor-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1494999}, {"module": "黑名单管理测试", "script": "test/security/blacklist-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1584037}, {"module": "白名单管理测试", "script": "test/security/whitelist-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1675176}, {"module": "关键字过滤测试", "script": "test/security/keyword-filter-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1737893}, {"module": "安全审计测试", "script": "test/security/security-audit-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1804797}, {"module": "密码修改测试", "script": "test/system/password-change-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1871702}, {"module": "系统日志测试", "script": "test/system/system-log-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1903956}, {"module": "权限测试功能", "script": "test/system/permission-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1915938}, {"module": "系统配置管理测试", "script": "test/system/system-config-test.js", "success": true, "exitCode": 0, "hasError": true, "duration": 1982970}]}