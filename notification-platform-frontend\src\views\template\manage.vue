<template>
  <div class="template-manage-container">
    <!-- 查询表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板编号">
          <el-input v-model="searchForm.templateCode" placeholder="请输入模板编号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="模板名称">
          <el-input v-model="searchForm.templateName" placeholder="请输入模板名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="适用渠道">
          <el-select v-model="searchForm.templateType" placeholder="请选择适用渠道" clearable style="width: 200px">
            <el-option label="短信" :value="1" />
            <el-option label="邮件" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="searchForm.typeId" placeholder="请选择模板类型" clearable style="width: 200px">
            <el-option v-for="type in templateTypes" :key="type.id" :label="type.typeName" :value="type.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">模板新增</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="templateCode" label="模板编号" width="120" />
        <el-table-column prop="templateName" label="模板名称" width="200" />
        <el-table-column prop="templateTypeName" label="模板类型" width="120" />
        <el-table-column prop="channelType" label="适用渠道" width="100">
          <template #default="{ row }">
            <el-tag :type="row.channelType === 'EMAIL' ? 'primary' : 'success'">
              {{ row.channelType === 'EMAIL' ? '邮件' : '短信' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="80" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="修改时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)" :disabled="submitting || loading">修改</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)" :disabled="submitting || loading">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="800px" @close="handleDialogClose">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="模板类型" prop="templateType">
          <el-radio-group v-model="formData.templateType" @change="handleTemplateTypeChange">
            <el-radio :label="2">邮件模板</el-radio>
            <el-radio :label="1">短信模板</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模板编号" prop="templateCode">
          <el-input v-model="formData.templateCode" placeholder="请输入模板编号" />
        </el-form-item>
        <el-form-item label="模板名称" prop="templateName">
          <el-input v-model="formData.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板分类" prop="typeId">
          <el-select v-model="formData.typeId" placeholder="请选择模板分类">
            <el-option v-for="type in templateTypes" :key="type.id" :label="type.typeName" :value="type.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 邮件相关字段 -->
        <template v-if="formData.templateType === 2">
          <el-form-item label="邮件主题" prop="subject" :rules="[{ required: true, message: '请输入邮件主题', trigger: 'blur' }]">
            <el-input v-model="formData.subject" placeholder="请输入邮件主题" />
          </el-form-item>
        </template>
        
        <el-form-item label="模板内容" prop="content">
          <!-- 参数插入工具 -->
          <div class="parameter-tools" style="margin-bottom: 10px;">
            <el-input
              v-model="parameterSearch"
              placeholder="搜索参数中文名"
              style="width: 200px; margin-right: 10px;"
              @input="handleParameterSearch"
            />
            <el-select
              v-model="selectedParameter"
              placeholder="选择参数"
              style="width: 200px; margin-right: 10px;"
              filterable
            >
              <el-option
                v-for="param in filteredParameters"
                :key="param.id"
                :label="param.paramName"
                :value="param.paramCode"
              >
                <span>{{ param.paramName }} ({{ param.paramCode }})</span>
              </el-option>
            </el-select>
            <el-button type="primary" @click="insertParameter" :disabled="!selectedParameter">插入参数</el-button>
          </div>
          
          <!-- 邮件富文本编辑器 -->
          <div v-if="formData.templateType === 2" style="border: 1px solid #dcdfe6; border-radius: 4px;">
            <Toolbar
              style="border-bottom: 1px solid #dcdfe6"
              :editor="editorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              style="height: 300px; overflow-y: hidden;"
              v-model="formData.content"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
              @onChange="handleContentChange"
            />
          </div>
          <!-- 短信文本编辑器 -->
          <el-input v-else v-model="formData.content" type="textarea" :rows="6" placeholder="请输入模板内容" @input="handleContentChange" />
        </el-form-item>
        
        <!-- 模板预览 -->
        <el-form-item label="模板预览">
          <el-button type="info" @click="showPreviewDialog" :disabled="!formData.content">预览模板</el-button>
        </el-form-item>
        
        <el-form-item label="模板描述" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false" :disabled="submitting">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :disabled="!hasPreviewed || submitting" :loading="submitting">提交</el-button>
      </template>
    </el-dialog>

    <!-- 模板预览对话框 -->
    <el-dialog v-model="previewDialogVisible" title="模板预览" width="700px">
      <div class="preview-container">
        <!-- 参数输入区域 -->
        <div class="parameter-section">
          <h4>模板参数</h4>
          <el-form v-if="templateParametersWithNames.length > 0" :model="previewParams" label-width="120px">
            <el-form-item v-for="param in templateParametersWithNames" :key="param.code" :label="param.name || param.code">
              <el-input v-model="previewParams[param.code]" :placeholder="`请输入${param.name || param.code}的值`" />
            </el-form-item>
          </el-form>
          <el-empty v-else description="没有发现模板参数" :image-size="60" />
        </div>
        
        <!-- 预览操作 -->
        <div class="preview-actions">
          <el-button type="primary" @click="handlePreview" :loading="previewLoading">生成预览</el-button>
        </div>
        
        <!-- 预览结果 -->
        <div class="preview-result" v-if="previewResult">
          <h4>预览结果</h4>
          <template v-if="formData.templateType === 2">
            <div class="result-item">
              <label>邮件主题：</label>
              <div class="result-content">{{ renderPreviewContent(formData.subject, false) }}</div>
            </div>
            <div class="result-item">
              <label>邮件内容：</label>
              <div class="result-content" v-html="renderPreviewContent(formData.content, true)"></div>
            </div>
          </template>
          <template v-else>
            <div class="result-item">
              <label>短信内容：</label>
              <div class="result-content">{{ renderPreviewContent(formData.content, false) }}</div>
            </div>
          </template>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="previewDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="confirmPreview">确认预览</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, shallowRef, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { getTemplatePage, createTemplate, updateTemplate, deleteTemplate, renderSmsTemplate, renderEmailTemplate, parseTemplateParameters } from '@/api/template'
import { getAllEnabledTemplateTypes } from '@/api/templateType'
import { getAllEnabledParameters } from '@/api/parameter'

const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()
const hasPreviewed = ref(false)
const submitting = ref(false)

// 富文本编辑器相关
const editorRef = shallowRef()
const mode = 'default'
const toolbarConfig = {
  toolbarKeys: [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    'lineHeight',
    '|',
    'bulletedList',
    'numberedList',
    'todo',
    '|',
    'emotion',
    'insertLink',
    'insertTable',
    '|',
    'codeBlock',
    'divider',
    'undo',
    'redo'
  ]
}
const editorConfig = {
  placeholder: '请输入邮件内容...',
  readOnly: false,
  autoFocus: false,
  scroll: true,
  MENU_CONF: {
    uploadImage: {
      server: '/api/upload/image',
      fieldName: 'file'
    }
  }
}

// 预览相关
const previewDialogVisible = ref(false)
const previewLoading = ref(false)
const templateParameters = ref([])
const templateParametersWithNames = ref([])
const previewParams = ref({})
const previewResult = ref(null)

// 参数插入相关
const parameterSearch = ref('')
const selectedParameter = ref('')
const availableParameters = ref([])
const filteredParameters = ref([])

const searchForm = reactive({
  templateCode: '',
  templateName: '',
  templateType: '',
  typeId: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const tableData = ref([])
const templateTypes = ref([])

const formData = reactive({
  id: null,
  templateType: 2,
  templateCode: '',
  templateName: '',
  typeId: '',
  subject: '',
  content: '',
  description: '',
  status: 1
})

const formRules = {
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  templateCode: [{ required: true, message: '请输入模板编号', trigger: 'blur' }],
  templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  typeId: [{ required: true, message: '请选择模板分类', trigger: 'change' }],
  content: [{ required: true, message: '请输入模板内容', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      templateCode: searchForm.templateCode,
      templateName: searchForm.templateName,
      templateType: searchForm.templateType,
      typeId: searchForm.typeId
    }
    
    const response = await getTemplatePage(params)
    
    if (response.code === 200) {
      tableData.value = response.data.records.map(item => ({
        ...item,
        channelType: item.templateType === 1 ? 'SMS' : 'EMAIL',
        templateTypeName: item.typeName
      }))
      pagination.total = response.data.total
    } else {
      ElMessage.error(response.message || '查询失败')
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const loadTemplateTypes = async () => {
  try {
    const response = await getAllEnabledTemplateTypes()
    if (response.code === 200) {
      templateTypes.value = response.data || []
    }
  } catch (error) {
    console.error('加载模板类型失败', error)
  }
}

const loadParameters = async () => {
  try {
    const response = await getAllEnabledParameters()
    if (response.code === 200) {
      availableParameters.value = response.data || []
      filteredParameters.value = response.data || []
    }
  } catch (error) {
    console.error('加载参数失败', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    templateCode: '',
    templateName: '',
    templateType: '',
    typeId: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogTitle.value = '模板新增'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '模板修改'
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
  // 延迟设置编辑器内容，确保编辑器已初始化
  setTimeout(() => {
    if (editorRef.value && row.templateType === 2 && row.content) {
      // 确保内容被正确设置到编辑器
      editorRef.value.setHtml(row.content)
      // 同时更新formData.content以保持同步
      formData.content = row.content
    }
  }, 200)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该模板吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteTemplate(row.id)
    if (response.success) {
      ElMessage.success({
        message: '✓ 删除成功',
        showClose: false
      })
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleTemplateTypeChange = () => {
  // 切换模板类型时重置相关字段
  if (formData.templateType === 1) {
    formData.subject = ''
  } else {
    // 切换到邮件模板时，延迟设置编辑器内容
    setTimeout(() => {
      if (editorRef.value && formData.content) {
        editorRef.value.setHtml(formData.content)
      }
    }, 100)
  }
  hasPreviewed.value = false
}

const handleCreated = (editor) => {
  editorRef.value = editor
  // 如果有初始内容，设置到编辑器
  if (formData.content && formData.templateType === 2) {
    editor.setHtml(formData.content)
  }
}

const handleSubmit = async () => {
  if (!hasPreviewed.value) {
    ElMessage.warning('请先预览模板后再提交')
    return
  }
  
  if (submitting.value) {
    return
  }
  
  submitting.value = true
  try {
    await formRef.value.validate()
    
    const apiCall = isEdit.value ? updateTemplate : createTemplate
    const params = isEdit.value ? [formData.id, formData] : [formData]
    const response = await apiCall(...params)
    
    if (response.code === 200) {
      ElMessage.success({
        message: isEdit.value ? '✓ 修改成功' : '✓ 新增成功',
        showClose: false
      })
      dialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败', error)
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  if (submitting.value) {
    return
  }
  formRef.value?.resetFields()
  resetForm()
  hasPreviewed.value = false
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    templateType: 2,
    templateCode: '',
    templateName: '',
    typeId: '',
    subject: '',
    content: '',
    description: '',
    status: 1
  })
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

// 模板内容变化时解析参数
const handleContentChange = async (editor) => {
  hasPreviewed.value = false
  
  // 如果是富文本编辑器的变化，更新formData.content
  if (editor && formData.templateType === 2) {
    formData.content = editor.getHtml()
  }
  
  if (!formData.content) {
    templateParameters.value = []
    templateParametersWithNames.value = []
    return
  }
  
  // 解析模板参数
  const content = formData.templateType === 2 ? formData.content.replace(/<[^>]*>/g, '') : formData.content
  const paramRegex = /\{([^}]+)\}/g
  const params = []
  let match
  while ((match = paramRegex.exec(content)) !== null) {
    if (!params.includes(match[1])) {
      params.push(match[1])
    }
  }
  templateParameters.value = params
  
  // 获取参数的中文名
  templateParametersWithNames.value = params.map(paramCode => {
    const param = availableParameters.value.find(p => p.paramCode === paramCode)
    return {
      code: paramCode,
      name: param ? param.paramName : paramCode
    }
  })
}

// 显示预览对话框
const showPreviewDialog = async () => {
  await handleContentChange()
  
  // 初始化预览参数
  previewParams.value = {}
  templateParameters.value.forEach(param => {
    previewParams.value[param] = ''
  })
  
  previewResult.value = null
  previewDialogVisible.value = true
}

// 参数搜索
const handleParameterSearch = () => {
  if (!parameterSearch.value) {
    filteredParameters.value = availableParameters.value
  } else {
    filteredParameters.value = availableParameters.value.filter(param => 
      param.paramName.includes(parameterSearch.value) || 
      param.paramCode.includes(parameterSearch.value)
    )
  }
}

// 插入参数
const insertParameter = () => {
  if (!selectedParameter.value) return
  
  const parameterPlaceholder = `{${selectedParameter.value}}`
  
  if (formData.templateType === 2 && editorRef.value) {
    // 富文本编辑器插入
    editorRef.value.insertText(parameterPlaceholder)
  } else {
    // 文本框插入到末尾
    formData.content = (formData.content || '') + parameterPlaceholder
  }
  
  // 重新解析参数
  handleContentChange()
  
  // 清空选择
  selectedParameter.value = ''
  parameterSearch.value = ''
  filteredParameters.value = availableParameters.value
}

// 前端渲染预览内容
const renderPreviewContent = (content, isHtml = false) => {
  if (!content) return ''
  
  let result = content
  templateParameters.value.forEach(param => {
    const value = previewParams.value[param] || `{${param}}`
    const regex = new RegExp(`\\{${param}\\}`, 'g')
    result = result.replace(regex, value)
  })
  
  // 如果是HTML内容，直接返回用于v-html渲染
  // 如果不是HTML内容，返回纯文本
  return result
}

// 生成预览
const handlePreview = async () => {
  previewLoading.value = true
  try {
    // 前端直接生成预览，不调用后端接口
    previewResult.value = {
      subject: formData.subject,
      content: formData.content
    }
    
    ElMessage.success({
      message: '✓ 预览生成成功',
      showClose: false
    })
  } catch (error) {
    ElMessage.error('预览失败')
  } finally {
    previewLoading.value = false
  }
}

// 确认预览
const confirmPreview = () => {
  hasPreviewed.value = true
  previewDialogVisible.value = false
  ElMessage.success('预览确认成功，现在可以提交模板')
}

onMounted(() => {
  loadTemplateTypes()
  loadParameters()
  loadData()
})

onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})
</script>

<style scoped>
.template-manage-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.preview-container {
  max-height: 500px;
  overflow-y: auto;
}

.parameter-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.parameter-section h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.preview-actions {
  margin-bottom: 20px;
  text-align: center;
}

.preview-result {
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #e1f5fe;
}

.preview-result h4 {
  margin: 0 0 15px 0;
  color: #333;
}

.result-item {
  margin-bottom: 15px;
}

.result-item label {
  font-weight: bold;
  color: #666;
  display: block;
  margin-bottom: 5px;
}

.result-content {
  padding: 10px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 40px;
  word-wrap: break-word;
}

/* 富文本编辑器样式 */
.w-e-text-placeholder {
  color: #c0c4cc;
}

.w-e-text-container {
  background-color: #fff;
}

.w-e-toolbar {
  background-color: #fafafa;
}



/* 富文本编辑器容器 */
.rich-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.rich-editor-container:focus-within {
  border-color: #409eff;
}

/* 参数插入工具样式 */
.parameter-tools {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 10px;
}

.parameter-tools .el-input,
.parameter-tools .el-select {
  margin-right: 10px;
}

.parameter-tools .el-input:last-child,
.parameter-tools .el-select:last-child,
.parameter-tools .el-button:last-child {
  margin-right: 0;
}
</style>