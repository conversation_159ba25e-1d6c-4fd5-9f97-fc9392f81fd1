/**
 * RSA加密工具类
 */
class RSAUtil {
  constructor() {
    this.jsencrypt = null
    this.publicKey = null
    this.isInitialized = false
  }

  /**
   * 初始化JSEncrypt实例
   */
  _initJSEncrypt() {
    try {
      if (typeof JSEncrypt === 'undefined') {
        console.error('JSEncrypt库未加载')
        return false
      }
      this.jsencrypt = new JSEncrypt()
      return true
    } catch (error) {
      console.error('JSEncrypt初始化失败:', error)
      return false
    }
  }

  /**
   * 设置公钥
   * @param {string} publicKey - RSA公钥
   */
  setPublicKey(publicKey) {
    if (!publicKey || typeof publicKey !== 'string') {
      throw new Error('无效的RSA公钥')
    }

    // 确保JSEncrypt实例已初始化
    if (!this.jsencrypt && !this._initJSEncrypt()) {
      throw new Error('JSEncrypt初始化失败')
    }

    try {
      this.publicKey = publicKey
      this.jsencrypt.setPublicKey(publicKey)
      this.isInitialized = true
      console.log('RSA公钥设置成功')
    } catch (error) {
      console.error('设置RSA公钥失败:', error)
      throw new Error('设置RSA公钥失败')
    }
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    return this.isInitialized && this.publicKey && this.jsencrypt
  }

  /**
   * 加密文本
   * @param {string} text - 要加密的文本
   * @returns {string} 加密后的文本
   */
  encrypt(text) {
    if (!this.checkInitialized()) {
      throw new Error('RSA未初始化，请先获取公钥')
    }

    if (!text || typeof text !== 'string') {
      throw new Error('待加密文本不能为空')
    }
    
    try {
      const encrypted = this.jsencrypt.encrypt(text)
      if (!encrypted) {
        throw new Error('加密操作返回空值')
      }
      return encrypted
    } catch (error) {
      console.error('RSA加密失败:', error)
      throw new Error('密码加密失败，请刷新页面重试')
    }
  }

  /**
   * 加密密码
   * @param {string} password - 要加密的密码
   * @returns {string} 加密后的密码
   */
  encryptPassword(password) {
    return this.encrypt(password)
  }

  /**
   * 重置工具类
   */
  reset() {
    this.jsencrypt = null
    this.publicKey = null
    this.isInitialized = false
  }
}

// 创建全局实例
const rsaUtil = new RSAUtil()

export default rsaUtil