/**
 * T081-T090: 营销邮件发送功能测试
 * 基于需求文档中的营销邮件发送功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class EmailMarketingTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T081: 营销邮件页面加载测试
   */
  async testT081_MarketingEmailPageLoad() {
    const testId = 'T081';
    console.log(`\n🧪 执行测试 ${testId}: 营销邮件页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到营销邮件页面
      await this.testHelper.navigateTo('/message/email/marketing');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isSubjectInputVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.subjectInput);
      const isHtmlEditorVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.htmlEditor);
      const isEmailListVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.emailListTextarea);
      const isSendButtonVisible = await this.testHelper.verifyElementVisibility(selectors.emailBatch.sendButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '营销邮件页面加载测试',
        testContent: '验证营销邮件发送页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保营销人员能够看到完整的营销邮件发送界面',
        testInput: '访问营销邮件页面URL: /message/email/marketing',
        expectedOutput: '页面正常加载，显示邮件主题、HTML编辑器、目标邮箱列表和发送按钮',
        actualOutput: `主题输入: ${isSubjectInputVisible ? '✅显示' : '❌隐藏'}, HTML编辑器: ${isHtmlEditorVisible ? '✅显示' : '❌隐藏'}, 邮箱列表: ${isEmailListVisible ? '✅显示' : '❌隐藏'}, 发送按钮: ${isSendButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isSubjectInputVisible && isEmailListVisible && isSendButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '营销邮件页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T082: 营销邮件发送测试
   */
  async testT082_MarketingEmailSend() {
    const testId = 'T082';
    console.log(`\n🧪 执行测试 ${testId}: 营销邮件发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到营销邮件页面
      await this.testHelper.navigateTo('/message/email/marketing');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写营销邮件信息
      const marketingEmails = testData.email.batchEmails.slice(0, 3).join('\n'); // 使用前3个邮箱
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, testData.email.marketingSubject);
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, marketingEmails);
      
      // 填写HTML内容
      try {
        await this.testHelper.page.fill(selectors.emailBatch.htmlEditor, testData.email.marketingContent);
      } catch (htmlError) {
        // 如果没有HTML编辑器，使用普通文本框
        await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, testData.email.marketingContent);
      }
      
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送营销邮件
      await this.testHelper.page.click(selectors.emailBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '营销邮件发送测试',
        testContent: '发送包含营销内容的HTML格式邮件',
        testPurpose: '验证营销邮件发送功能能够正常工作',
        testInput: `目标邮箱: ${marketingEmails.split('\n').length}个, 主题: ${testData.email.marketingSubject}`,
        expectedOutput: '营销邮件发送成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '营销邮件发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T083: 营销邮件模板使用测试
   */
  async testT083_MarketingTemplateUse() {
    const testId = 'T083';
    console.log(`\n🧪 执行测试 ${testId}: 营销邮件模板使用测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到营销邮件页面
      await this.testHelper.navigateTo('/message/email/marketing');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 尝试选择模板
      try {
        await this.testHelper.page.click(selectors.emailBatch.templateSelect);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('模板选择下拉框');
        
        // 选择第一个模板
        await this.testHelper.page.click(`${selectors.emailBatch.templateSelect} option:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('模板选择完成');
        
        // 填写其他信息
        const marketingEmails = testData.email.batchEmails.slice(0, 2).join('\n');
        await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, marketingEmails);
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 发送邮件
        await this.testHelper.page.click(selectors.emailBatch.sendButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '营销邮件模板使用测试',
          testContent: '使用预定义模板发送营销邮件',
          testPurpose: '验证营销邮件模板功能能够正常工作',
          testInput: `使用模板发送给${marketingEmails.split('\n').length}个邮箱`,
          expectedOutput: '使用模板发送营销邮件成功，显示成功提示',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (templateError) {
        // 如果没有模板选择功能，标记为跳过
        const result = {
          testId: testId,
          testName: '营销邮件模板使用测试',
          testContent: '使用预定义模板发送营销邮件',
          testPurpose: '验证营销邮件模板功能能够正常工作',
          testInput: '查找模板选择功能',
          expectedOutput: '找到模板选择器并成功使用',
          actualOutput: '未找到模板选择功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到模板选择功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '营销邮件模板使用测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T084: 营销邮件定时发送测试
   */
  async testT084_ScheduledMarketingEmail() {
    const testId = 'T084';
    console.log(`\n🧪 执行测试 ${testId}: 营销邮件定时发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到营销邮件页面
      await this.testHelper.navigateTo('/message/email/marketing');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写营销邮件信息
      const marketingEmails = testData.email.batchEmails.slice(0, 2).join('\n');
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, '定时营销邮件测试');
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, marketingEmails);
      await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, '这是一封定时发送的营销邮件');
      
      // 尝试设置定时发送
      try {
        // 查找定时发送相关的元素
        const scheduleDatePicker = await this.testHelper.verifyElementVisibility('.schedule-date-picker');
        if (scheduleDatePicker) {
          await this.testHelper.page.click('.schedule-date-picker');
          await this.screenshotHelper.takeCustomScreenshot('定时发送设置');
          
          // 设置明天的时间
          const tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          const tomorrowStr = tomorrow.toISOString().split('T')[0];
          
          await this.testHelper.page.fill('.schedule-date-picker input', tomorrowStr);
          await this.screenshotHelper.takeFormFilledScreenshot();
          
          // 提交定时发送任务
          await this.testHelper.page.click(selectors.emailBatch.sendButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          const successMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '营销邮件定时发送测试',
            testContent: '设置营销邮件的定时发送功能',
            testPurpose: '验证营销邮件定时发送功能能够正常工作',
            testInput: `定时时间: ${tomorrowStr}, 目标邮箱: ${marketingEmails.split('\n').length}个`,
            expectedOutput: '定时发送任务创建成功，显示成功提示',
            actualOutput: `成功消息: ${successMessage || '无'}`,
            result: successMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } else {
          throw new Error('未找到定时发送功能');
        }
        
      } catch (scheduleError) {
        // 如果没有定时发送功能，标记为跳过
        const result = {
          testId: testId,
          testName: '营销邮件定时发送测试',
          testContent: '设置营销邮件的定时发送功能',
          testPurpose: '验证营销邮件定时发送功能能够正常工作',
          testInput: '查找定时发送功能',
          expectedOutput: '找到定时发送设置并成功配置',
          actualOutput: '未找到定时发送功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到定时发送功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '营销邮件定时发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T085: 营销邮件退订功能测试
   */
  async testT085_MarketingEmailUnsubscribe() {
    const testId = 'T085';
    console.log(`\n🧪 执行测试 ${testId}: 营销邮件退订功能测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到营销邮件页面
      await this.testHelper.navigateTo('/message/email/marketing');
      await this.testHelper.waitForPageLoad(selectors.emailBatch.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写包含退订链接的营销邮件
      const marketingEmails = testData.email.batchEmails.slice(0, 2).join('\n');
      const contentWithUnsubscribe = testData.email.marketingContent + '\n\n<p><a href="{unsubscribe_link}">点击退订</a></p>';
      
      await this.testHelper.page.fill(selectors.emailBatch.subjectInput, '包含退订链接的营销邮件');
      await this.testHelper.page.fill(selectors.emailBatch.emailListTextarea, marketingEmails);
      
      try {
        await this.testHelper.page.fill(selectors.emailBatch.htmlEditor, contentWithUnsubscribe);
      } catch (htmlError) {
        await this.testHelper.page.fill(selectors.emailBatch.contentTextarea, contentWithUnsubscribe);
      }
      
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送包含退订链接的营销邮件
      await this.testHelper.page.click(selectors.emailBatch.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '营销邮件退订功能测试',
        testContent: '发送包含退订链接的营销邮件',
        testPurpose: '验证营销邮件退订功能的集成',
        testInput: `包含退订链接的邮件内容，目标邮箱: ${marketingEmails.split('\n').length}个`,
        expectedOutput: '包含退订链接的营销邮件发送成功',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '营销邮件退订功能测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有营销邮件测试
   */
  async runAllTests() {
    console.log('🚀 开始执行营销邮件发送功能测试套件 (T081-T085)');
    
    const startTime = Date.now();
    
    await this.testT081_MarketingEmailPageLoad();
    await this.testT082_MarketingEmailSend();
    await this.testT083_MarketingTemplateUse();
    await this.testT084_ScheduledMarketingEmail();
    await this.testT085_MarketingEmailUnsubscribe();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 营销邮件发送功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const emailMarketingTest = new EmailMarketingTest();
  emailMarketingTest.runAllTests().catch(console.error);
}

module.exports = EmailMarketingTest;
