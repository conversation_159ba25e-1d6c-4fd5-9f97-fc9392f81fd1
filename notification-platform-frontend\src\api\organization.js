import request from '@/utils/request'

// 获取机构列表
export function getOrganizationList(params) {
  return request({
    url: '/auth/organizations',
    method: 'get',
    params
  })
}

// 获取机构详情
export function getOrganizationDetail(id) {
  return request({
    url: `/auth/organizations/${id}`,
    method: 'get'
  })
}

// 创建机构
export function createOrganization(data) {
  return request({
    url: '/auth/organizations',
    method: 'post',
    data
  })
}

// 更新机构
export function updateOrganization(id, data) {
  return request({
    url: `/auth/organizations/${id}`,
    method: 'put',
    data
  })
}

// 删除机构
export function deleteOrganization(id) {
  return request({
    url: `/auth/organizations/${id}`,
    method: 'delete'
  })
}