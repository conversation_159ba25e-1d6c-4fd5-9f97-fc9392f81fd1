// 测试数据配置文件
module.exports = {
  // 用户认证数据
  auth: {
    validUser: {
      username: 'admin',
      password: 'Admin123!'
    },
    invalidUser: {
      username: 'wronguser',
      password: 'wrongpass'
    },
    testUser: {
      username: 'testuser',
      password: 'testpass'
    },
    newUser: {
      username: 'newuser001',
      password: 'abcd1234',
      name: '测试用户001',
      phone: '13800138001',
      email: '<EMAIL>'
    },
    lockedUser: {
      username: 'lockeduser',
      password: 'wrongpass'
    }
  },

  // 短信测试数据
  sms: {
    validPhone: '***********',
    invalidPhone: '123',
    overseasPhone: '+1234567890',
    content: '这是一条测试短信内容',
    longContent: '这是一条超长的测试短信内容，用于测试字符限制功能。'.repeat(5),
    batchPhones: [
      '13800138001',
      '***********', 
      '13800138003',
      '13800138004',
      '13800138005'
    ],
    templateContent: '您的验证码是{code}，请在{time}分钟内使用。【测试】'
  },

  // 邮件测试数据
  email: {
    validEmail: '<EMAIL>',
    invalidEmail: 'invalid-email',
    subject: '测试邮件主题',
    content: '这是一封测试邮件的内容，用于验证邮件发送功能。',
    htmlContent: '<h1>测试邮件</h1><p>这是HTML格式的邮件内容</p><p>包含<strong>粗体</strong>和<em>斜体</em>文字。</p>',
    batchEmails: [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ],
    marketingSubject: '银行产品推广邮件',
    marketingContent: '<html><body><h1>银行新产品推广</h1><p>尊敬的客户，我们推出了新的理财产品...</p></body></html>',
    dormantSubject: '账户激活提醒',
    dormantContent: '尊敬的{customerName}，您的账户{accountNo}已休眠{dormantDays}天，请及时激活。'
  },

  // 模板测试数据
  template: {
    smsTemplate: {
      code: 'SMS_TEST_001',
      name: '测试短信模板',
      content: '您的验证码是{code}，请在{time}分钟内使用。【测试】',
      type: 'SMS',
      priority: 5
    },
    emailTemplate: {
      code: 'EMAIL_TEST_001', 
      name: '测试邮件模板',
      subject: '验证码通知',
      content: '尊敬的{name}，您的验证码是{code}，有效期{time}分钟。',
      type: 'EMAIL',
      priority: 3
    },
    parameters: {
      code: '123456',
      time: '5',
      name: '张三',
      customerName: '李四',
      accountNo: '****************',
      dormantDays: '90'
    },
    templateType: {
      code: 'VERIFY',
      name: '验证码类'
    }
  },

  // 渠道测试数据
  channel: {
    accessChannel: {
      id: 'TEST_CH_001',
      name: '测试接入渠道',
      status: 'ACTIVE',
      description: '用于测试的接入渠道'
    },
    sendChannel: {
      name: '测试发送渠道',
      type: 'SMS',
      status: 'ACTIVE',
      config: {
        url: 'http://test.sms.com',
        username: 'testuser',
        password: 'testpass'
      }
    }
  },

  // 安全管理数据
  security: {
    blacklist: {
      phone: '***********',
      email: '<EMAIL>',
      reason: '测试黑名单用户',
      type: 'PHONE'
    },
    whitelist: {
      phone: '***********',
      email: '<EMAIL>',
      department: '测试部门',
      type: 'PHONE'
    },
    keywords: [
      '敏感词1',
      '敏感词2',
      '测试关键字',
      '违规内容',
      '禁用词汇'
    ]
  },

  // 系统管理数据
  system: {
    role: {
      name: '测试角色',
      code: 'TEST_ROLE',
      description: '用于测试的角色',
      permissions: ['system:user', 'message:sms', 'template:manage']
    },
    user: {
      loginName: 'testuser002',
      userName: '测试用户002',
      phone: '***********',
      email: '<EMAIL>',
      orgId: '001',
      roleIds: ['1', '2']
    },
    organization: {
      name: '测试机构',
      type: 'BUSINESS',
      address: '测试地址123号',
      contact: '***********'
    },
    passwordChange: {
      oldPassword: 'Admin123!',
      newPassword: 'NewPass123!',
      confirmPassword: 'NewPass123!'
    }
  },

  // 统计查询数据
  statistics: {
    dateRange: {
      startDate: '2024-01-01',
      endDate: '2024-12-31'
    },
    queryParams: {
      templateCode: 'SMS_TEST_001',
      status: 'SUCCESS',
      phone: '***********',
      email: '<EMAIL>'
    }
  },

  // 文件上传数据
  files: {
    excelFile: 'test-data.xlsx',
    csvFile: 'test-data.csv',
    htmlFile: 'marketing.html',
    imageFile: 'test-image.jpg',
    batchSmsFile: 'batch-sms.xlsx',
    batchEmailFile: 'batch-email.xlsx'
  },

  // 等待时间配置
  timeouts: {
    short: 2000,
    medium: 5000,
    long: 10000,
    navigation: 30000,
    fileUpload: 15000
  },

  // 测试环境配置
  environment: {
    baseUrl: 'http://localhost:3000',
    apiUrl: 'http://localhost:8080',
    screenshotPath: './test-results/screenshots',
    videoPath: './test-results/videos'
  },

  // 验证规则
  validation: {
    phone: {
      domestic: /^1[3-9]\d{9}$/,
      overseas: /^\+(?!86)\d{11,}$/
    },
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    password: {
      minLength: 8,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    },
    templateCode: /^[A-Z0-9_]{1,50}$/,
    parameterCode: /^[A-Za-z0-9_]{1,100}$/
  },

  // 错误消息
  errorMessages: {
    login: {
      invalidCredentials: '登录密码错误',
      userNotFound: '用户不存在',
      userLocked: '密码错误5次，用户锁定',
      emptyUsername: '请输入用户名',
      emptyPassword: '请输入密码'
    },
    sms: {
      invalidPhone: '请输入正确的手机号码',
      emptyContent: '请输入短信内容',
      contentTooLong: '短信内容不能超过200字符'
    },
    email: {
      invalidEmail: '请输入正确的邮箱地址',
      emptySubject: '请输入邮件主题',
      emptyContent: '请输入邮件内容',
      subjectTooLong: '邮件主题不能超过50字符'
    },
    template: {
      duplicateCode: '模板编码已存在',
      emptyCode: '请输入模板编码',
      emptyName: '请输入模板名称',
      emptyContent: '请输入模板内容'
    }
  },

  // 成功消息
  successMessages: {
    login: '登录成功',
    sms: {
      sendSuccess: '短信发送成功',
      batchSendSuccess: '批量发送任务已提交'
    },
    email: {
      sendSuccess: '邮件发送成功',
      batchSendSuccess: '批量发送任务已提交'
    },
    template: {
      saveSuccess: '模板保存成功',
      deleteSuccess: '模板删除成功'
    },
    user: {
      createSuccess: '用户创建成功',
      updateSuccess: '用户更新成功',
      deleteSuccess: '用户删除成功'
    },
    password: {
      changeSuccess: '密码修改成功'
    }
  }
};
