import request from '@/utils/request'

// 查询操作日志
export function getOperationLogs(params) {
  return request({
    url: '/api/system/log/operation',
    method: 'get',
    params
  })
}

// 查询登录日志
export function getLoginLogs(params) {
  return request({
    url: '/api/system/log/login',
    method: 'get',
    params
  })
}

// 获取操作日志统计
export function getOperationStatistics(params) {
  return request({
    url: '/api/system/log/operation/statistics',
    method: 'get',
    params
  })
}

// 获取登录日志统计
export function getLoginStatistics(params) {
  return request({
    url: '/api/system/log/login/statistics',
    method: 'get',
    params
  })
}

// 获取用户最近登录记录
export function getRecentLogins(userId, limit = 10) {
  return request({
    url: `/api/system/log/login/recent/${userId}`,
    method: 'get',
    params: { limit }
  })
}

// 清理旧日志
export function cleanOldLogs(daysTo<PERSON>eep = 90) {
  return request({
    url: '/api/system/log/clean',
    method: 'delete',
    params: { daysToKeep }
  })
}