<template>
  <div class="login-container">
    <div class="login-form">
      <div class="title">
        <h3>通知平台后台管理系统</h3>
      </div>
      
      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" label-width="0">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            maxlength="50"
            prefix-icon="el-icon-user"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            prefix-icon="el-icon-lock"
            @keyup.enter="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button :loading="loading" type="primary" style="width: 100%" @click="handleLogin">
            登录
          </el-button>
        </el-form-item>
        

      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import { setToken, setUserInfo } from '@/utils/auth';
import { login, getPublicKey } from '@/api/auth';
import rsaUtil from '@/utils/rsa';


const router = useRouter();
const route = useRoute();
const loginFormRef = ref(null);
const loading = ref(false);


const loginForm = reactive({
  username: '',
  password: ''
});

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
};

const handleLogin = async () => {
  const valid = await loginFormRef.value.validate().catch(() => false);
  if (!valid) return;
  
  loading.value = true;
  
  try {
    // 检查RSA是否已初始化
    if (!rsaUtil.checkInitialized()) {
      ElMessage({
        message: '系统正在初始化，请稍后重试',
        type: 'warning'
      });
      // 尝试重新初始化
      await initRSAKey();
      if (!rsaUtil.checkInitialized()) {
        throw new Error('系统初始化失败');
      }
    }
    
    // 加密密码
    const encryptedPassword = rsaUtil.encryptPassword(loginForm.password);
    
    const response = await login({
      username: loginForm.username,
      password: encryptedPassword
    });
    
    const { data } = response;
    if (data && data.token) {
      setToken(data.token);
      if (data.userInfo) {
        setUserInfo(data.userInfo);
      }
      
      const redirect = route.query.redirect || '/';
      router.push(redirect);
      ElMessage({
        message: '登录成功',
        type: 'success'
      });
    } else {
      ElMessage({
        message: data?.message || '登录失败：服务器返回格式错误',
        type: 'error'
      });
    }
  } catch (error) {
    console.error('Login error:', error);
    
    let errorMessage = '登录失败';
    if (error.message.includes('加密失败') || error.message.includes('初始化')) {
      errorMessage = error.message;
    } else if (error.response?.status === 401) {
      errorMessage = '用户名或密码错误';
    } else if (error.response?.status >= 500) {
      errorMessage = '服务器错误，请稍后重试';
    } else {
      errorMessage = error.message || '网络错误，请检查网络连接';
    }
    
    ElMessage({
      message: errorMessage,
      type: 'error',
      duration: 5000
    });
  } finally {
    loading.value = false;
  }
};

// 初始化RSA公钥
const initRSAKey = async (retryCount = 0) => {
  const maxRetries = 3;
  
  try {
    console.log('正在获取RSA公钥...');
    const response = await getPublicKey();
    console.log('原始响应:', response);
    
    // 处理响应格式 - request拦截器已经处理了嵌套结构
    let publicKeyData;
    if (response.code === 200 && response.data) {
      // 响应拦截器返回的格式：{code: 200, data: "公钥字符串", message: "获取公钥成功"}
      publicKeyData = response.data;
    } else if (typeof response === 'string') {
      // 直接是公钥字符串
      publicKeyData = response;
    } else {
      throw new Error('无法解析公钥数据');
    }
    
    console.log('提取的公钥:', publicKeyData);
    
    if (publicKeyData && typeof publicKeyData === 'string') {
      console.log('开始设置公钥...');
      rsaUtil.setPublicKey(publicKeyData);
      console.log('RSA公钥初始化成功');
      return true;
    } else {
      console.log('公钥数据验证失败');
      throw new Error('公钥数据格式错误');
    }
  } catch (error) {
    console.error(`获取RSA公钥失败 (尝试 ${retryCount + 1}/${maxRetries}):`, error);
    
    if (retryCount < maxRetries - 1) {
      // 延迟重试
      setTimeout(() => {
        initRSAKey(retryCount + 1);
      }, 1000 * (retryCount + 1));
    } else {
      ElMessage({
        message: '系统初始化失败，请刷新页面重试',
        type: 'error',
        duration: 5000
      });
    }
    return false;
  }
};

// 组件挂载时初始化RSA公钥
onMounted(() => {
  initRSAKey();
});

// 页面可见性变化时重新初始化（处理页面长时间未使用的情况）
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible' && !rsaUtil.checkInitialized()) {
    console.log('页面重新可见，检查RSA初始化状态');
    initRSAKey();
  }
};

onMounted(async () => {
  await initRSAKey();
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 组件卸载时清理事件监听
import { onUnmounted } from 'vue';
onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
});


</script>

<style lang="scss" scoped>
.login-container {
  height: 100%;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  
  .login-form {
    width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    .title {
      text-align: center;
      margin-bottom: 30px;
      
      h3 {
        font-size: 22px;
        color: #303133;
        margin: 0;
      }
    }
  }
}
</style>