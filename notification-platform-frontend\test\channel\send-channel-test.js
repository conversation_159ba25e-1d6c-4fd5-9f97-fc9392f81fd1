/**
 * T111-T120: 发送渠道管理功能测试
 * 基于需求文档中的发送渠道管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class SendChannelTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T111: 发送渠道管理页面加载测试
   */
  async testT111_SendChannelPageLoad() {
    const testId = 'T111';
    console.log(`\n🧪 执行测试 ${testId}: 发送渠道管理页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到发送渠道管理页面
      await this.testHelper.navigateTo('/channel/send');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isSendChannelTabVisible = await this.testHelper.verifyElementVisibility(selectors.channel.sendChannelTab);
      const isTableVisible = await this.testHelper.verifyElementVisibility(selectors.common.table);
      const isAddButtonVisible = await this.testHelper.verifyElementVisibility(selectors.security.addButton);
      const isTestButtonVisible = await this.testHelper.verifyElementVisibility(selectors.channel.testButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '发送渠道管理页面加载测试',
        testContent: '验证发送渠道管理页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的发送渠道管理界面',
        testInput: '访问发送渠道管理页面URL: /channel/send',
        expectedOutput: '页面正常加载，显示发送渠道标签页、渠道列表和管理按钮',
        actualOutput: `发送渠道标签: ${isSendChannelTabVisible ? '✅显示' : '❌隐藏'}, 渠道列表: ${isTableVisible ? '✅显示' : '❌隐藏'}, 新增按钮: ${isAddButtonVisible ? '✅显示' : '❌隐藏'}, 测试按钮: ${isTestButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isTableVisible && isAddButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '发送渠道管理页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T112: 新增短信发送渠道测试
   */
  async testT112_AddSmsChannel() {
    const testId = 'T112';
    console.log(`\n🧪 执行测试 ${testId}: 新增短信发送渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到发送渠道管理页面
      await this.testHelper.navigateTo('/channel/send');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增发送渠道对话框');
      
      // 生成唯一的渠道信息
      const uniqueChannelName = `短信渠道_${this.testHelper.generateRandomString(6)}`;
      const channelData = {
        name: uniqueChannelName,
        type: 'SMS',
        config: JSON.stringify({
          url: 'http://sms.test.com/api',
          username: 'testuser',
          password: 'testpass',
          signature: '【测试】'
        })
      };
      
      // 填写渠道信息
      await this.testHelper.page.fill(selectors.channel.nameInput, channelData.name);
      
      // 选择渠道类型
      try {
        await this.testHelper.page.click(selectors.channel.typeSelect);
        await this.testHelper.page.click(`${selectors.channel.typeSelect} option[value="SMS"]`);
      } catch (typeError) {
        // 如果没有类型选择器，跳过
      }
      
      // 填写配置信息
      await this.testHelper.page.fill(selectors.channel.configTextarea, channelData.config);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存渠道
      await this.testHelper.page.click(selectors.channel.saveButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增短信发送渠道测试',
        testContent: '创建一个新的短信发送渠道',
        testPurpose: '验证短信发送渠道新增功能能够正常工作',
        testInput: `渠道名称: ${uniqueChannelName}, 类型: SMS, 配置: ${channelData.config}`,
        expectedOutput: '短信发送渠道创建成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增短信发送渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T113: 新增邮件发送渠道测试
   */
  async testT113_AddEmailChannel() {
    const testId = 'T113';
    console.log(`\n🧪 执行测试 ${testId}: 新增邮件发送渠道测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到发送渠道管理页面
      await this.testHelper.navigateTo('/channel/send');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 点击新增按钮
      await this.testHelper.page.click(selectors.security.addButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeCustomScreenshot('新增邮件发送渠道对话框');
      
      // 生成唯一的渠道信息
      const uniqueChannelName = `邮件渠道_${this.testHelper.generateRandomString(6)}`;
      const channelData = {
        name: uniqueChannelName,
        type: 'EMAIL',
        config: JSON.stringify({
          smtp_host: 'smtp.test.com',
          smtp_port: 587,
          username: '<EMAIL>',
          password: 'testpass',
          from_name: '测试发送方'
        })
      };
      
      // 填写渠道信息
      await this.testHelper.page.fill(selectors.channel.nameInput, channelData.name);
      
      // 选择渠道类型
      try {
        await this.testHelper.page.click(selectors.channel.typeSelect);
        await this.testHelper.page.click(`${selectors.channel.typeSelect} option[value="EMAIL"]`);
      } catch (typeError) {
        // 如果没有类型选择器，跳过
      }
      
      // 填写配置信息
      await this.testHelper.page.fill(selectors.channel.configTextarea, channelData.config);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 保存渠道
      await this.testHelper.page.click(selectors.channel.saveButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '新增邮件发送渠道测试',
        testContent: '创建一个新的邮件发送渠道',
        testPurpose: '验证邮件发送渠道新增功能能够正常工作',
        testInput: `渠道名称: ${uniqueChannelName}, 类型: EMAIL, 配置: ${channelData.config}`,
        expectedOutput: '邮件发送渠道创建成功，显示成功提示信息',
        actualOutput: `成功消息: ${successMessage || '无'}`,
        result: successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '新增邮件发送渠道测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T114: 发送渠道连接测试
   */
  async testT114_ChannelConnectionTest() {
    const testId = 'T114';
    console.log(`\n🧪 执行测试 ${testId}: 发送渠道连接测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到发送渠道管理页面
      await this.testHelper.navigateTo('/channel/send');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找第一个测试连接按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.channel.testButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeCustomScreenshot('渠道连接测试执行中');
        
        // 等待测试结果
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取测试结果消息
        const successMessage = await this.testHelper.getSuccessMessage();
        const errorMessage = await this.testHelper.getErrorMessage();
        
        const result = {
          testId: testId,
          testName: '发送渠道连接测试',
          testContent: '测试发送渠道的连接状态',
          testPurpose: '验证发送渠道连接测试功能能够正常工作',
          testInput: '点击测试连接按钮',
          expectedOutput: '显示渠道连接测试结果',
          actualOutput: `成功消息: ${successMessage || '无'}, 错误消息: ${errorMessage || '无'}`,
          result: successMessage || errorMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (testError) {
        // 如果没有测试连接按钮，标记为跳过
        const result = {
          testId: testId,
          testName: '发送渠道连接测试',
          testContent: '测试发送渠道的连接状态',
          testPurpose: '验证发送渠道连接测试功能能够正常工作',
          testInput: '查找测试连接功能',
          expectedOutput: '找到测试按钮并执行连接测试',
          actualOutput: '未找到测试连接功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到测试连接功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '发送渠道连接测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T115: 发送渠道优先级设置测试
   */
  async testT115_ChannelPriorityTest() {
    const testId = 'T115';
    console.log(`\n🧪 执行测试 ${testId}: 发送渠道优先级设置测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到发送渠道管理页面
      await this.testHelper.navigateTo('/channel/send');
      await this.testHelper.waitForPageLoad(selectors.channel.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 查找第一个编辑按钮并点击
      try {
        await this.testHelper.page.click(`${selectors.common.table} ${selectors.security.editButton}:first-child`);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeEditPageScreenshot();
        
        // 尝试修改优先级
        try {
          const prioritySelector = '.priority-input';
          await this.testHelper.page.fill(prioritySelector, '10');
          await this.screenshotHelper.takeFormFilledScreenshot();
          
          // 保存修改
          await this.testHelper.page.click(selectors.channel.saveButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          // 获取成功消息
          const successMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '发送渠道优先级设置测试',
            testContent: '设置发送渠道的优先级',
            testPurpose: '验证发送渠道优先级设置功能能够正常工作',
            testInput: '修改渠道优先级为10',
            expectedOutput: '渠道优先级修改成功，显示成功提示',
            actualOutput: `成功消息: ${successMessage || '无'}`,
            result: successMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } catch (priorityError) {
          throw new Error('未找到优先级设置功能');
        }
        
      } catch (editError) {
        // 如果没有编辑功能或优先级设置，标记为跳过
        const result = {
          testId: testId,
          testName: '发送渠道优先级设置测试',
          testContent: '设置发送渠道的优先级',
          testPurpose: '验证发送渠道优先级设置功能能够正常工作',
          testInput: '查找优先级设置功能',
          expectedOutput: '找到优先级设置并成功修改',
          actualOutput: '未找到优先级设置功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到优先级设置功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '发送渠道优先级设置测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有发送渠道管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行发送渠道管理功能测试套件 (T111-T115)');
    
    const startTime = Date.now();
    
    await this.testT111_SendChannelPageLoad();
    await this.testT112_AddSmsChannel();
    await this.testT113_AddEmailChannel();
    await this.testT114_ChannelConnectionTest();
    await this.testT115_ChannelPriorityTest();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 发送渠道管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const sendChannelTest = new SendChannelTest();
  sendChannelTest.runAllTests().catch(console.error);
}

module.exports = SendChannelTest;
