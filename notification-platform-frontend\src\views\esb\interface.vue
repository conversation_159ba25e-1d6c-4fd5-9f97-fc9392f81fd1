<template>
  <div class="esb-interface">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>ESB接口监控</span>
        </div>
      </template>

      <!-- 接口统计 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-statistic title="今日请求总数" :value="stats.totalRequests" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="成功请求" :value="stats.successRequests" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="失败请求" :value="stats.failureRequests" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="成功率" :value="stats.successRate" suffix="%" />
        </el-col>
      </el-row>

      <!-- 接口列表 -->
      <el-table :data="interfaceList" style="width: 100%; margin-top: 20px;">
        <el-table-column prop="scenesCode" label="场景码" width="100" />
        <el-table-column prop="interfaceName" label="接口名称" width="200" />
        <el-table-column prop="requestCount" label="请求次数" width="120" />
        <el-table-column prop="successCount" label="成功次数" width="120" />
        <el-table-column prop="failureCount" label="失败次数" width="120" />
        <el-table-column prop="successRate" label="成功率" width="100">
          <template #default="scope">
            {{ scope.row.successRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="avgResponseTime" label="平均响应时间" width="140">
          <template #default="scope">
            {{ scope.row.avgResponseTime }}ms
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button size="small" type="info" @click="viewLogs(scope.row)">日志</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>



    <!-- 日志查看对话框 -->
    <el-dialog v-model="showLogDialog" title="接口日志" width="1000px">
      <el-table :data="logList" style="width: 100%;">
        <el-table-column prop="serviceReqSeq" label="请求流水号" width="180" />
        <el-table-column prop="reqSysCode" label="请求系统" width="120" />
        <el-table-column prop="sendStatus" label="发送状态" width="100">
          <template #default="scope">
            <el-tag :type="getSendStatusType(scope.row.sendStatus)">
              {{ getSendStatusText(scope.row.sendStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sendChannel" label="发送渠道" width="100" />
        <el-table-column prop="recipient" label="收件人" width="150" show-overflow-tooltip />
        <el-table-column prop="requestTime" label="请求时间" width="160" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button size="small" @click="viewLogDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="logQuery.page"
        v-model:page-size="logQuery.size"
        :total="logTotal"
        @current-change="loadLogs"
        layout="total, prev, pager, next"
        style="margin-top: 20px; text-align: center;"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getEsbStats, getEsbInterfaces, getEsbLogs } from '@/api/esb'

// 数据定义
const stats = reactive({
  totalRequests: 0,
  successRequests: 0,
  failureRequests: 0,
  successRate: 0
})

const interfaceList = ref([
  { scenesCode: '01', interfaceName: '短信动态码发送', requestCount: 0, successCount: 0, failureCount: 0, successRate: 0, avgResponseTime: 0 },
  { scenesCode: '02', interfaceName: '短信动态码验证', requestCount: 0, successCount: 0, failureCount: 0, successRate: 0, avgResponseTime: 0 },
  { scenesCode: '03', interfaceName: '通用消息发送', requestCount: 0, successCount: 0, failureCount: 0, successRate: 0, avgResponseTime: 0 },
  { scenesCode: '04', interfaceName: '定制模板信息发送', requestCount: 0, successCount: 0, failureCount: 0, successRate: 0, avgResponseTime: 0 },
  { scenesCode: '05', interfaceName: '批量消息发送', requestCount: 0, successCount: 0, failureCount: 0, successRate: 0, avgResponseTime: 0 }
])

const showLogDialog = ref(false)

const logList = ref([])
const logTotal = ref(0)
const logQuery = reactive({
  page: 1,
  size: 10,
  scenesCode: ''
})

// 方法定义
const loadStats = async () => {
  try {
    const response = await getEsbStats()
    if (response.code === 200 && response.data) {
      Object.assign(stats, response.data)
    } else {
      ElMessage.error(response.message || '获取统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  }
}

const loadInterfaceList = async () => {
  try {
    const response = await getEsbInterfaces()
    if (response.code === 200 && response.data && Array.isArray(response.data)) {
      interfaceList.value = response.data
    } else {
      ElMessage.error(response.message || '获取接口列表失败')
    }
  } catch (error) {
    console.error('加载接口列表失败:', error)
    ElMessage.error('获取接口列表失败')
  }
}



const viewLogs = (row) => {
  logQuery.scenesCode = row.scenesCode
  logQuery.page = 1
  loadLogs()
  showLogDialog.value = true
}

const loadLogs = async () => {
  try {
    const params = {
      page: logQuery.page,
      size: logQuery.size,
      scenesCode: logQuery.scenesCode
    }
    const response = await getEsbLogs(params)
    if (response.code === 200 && response.data) {
      logList.value = response.data.records || []
      logTotal.value = response.data.total || 0
    } else {
      ElMessage.error(response.message || '获取日志失败')
    }
  } catch (error) {
    console.error('加载日志失败:', error)
    ElMessage.error('获取日志失败')
  }
}

const getSendStatusText = (status) => {
  const statusMap = {
    0: '队列中',
    1: '发送中',
    2: '发送成功',
    3: '发送失败'
  }
  return statusMap[status] || '未知'
}

const getSendStatusType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'danger'
  }
  return typeMap[status] || 'info'
}

const viewLogDetail = (row) => {
  ElMessage.info('日志详情功能开发中...')
}

// 生命周期
onMounted(() => {
  loadStats()
  loadInterfaceList()
})
</script>

<style scoped>
.esb-interface {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
}

.stats-row {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>