<template>
  <div class="dashboard-container">
    <div class="welcome-message">
      <h2>尊敬的{{ username }}您好！</h2>
      <p>欢迎登录消息中心统一后台管理系统</p>
    </div>
    
    <el-row :gutter="20" v-loading="loading">
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>今日短信发送量</span>
            </div>
          </template>
          <div class="card-content">
            <div class="card-value">{{ statistics.smsSendCount }}</div>
            <div class="card-trend" :class="{ 'up': statistics.smsTrend > 0, 'down': statistics.smsTrend < 0 }" v-if="statistics.smsTrend !== 0">
              {{ statistics.smsTrend > 0 ? '+' : '' }}{{ statistics.smsTrend }}%
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>今日邮件发送量</span>
            </div>
          </template>
          <div class="card-content">
            <div class="card-value">{{ statistics.emailSendCount }}</div>
            <div class="card-trend" :class="{ 'up': statistics.emailTrend > 0, 'down': statistics.emailTrend < 0 }" v-if="statistics.emailTrend !== 0">
              {{ statistics.emailTrend > 0 ? '+' : '' }}{{ statistics.emailTrend }}%
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>短信发送成功率</span>
            </div>
          </template>
          <div class="card-content">
            <div class="card-value">{{ statistics.smsSuccessRate }}%</div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="dashboard-card">
          <template #header>
            <div class="card-header">
              <span>邮件发送成功率</span>
            </div>
          </template>
          <div class="card-content">
            <div class="card-value">{{ statistics.emailSuccessRate }}%</div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>模板统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="dateChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>状态统计</span>
            </div>
          </template>
          <div class="chart-container">
            <div ref="statusChart" class="chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue';
import { getDashboardStatistics } from '@/api/message';
import { getUserInfo } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';

const userInfo = getUserInfo();
const username = ref(userInfo?.username || '管理员');

const statistics = ref({
  smsSendCount: 0,
  emailSendCount: 0,
  smsTrend: 0,
  emailTrend: 0,
  smsSuccessRate: 0,
  emailSuccessRate: 0
});

const loading = ref(false);

const chartData = ref({
  dateStats: [],
  statusStats: [],
  templateStats: [],
  overall: {},
  smsOverall: {},
  emailOverall: {}
});

// 获取统计数据
const fetchStatistics = async () => {
  loading.value = true;
  try {
    // 获取今日数据
    const today = new Date();
    const startTime = new Date(today.getFullYear(), today.getMonth(), today.getDate()).toISOString().slice(0, 19).replace('T', ' ');
    const endTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59).toISOString().slice(0, 19).replace('T', ' ');
    
    const response = await getDashboardStatistics({
      startTime,
      endTime
    });
    
    if (response.code === 200 && response.data) {
      const data = response.data;
      
      // 使用新的分类统计数据
      const smsOverall = data.smsOverall || {};
      const emailOverall = data.emailOverall || {};
      const overall = data.overall || {};
      
      statistics.value = {
        smsSendCount: smsOverall.totalCount || 0,
        emailSendCount: emailOverall.totalCount || 0,
        smsTrend: 0, // 趋势数据需要额外计算，暂时设为0
        emailTrend: 0,
        smsSuccessRate: parseFloat((smsOverall.successRate || 0).toFixed(1)),
        emailSuccessRate: parseFloat((emailOverall.successRate || 0).toFixed(1))
      };
      
      // 设置图表数据
      chartData.value.dateStats = data.dateStats || [];
      chartData.value.statusStats = data.statusStats || [];
      chartData.value.templateStats = data.templateStats || [];
      chartData.value.overall = overall;
      chartData.value.smsOverall = smsOverall;
      chartData.value.emailOverall = emailOverall;
      
      // 初始化图表
      initCharts();
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

const dateChart = ref(null);
const statusChart = ref(null);
let dateChartInstance = null;
let statusChartInstance = null;

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initDateChart();
    initStatusChart();
  });
};

// 初始化模板统计图表
const initDateChart = () => {
  if (!dateChart.value) return;
  
  dateChartInstance = echarts.init(dateChart.value);
  
  // 如果有日期统计数据，使用日期统计
  if (chartData.value.dateStats && chartData.value.dateStats.length > 0) {
    const dates = chartData.value.dateStats.map(item => item.sendDate);
    const totalCounts = chartData.value.dateStats.map(item => item.totalCount);
    const successCounts = chartData.value.dateStats.map(item => item.successCount);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['发送总数', '成功数']
      },
      xAxis: {
        type: 'category',
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '发送总数',
          type: 'bar',
          data: totalCounts,
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '成功数',
          type: 'bar',
          data: successCounts,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    };
    
    dateChartInstance.setOption(option);
  } else {
    // 如果没有日期统计，使用模板统计数据
    const templateNames = chartData.value.templateStats.map(item => 
      item.templateCode || '未知模板'
    );
    const totalCounts = chartData.value.templateStats.map(item => item.totalCount);
    const successCounts = chartData.value.templateStats.map(item => item.successCount);
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['发送总数', '成功数']
      },
      xAxis: {
        type: 'category',
        data: templateNames
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '发送总数',
          type: 'bar',
          data: totalCounts,
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '成功数',
          type: 'bar',
          data: successCounts,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    };
    
    dateChartInstance.setOption(option);
  }
};

// 初始化状态统计图表
const initStatusChart = () => {
  if (!statusChart.value) return;
  
  statusChartInstance = echarts.init(statusChart.value);
  
  let data = [];
  
  // 如果有状态统计数据，使用状态统计
  if (chartData.value.statusStats && chartData.value.statusStats.length > 0) {
    data = chartData.value.statusStats.map(item => ({
      name: item.statusName,
      value: item.count
    }));
  } else {
    // 如果没有状态统计，使用总体统计数据
    const overall = chartData.value.overall;
    const smsOverall = chartData.value.smsOverall;
    const emailOverall = chartData.value.emailOverall;
    
    data = [
      { name: '短信成功', value: smsOverall.successCount || 0 },
      { name: '短信失败', value: smsOverall.failedCount || 0 },
      { name: '邮件成功', value: emailOverall.successCount || 0 },
      { name: '邮件失败', value: emailOverall.failedCount || 0 },
      { name: '发送中', value: (smsOverall.sendingCount || 0) + (emailOverall.sendingCount || 0) },
      { name: '未知', value: (smsOverall.unknownCount || 0) + (emailOverall.unknownCount || 0) }
    ].filter(item => item.value > 0);
  }
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '消息状态',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  
  statusChartInstance.setOption(option);
};

onMounted(() => {
  fetchStatistics();
});



onBeforeUnmount(() => {
  if (dateChartInstance) {
    dateChartInstance.dispose();
  }
  if (statusChartInstance) {
    statusChartInstance.dispose();
  }
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  
  .welcome-message {
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    
    h2 {
      margin: 0;
      font-size: 24px;
      color: #303133;
      
      span {
        color: #1890ff;
      }
    }
    
    p {
      margin: 10px 0 0;
      font-size: 16px;
      color: #606266;
    }
  }
  
  .dashboard-card {
    height: 180px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .card-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100px;
      
      .card-value {
        font-size: 36px;
        font-weight: bold;
        color: #303133;
      }
      
      .card-trend {
        margin-top: 10px;
        font-size: 14px;
        
        &.up {
          color: #67c23a;
        }
        
        &.down {
          color: #f56c6c;
        }
      }
    }
  }
  
  .chart-row {
    margin-top: 20px;
    
    .chart-container {
      height: 300px;
      
      .chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>