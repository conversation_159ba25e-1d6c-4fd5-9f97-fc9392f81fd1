
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>详细测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card.passed { border-left-color: #28a745; }
        .summary-card.failed { border-left-color: #dc3545; }
        .summary-card.skipped { border-left-color: #ffc107; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .passed .number { color: #28a745; }
        .failed .number { color: #dc3545; }
        .skipped .number { color: #ffc107; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-item { background: #fff; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); overflow: hidden; }
        .test-item.passed { border-left: 4px solid #28a745; }
        .test-item.failed { border-left: 4px solid #dc3545; }
        .test-item.skipped { border-left: 4px solid #ffc107; }
        .test-status { display: inline-block; padding: 6px 12px; border-radius: 20px; color: white; font-size: 0.8em; font-weight: bold; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .detail-card { transition: transform 0.2s, box-shadow 0.2s; }
        .detail-card:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
        .screenshot-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px; }
        .screenshot-item { background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05); }
        .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 详细测试报告</h1>
            <p>生成时间: 2025/7/29 15:16:34</p>
            <p>测试持续时间: 66秒</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card passed">
                <h3>通过</h3>
                <div class="number">0</div>
            </div>
            <div class="summary-card failed">
                <h3>失败</h3>
                <div class="number">1</div>
            </div>
            <div class="summary-card skipped">
                <h3>跳过</h3>
                <div class="number">0</div>
            </div>
        </div>

        <div class="section">
            <h2>📋 详细测试报告</h2>
            
                <div class="test-item failed" style="margin-bottom: 30px; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                    <!-- 测试用例头部 -->
                    <div class="test-header" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-bottom: 1px solid #dee2e6;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <h3 style="margin: 0; color: #495057; font-size: 1.2em;">T131 - 模板管理页面加载测试</h3>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <span class="test-status status-failed" style="padding: 6px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold;">FAILED</span>
                                <span style="color: #666; font-size: 0.9em;">⏱️ 13029ms</span>
                            </div>
                        </div>
                    </div>

                    <!-- 测试详细信息 -->
                    <div class="test-details" style="padding: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #007bff;">
                                <h4 style="margin: 0 0 8px 0; color: #007bff; font-size: 0.9em;">📝 测试内容</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">验证模板管理页面能够正常加载并显示所有必要的UI元素</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #28a745;">
                                <h4 style="margin: 0 0 8px 0; color: #28a745; font-size: 0.9em;">🎯 测试目的</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">确保用户能够看到完整的模板管理界面，为后续模板操作提供基础</p>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #ffc107;">
                                <h4 style="margin: 0 0 8px 0; color: #ffc107; font-size: 0.9em;">📥 测试输入</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4; font-family: monospace; background: #fff; padding: 8px; border-radius: 4px;">访问模板管理页面URL: /template/manage</p>
                            </div>
                            <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #17a2b8;">
                                <h4 style="margin: 0 0 8px 0; color: #17a2b8; font-size: 0.9em;">📤 预期输出</h4>
                                <p style="margin: 0; color: #495057; font-size: 0.85em; line-height: 1.4;">页面正常加载，显示搜索表单、数据表格和新增按钮</p>
                            </div>
                        </div>

                        
                        <div class="detail-card" style="background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #6f42c1; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #6f42c1; font-size: 0.9em;">📋 测试步骤</h4>
                            <ol style="margin: 0; padding-left: 20px; color: #495057; font-size: 0.85em; line-height: 1.6;">
                                <li style="margin-bottom: 4px;">1. 访问模板管理页面</li><li style="margin-bottom: 4px;">2. 等待页面加载完成</li><li style="margin-bottom: 4px;">3. 验证模板容器存在</li><li style="margin-bottom: 4px;">4. 验证搜索表单可见</li><li style="margin-bottom: 4px;">5. 验证数据表格可见</li><li style="margin-bottom: 4px;">6. 验证新增按钮可见</li>
                            </ol>
                        </div>

                        
                        <div class="execution-result" style="background: #f8d7da; padding: 15px; border-radius: 6px; border-left: 4px solid #dc3545;">
                            <h4 style="margin: 0 0 8px 0; color: #721c24; font-size: 0.9em;">
                                ❌ 执行结果 - 失败
                            </h4>
                            <p style="margin: 0; color: #721c24; font-size: 0.85em; line-height: 1.4;">
                                测试执行失败，无法验证页面元素
                            </p>
                            <div class="error-details" style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em;">
                                <strong>错误详情:</strong><br>
                                page.waitForSelector: Timeout 10000ms exceeded.
Call log:
[2m  - waiting for locator('.template-container') to be visible[22m

                            </div>
                        </div>
                    </div>

                    
                    <!-- 测试截图区域 -->
                    <div class="test-screenshots" style="margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
                        <h4 style="margin: 0 0 15px 0; color: #495057; font-size: 1em; display: flex; align-items: center; gap: 8px;">
                            📸 执行截图记录
                            <span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.7em;">2张</span>
                        </h4>
                        <div class="screenshot-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 20px;">
                            
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T131-01-登录后状态.png" alt="登录后状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 1</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 15:15:30</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">登录后状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T131-01-登录后状态.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 Unknown</p>
                                        </div>
                                    </div>
                                    <div class="screenshot-item" style="background: #fff; padding: 15px; border-radius: 8px; border: 1px solid #e9ecef; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                        <div style="background: #f8f9fa; padding: 8px; border-radius: 6px; margin-bottom: 12px; text-align: center;">
                                            <img src="screenshots/T131-02-错误状态.png" alt="错误状态" style="max-width: 100%; height: auto; border-radius: 4px; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.1); transition: transform 0.2s;" onclick="window.open(this.src)" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        </div>
                                        <div style="text-align: left;">
                                            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                                                <span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 10px; font-size: 0.7em; font-weight: bold;">步骤 2</span>
                                                <span style="color: #28a745; font-size: 0.75em;">📅 2025/7/29 15:15:41</span>
                                            </div>
                                            <p style="margin: 0 0 6px 0; font-size: 0.9em; font-weight: bold; color: #495057; line-height: 1.3;">错误状态</p>
                                            <p style="margin: 0 0 4px 0; font-size: 0.75em; color: #6c757d; font-family: monospace; background: #f8f9fa; padding: 4px 6px; border-radius: 3px;">T131-02-错误状态.png</p>
                                            <p style="margin: 0; font-size: 0.7em; color: #999;">📁 Unknown</p>
                                        </div>
                                    </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</body>
</html>