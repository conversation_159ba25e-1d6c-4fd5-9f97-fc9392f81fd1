<template>
  <div class="template-list">
    <div class="page-header">
      <h2>模板列表</h2>
      <div class="actions">
        <el-button 
          v-if="permissions.add" 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增模板
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="模板名称">
          <el-input v-model="searchForm.templateName" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="searchForm.templateType" placeholder="请选择模板类型">
            <el-option label="短信模板" value="SMS" />
            <el-option label="邮件模板" value="EMAIL" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchForm.auditStatus" placeholder="请选择审核状态">
            <el-option label="待审核" value="PENDING" />
            <el-option label="已通过" value="APPROVED" />
            <el-option label="已拒绝" value="REJECTED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="templateName" label="模板名称" />
        <el-table-column prop="templateCode" label="模板编码" />
        <el-table-column prop="templateType" label="模板类型">
          <template #default="{ row }">
            <el-tag :type="row.templateType === 'SMS' ? 'primary' : 'success'">
              {{ row.templateType === 'SMS' ? '短信' : '邮件' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="auditStatus" label="审核状态">
          <template #default="{ row }">
            <el-tag :type="getAuditStatusColor(row.auditStatus)">
              {{ getAuditStatusName(row.auditStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" />
        <el-table-column label="操作" width="250">
          <template #default="{ row }">
            <el-button 
              v-if="permissions.view"
              type="text" 
              size="small" 
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              v-if="permissions.edit"
              type="text" 
              size="small" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="permissions.audit && row.auditStatus === 'PENDING'"
              type="text" 
              size="small" 
              @click="handleAudit(row)"
            >
              审核
            </el-button>
            <el-button 
              v-if="permissions.delete"
              type="text" 
              size="small" 
              @click="handleDelete(row)"
              class="danger"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserInfo } from '@/utils/auth';
import { getPageButtonPermissions } from '@/utils/permission-buttons';

// 权限控制
const userInfo = getUserInfo();
const permissions = computed(() => {
  return getPageButtonPermissions('template:list', userInfo?.permissions || []);
});

// 数据定义
const loading = ref(false);
const tableData = ref([]);

// 搜索表单
const searchForm = reactive({
  templateName: '',
  templateType: '',
  auditStatus: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        templateName: '验证码短信模板',
        templateCode: 'SMS_VERIFY_CODE',
        templateType: 'SMS',
        auditStatus: 'APPROVED',
        createdTime: '2025-01-01 10:00:00'
      },
      {
        id: 2,
        templateName: '营销邮件模板',
        templateCode: 'EMAIL_MARKETING',
        templateType: 'EMAIL',
        auditStatus: 'PENDING',
        createdTime: '2025-01-01 11:00:00'
      }
    ];
    pagination.total = 2;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getAuditStatusName = (status) => {
  const statusMap = {
    PENDING: '待审核',
    APPROVED: '已通过',
    REJECTED: '已拒绝'
  };
  return statusMap[status] || '未知';
};

const getAuditStatusColor = (status) => {
  const colorMap = {
    PENDING: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger'
  };
  return colorMap[status] || '';
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    templateName: '',
    templateType: '',
    auditStatus: ''
  });
  handleSearch();
};

const handleAdd = () => {
  ElMessage.info('新增模板功能待实现');
};

const handleView = (row) => {
  ElMessage.info(`查看模板: ${row.templateName}`);
};

const handleEdit = (row) => {
  ElMessage.info(`编辑模板: ${row.templateName}`);
};

const handleAudit = (row) => {
  ElMessage.info(`审核模板: ${row.templateName}`);
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${row.templateName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.template-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.danger {
  color: #f56c6c;
}
</style>