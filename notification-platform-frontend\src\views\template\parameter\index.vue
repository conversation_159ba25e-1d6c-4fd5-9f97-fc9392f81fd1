<template>
  <div class="parameter-management">
    <div class="page-header">
      <h2>参数管理</h2>
      <div class="actions">
        <el-button 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增参数
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="参数名称">
          <el-input v-model="searchForm.paramName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数编码">
          <el-input v-model="searchForm.paramCode" placeholder="请输入参数编码" />
        </el-form-item>
        <el-form-item label="参数类型">
          <el-select v-model="searchForm.paramType" placeholder="请选择参数类型">
            <el-option label="字符串" value="STRING" />
            <el-option label="数字" value="NUMBER" />
            <el-option label="日期" value="DATE" />
            <el-option label="布尔" value="BOOLEAN" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="paramName" label="参数名称" />
        <el-table-column prop="paramCode" label="参数编码" />
        <el-table-column prop="paramType" label="参数类型">
          <template #default="{ row }">
            <el-tag :type="getParamTypeColor(row.paramType)">
              {{ getParamTypeName(row.paramType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="defaultValue" label="默认值" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="required" label="是否必填" width="100">
          <template #default="{ row }">
            <el-tag :type="row.required ? 'danger' : 'info'">
              {{ row.required ? '必填' : '选填' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button 
              type="text" 
              size="small" 
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="text" 
              size="small" 
              @click="handleDelete(row)"
              class="danger"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getParameterPage } from '@/api/parameter';

// 数据定义
const loading = ref(false);
const tableData = ref([]);

// 搜索表单
const searchForm = reactive({
  paramName: '',
  paramCode: '',
  paramType: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    };
    
    const response = await getParameterPage(params);
    if (response.code === 200) {
      // 映射后端字段到前端显示字段
      tableData.value = response.data.records.map(item => ({
        id: item.id,
        paramName: item.paramName,
        paramCode: item.paramCode,
        paramType: item.paramType,
        defaultValue: item.defaultValue,
        description: item.paramDescription,
        required: item.validationRule ? true : false,
        createdTime: item.createTime
      }));
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.message || '加载数据失败');
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const getParamTypeName = (type) => {
  const typeMap = {
    STRING: '字符串',
    NUMBER: '数字',
    DATE: '日期',
    BOOLEAN: '布尔'
  };
  return typeMap[type] || '未知';
};

const getParamTypeColor = (type) => {
  const colorMap = {
    STRING: 'primary',
    NUMBER: 'success',
    DATE: 'warning',
    BOOLEAN: 'info'
  };
  return colorMap[type] || '';
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    paramName: '',
    paramCode: '',
    paramType: ''
  });
  handleSearch();
};

const handleAdd = () => {
  ElMessage.info('新增参数功能待实现');
};

const handleView = (row) => {
  ElMessage.info(`查看参数: ${row.paramName}`);
};

const handleEdit = (row) => {
  ElMessage.info(`编辑参数: ${row.paramName}`);
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除参数"${row.paramName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.parameter-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.danger {
  color: #f56c6c;
}
</style>