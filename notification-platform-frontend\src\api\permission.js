import request from '@/utils/request'

// 查询权限列表
export function getPermissionList() {
  return request({
    url: '/api/permissions',
    method: 'get'
  })
}

// 查询权限树
export function getPermissionTree() {
  return request({
    url: '/api/permissions/tree',
    method: 'get'
  })
}

// 查询权限详情
export function getPermissionById(id) {
  return request({
    url: `/api/permissions/${id}`,
    method: 'get'
  })
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/api/permissions',
    method: 'post',
    data
  })
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/api/permissions/${id}`,
    method: 'put',
    data
  })
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/api/permissions/${id}`,
    method: 'delete'
  })
}

// 查询用户权限
export function getUserPermissions(userId) {
  return request({
    url: `/api/permissions/user/${userId}`,
    method: 'get'
  })
}

// 查询角色权限
export function getRolePermissions(roleId) {
  return request({
    url: `/api/permissions/role/${roleId}`,
    method: 'get'
  })
}