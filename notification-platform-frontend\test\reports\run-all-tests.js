/**
 * 通知平台前端自动化测试运行器
 * 按功能模块执行所有测试案例并生成综合报告
 */

const fs = require('fs');
const path = require('path');

// 导入测试模块
const LoginTest = require('../auth/login-test');
const UserManagementTest = require('../auth/user-management-test');
const RoleManagementTest = require('../auth/role-management-test');
const APIInterfaceTest = require('../api/api-interface-test');
const ChannelConfigTest = require('../auth/channel-config-test');
const SmsSingleTest = require('../message/sms-single-test');
const SmsBatchTest = require('../message/sms-batch-test');
const EmailSingleTest = require('../message/email-single-test');
const EmailBatchTest = require('../message/email-batch-test');
const EmailMarketingTest = require('../message/email-marketing-test');
const DormantAccountTest = require('../message/dormant-account-test');
const TemplateManageTest = require('../template/template-manage-test');
const AccessChannelTest = require('../channel/access-channel-test');
const SendChannelTest = require('../channel/send-channel-test');
const ESBInterfaceTest = require('../channel/esb-interface-test');
const TemplateParameterTest = require('../template/template-parameter-test');
const TemplateTypeTest = require('../template/template-type-test');
const BlacklistTest = require('../security/blacklist-test');
const WhitelistTest = require('../security/whitelist-test');
const KeywordFilterTest = require('../security/keyword-filter-test');
const DashboardTest = require('../statistics/dashboard-test');
const SendDetailTest = require('../statistics/send-detail-test');
const TemplateStatisticsTest = require('../statistics/template-statistics-test');
const PasswordChangeTest = require('../system/password-change-test');
const SystemLogTest = require('../system/system-log-test');
const PermissionTest = require('../system/permission-test');
const SystemConfigTest = require('../system/system-config-test');
const DataBackupTest = require('../system/data-backup-test');
const MessageQueueTest = require('../channel/message-queue-test');
const TemplateVersionTest = require('../template/template-version-test');
const I18nSupportTest = require('../template/i18n-support-test');
const AdvancedSearchTest = require('../statistics/advanced-search-test');
const RealTimeMonitorTest = require('../statistics/real-time-monitor-test');
const SecurityAuditTest = require('../security/security-audit-test');

class TestRunner {
  constructor() {
    this.allResults = [];
    this.moduleResults = {};
    this.startTime = Date.now();
    this.reportDir = './test-results/reports';
    
    // 确保报告目录存在
    this.ensureDirectoryExists();
  }

  /**
   * 确保报告目录存在
   */
  ensureDirectoryExists() {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  /**
   * 运行认证权限模块测试
   */
  async runAuthTests() {
    console.log('\n🔐 开始执行认证权限模块测试');
    console.log('='.repeat(60));

    const authResults = [];

    try {
      // 登录功能测试
      const loginTest = new LoginTest();
      await loginTest.runAllTests();
      authResults.push(...loginTest.testResults);

      // 用户管理测试
      const userTest = new UserManagementTest();
      await userTest.runAllTests();
      authResults.push(...userTest.testResults);

      // 角色管理测试
      const roleTest = new RoleManagementTest();
      await roleTest.runAllTests();
      authResults.push(...roleTest.testResults);

      // API接口测试
      const apiTest = new APIInterfaceTest();
      await apiTest.runAllTests();
      authResults.push(...apiTest.testResults);

      // 渠道配置测试
      const channelConfigTest = new ChannelConfigTest();
      await channelConfigTest.runAllTests();
      authResults.push(...channelConfigTest.testResults);

      this.moduleResults.auth = {
        moduleName: '认证权限模块',
        testRange: 'T001-T040',
        results: authResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...authResults);

    } catch (error) {
      console.error('❌ 认证权限模块测试失败:', error.message);
      this.moduleResults.auth = {
        moduleName: '认证权限模块',
        testRange: 'T001-T040',
        results: authResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行消息发送模块测试
   */
  async runMessageTests() {
    console.log('\n📱 开始执行消息发送模块测试');
    console.log('='.repeat(60));

    const messageResults = [];

    try {
      // 短信单发测试
      const smsTest = new SmsSingleTest();
      await smsTest.runAllTests();
      messageResults.push(...smsTest.testResults);

      // 短信批量发送测试
      const smsBatchTest = new SmsBatchTest();
      await smsBatchTest.runAllTests();
      messageResults.push(...smsBatchTest.testResults);

      // 邮件单发测试
      const emailTest = new EmailSingleTest();
      await emailTest.runAllTests();
      messageResults.push(...emailTest.testResults);

      // 邮件批量发送测试
      const emailBatchTest = new EmailBatchTest();
      await emailBatchTest.runAllTests();
      messageResults.push(...emailBatchTest.testResults);

      // 营销邮件测试
      const emailMarketingTest = new EmailMarketingTest();
      await emailMarketingTest.runAllTests();
      messageResults.push(...emailMarketingTest.testResults);

      // 休眠账户通知测试
      const dormantAccountTest = new DormantAccountTest();
      await dormantAccountTest.runAllTests();
      messageResults.push(...dormantAccountTest.testResults);

      // 数据备份测试
      const dataBackupTest = new DataBackupTest();
      await dataBackupTest.runAllTests();
      messageResults.push(...dataBackupTest.testResults);

      this.moduleResults.message = {
        moduleName: '消息发送模块',
        testRange: 'T041-T100',
        results: messageResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...messageResults);

    } catch (error) {
      console.error('❌ 消息发送模块测试失败:', error.message);
      this.moduleResults.message = {
        moduleName: '消息发送模块',
        testRange: 'T041-T100',
        results: messageResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行模板管理模块测试
   */
  async runTemplateTests() {
    console.log('\n📄 开始执行模板管理模块测试');
    console.log('='.repeat(60));
    
    const templateResults = [];

    try {
      // 模板管理测试
      const templateTest = new TemplateManageTest();
      await templateTest.runAllTests();
      templateResults.push(...templateTest.testResults);

      // 模板参数管理测试
      const templateParameterTest = new TemplateParameterTest();
      await templateParameterTest.runAllTests();
      templateResults.push(...templateParameterTest.testResults);

      // 模板类型管理测试
      const templateTypeTest = new TemplateTypeTest();
      await templateTypeTest.runAllTests();
      templateResults.push(...templateTypeTest.testResults);

      // 模板版本管理测试
      const templateVersionTest = new TemplateVersionTest();
      await templateVersionTest.runAllTests();
      templateResults.push(...templateVersionTest.testResults);

      // 国际化支持测试
      const i18nSupportTest = new I18nSupportTest();
      await i18nSupportTest.runAllTests();
      templateResults.push(...i18nSupportTest.testResults);

      this.moduleResults.template = {
        moduleName: '模板管理模块',
        testRange: 'T131-T160',
        results: templateResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...templateResults);
      
    } catch (error) {
      console.error('❌ 模板管理模块测试失败:', error.message);
      this.moduleResults.template = {
        moduleName: '模板管理模块',
        testRange: 'T131-T160',
        results: templateResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行渠道管理模块测试
   */
  async runChannelTests() {
    console.log('\n🔗 开始执行渠道管理模块测试');
    console.log('='.repeat(60));

    const channelResults = [];

    try {
      // 接入渠道测试
      const accessChannelTest = new AccessChannelTest();
      await accessChannelTest.runAllTests();
      channelResults.push(...accessChannelTest.testResults);

      // 发送渠道测试
      const sendChannelTest = new SendChannelTest();
      await sendChannelTest.runAllTests();
      channelResults.push(...sendChannelTest.testResults);

      // ESB接口管理测试
      const esbInterfaceTest = new ESBInterfaceTest();
      await esbInterfaceTest.runAllTests();
      channelResults.push(...esbInterfaceTest.testResults);

      // 消息队列管理测试
      const messageQueueTest = new MessageQueueTest();
      await messageQueueTest.runAllTests();
      channelResults.push(...messageQueueTest.testResults);

      this.moduleResults.channel = {
        moduleName: '渠道管理模块',
        testRange: 'T101-T130',
        results: channelResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...channelResults);

    } catch (error) {
      console.error('❌ 渠道管理模块测试失败:', error.message);
      this.moduleResults.channel = {
        moduleName: '渠道管理模块',
        testRange: 'T101-T130',
        results: channelResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行统计分析模块测试
   */
  async runStatisticsTests() {
    console.log('\n📊 开始执行统计分析模块测试');
    console.log('='.repeat(60));

    const statisticsResults = [];

    try {
      // 仪表板测试
      const dashboardTest = new DashboardTest();
      await dashboardTest.runAllTests();
      statisticsResults.push(...dashboardTest.testResults);

      // 发送统计详情测试
      const sendDetailTest = new SendDetailTest();
      await sendDetailTest.runAllTests();
      statisticsResults.push(...sendDetailTest.testResults);

      // 模板发送统计测试
      const templateStatisticsTest = new TemplateStatisticsTest();
      await templateStatisticsTest.runAllTests();
      statisticsResults.push(...templateStatisticsTest.testResults);

      // 高级搜索测试
      const advancedSearchTest = new AdvancedSearchTest();
      await advancedSearchTest.runAllTests();
      statisticsResults.push(...advancedSearchTest.testResults);

      // 实时监控测试
      const realTimeMonitorTest = new RealTimeMonitorTest();
      await realTimeMonitorTest.runAllTests();
      statisticsResults.push(...realTimeMonitorTest.testResults);

      this.moduleResults.statistics = {
        moduleName: '统计分析模块',
        testRange: 'T161-T190',
        results: statisticsResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...statisticsResults);

    } catch (error) {
      console.error('❌ 统计分析模块测试失败:', error.message);
      this.moduleResults.statistics = {
        moduleName: '统计分析模块',
        testRange: 'T161-T190',
        results: statisticsResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行安全管理模块测试
   */
  async runSecurityTests() {
    console.log('\n🛡️ 开始执行安全管理模块测试');
    console.log('='.repeat(60));

    const securityResults = [];

    try {
      // 黑名单管理测试
      const blacklistTest = new BlacklistTest();
      await blacklistTest.runAllTests();
      securityResults.push(...blacklistTest.testResults);

      // 白名单管理测试
      const whitelistTest = new WhitelistTest();
      await whitelistTest.runAllTests();
      securityResults.push(...whitelistTest.testResults);

      // 关键字过滤测试
      const keywordFilterTest = new KeywordFilterTest();
      await keywordFilterTest.runAllTests();
      securityResults.push(...keywordFilterTest.testResults);

      // 安全审计测试
      const securityAuditTest = new SecurityAuditTest();
      await securityAuditTest.runAllTests();
      securityResults.push(...securityAuditTest.testResults);

      this.moduleResults.security = {
        moduleName: '安全管理模块',
        testRange: 'T191-T220',
        results: securityResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...securityResults);

    } catch (error) {
      console.error('❌ 安全管理模块测试失败:', error.message);
      this.moduleResults.security = {
        moduleName: '安全管理模块',
        testRange: 'T191-T220',
        results: securityResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行系统设置模块测试
   */
  async runSystemTests() {
    console.log('\n⚙️ 开始执行系统设置模块测试');
    console.log('='.repeat(60));

    const systemResults = [];

    try {
      // 密码修改测试
      const passwordTest = new PasswordChangeTest();
      await passwordTest.runAllTests();
      systemResults.push(...passwordTest.testResults);

      // 系统日志测试
      const systemLogTest = new SystemLogTest();
      await systemLogTest.runAllTests();
      systemResults.push(...systemLogTest.testResults);

      // 权限测试功能测试
      const permissionTest = new PermissionTest();
      await permissionTest.runAllTests();
      systemResults.push(...permissionTest.testResults);

      // 系统配置管理测试
      const systemConfigTest = new SystemConfigTest();
      await systemConfigTest.runAllTests();
      systemResults.push(...systemConfigTest.testResults);

      this.moduleResults.system = {
        moduleName: '系统设置模块',
        testRange: 'T221-T250',
        results: systemResults,
        status: 'COMPLETED'
      };

      this.allResults.push(...systemResults);

    } catch (error) {
      console.error('❌ 系统设置模块测试失败:', error.message);
      this.moduleResults.system = {
        moduleName: '系统设置模块',
        testRange: 'T221-T250',
        results: systemResults,
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 通知平台前端自动化测试开始执行');
    console.log('📅 开始时间:', new Date().toLocaleString('zh-CN'));
    console.log('='.repeat(80));

    // 按模块执行测试
    await this.runAuthTests();
    await this.runMessageTests();
    await this.runTemplateTests();
    await this.runChannelTests();
    await this.runStatisticsTests();
    await this.runSecurityTests();
    await this.runSystemTests();

    // 生成综合报告
    this.generateComprehensiveReport();
  }

  /**
   * 生成综合测试报告
   */
  generateComprehensiveReport() {
    const endTime = Date.now();
    const totalDuration = endTime - this.startTime;
    
    // 统计数据
    const totalTests = this.allResults.length;
    const passedTests = this.allResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.allResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.allResults.filter(r => r.result === 'SKIPPED').length;
    
    // 控制台报告
    this.printConsoleReport(totalTests, passedTests, failedTests, skippedTests, totalDuration);
    
    // 生成JSON报告
    this.generateJSONReport(totalTests, passedTests, failedTests, skippedTests, totalDuration);
    
    // 生成HTML报告
    this.generateHTMLReport(totalTests, passedTests, failedTests, skippedTests, totalDuration);
  }

  /**
   * 打印控制台报告
   */
  printConsoleReport(total, passed, failed, skipped, duration) {
    console.log('\n📊 通知平台前端自动化测试综合报告');
    console.log('='.repeat(80));
    console.log(`📅 执行时间: ${new Date().toLocaleString('zh-CN')}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log(`📋 总测试数: ${total}`);
    console.log(`✅ 通过: ${passed} (${Math.round(passed/total*100)}%)`);
    console.log(`❌ 失败: ${failed} (${Math.round(failed/total*100)}%)`);
    console.log(`⏭️ 跳过: ${skipped} (${Math.round(skipped/total*100)}%)`);
    console.log('='.repeat(80));
    
    // 模块统计
    console.log('\n📊 模块测试统计:');
    Object.values(this.moduleResults).forEach(module => {
      const moduleTotal = module.results.length;
      const modulePassed = module.results.filter(r => r.result === 'PASSED').length;
      const moduleFailed = module.results.filter(r => r.result === 'FAILED').length;
      const moduleSkipped = module.results.filter(r => r.result === 'SKIPPED').length;
      
      console.log(`  ${module.moduleName} (${module.testRange}):`);
      console.log(`    总计: ${moduleTotal}, 通过: ${modulePassed}, 失败: ${moduleFailed}, 跳过: ${moduleSkipped}`);
      console.log(`    状态: ${module.status}`);
      if (module.error) {
        console.log(`    错误: ${module.error}`);
      }
    });
    
    // 失败测试详情
    if (failed > 0) {
      console.log('\n❌ 失败测试详情:');
      this.allResults.filter(r => r.result === 'FAILED').forEach(result => {
        console.log(`  ${result.testId}: ${result.testName}`);
        if (result.error) {
          console.log(`    错误: ${result.error}`);
        }
      });
    }
    
    console.log('\n📁 报告文件:');
    console.log(`  - JSON报告: ${this.reportDir}/test-report.json`);
    console.log(`  - HTML报告: ${this.reportDir}/test-report.html`);
  }

  /**
   * 生成JSON报告
   */
  generateJSONReport(total, passed, failed, skipped, duration) {
    const report = {
      summary: {
        title: '通知平台前端自动化测试报告',
        executionTime: new Date().toISOString(),
        duration: duration,
        totalTests: total,
        passedTests: passed,
        failedTests: failed,
        skippedTests: skipped,
        passRate: Math.round(passed/total*100)
      },
      modules: this.moduleResults,
      testResults: this.allResults,
      environment: {
        platform: process.platform,
        nodeVersion: process.version,
        testFramework: 'Playwright + Custom Framework'
      }
    };
    
    const reportPath = path.join(this.reportDir, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
    
    console.log(`📄 JSON报告已生成: ${reportPath}`);
  }

  /**
   * 生成HTML报告
   */
  generateHTMLReport(total, passed, failed, skipped, duration) {
    const htmlContent = this.generateHTMLContent(total, passed, failed, skipped, duration);
    const reportPath = path.join(this.reportDir, 'test-report.html');
    
    fs.writeFileSync(reportPath, htmlContent, 'utf8');
    
    console.log(`📄 HTML报告已生成: ${reportPath}`);
  }

  /**
   * 生成HTML报告内容
   */
  generateHTMLContent(total, passed, failed, skipped, duration) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知平台前端自动化测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .summary-card.passed { border-left-color: #28a745; }
        .summary-card.failed { border-left-color: #dc3545; }
        .summary-card.skipped { border-left-color: #ffc107; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .passed .number { color: #28a745; }
        .failed .number { color: #dc3545; }
        .skipped .number { color: #ffc107; }
        .module-section { margin-bottom: 30px; }
        .module-section h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .test-item { background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .test-item.passed { border-left-color: #28a745; }
        .test-item.failed { border-left-color: #dc3545; }
        .test-item.skipped { border-left-color: #ffc107; }
        .test-title { font-weight: bold; margin-bottom: 5px; }
        .test-status { display: inline-block; padding: 4px 8px; border-radius: 4px; color: white; font-size: 0.8em; }
        .status-passed { background-color: #28a745; }
        .status-failed { background-color: #dc3545; }
        .status-skipped { background-color: #ffc107; }
        .error-details { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 通知平台前端自动化测试报告</h1>
            <p>生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>测试持续时间: ${Math.round(duration / 1000)}秒</p>
        </div>

        <div class="summary">
            <div class="summary-card">
                <h3>总测试数</h3>
                <div class="number">${total}</div>
            </div>
            <div class="summary-card passed">
                <h3>通过</h3>
                <div class="number">${passed}</div>
            </div>
            <div class="summary-card failed">
                <h3>失败</h3>
                <div class="number">${failed}</div>
            </div>
            <div class="summary-card skipped">
                <h3>跳过</h3>
                <div class="number">${skipped}</div>
            </div>
        </div>

        ${Object.values(this.moduleResults).map(module => `
            <div class="module-section">
                <h2>📋 ${module.moduleName} (${module.testRange})</h2>
                ${module.results.map(test => `
                    <div class="test-item ${test.result.toLowerCase()}">
                        <div class="test-title">${test.testId}: ${test.testName}</div>
                        <span class="test-status status-${test.result.toLowerCase()}">${test.result}</span>
                        ${test.testContent ? `<p><strong>测试内容:</strong> ${test.testContent}</p>` : ''}
                        ${test.testPurpose ? `<p><strong>测试目的:</strong> ${test.testPurpose}</p>` : ''}
                        ${test.testInput ? `<p><strong>测试输入:</strong> ${test.testInput}</p>` : ''}
                        ${test.expectedOutput ? `<p><strong>预期输出:</strong> ${test.expectedOutput}</p>` : ''}
                        ${test.actualOutput ? `<p><strong>实际输出:</strong> ${test.actualOutput}</p>` : ''}
                        ${test.error ? `<div class="error-details">${test.error}</div>` : ''}
                    </div>
                `).join('')}
            </div>
        `).join('')}
    </div>
</body>
</html>`;
  }
}

// 主执行函数
async function main() {
  const runner = new TestRunner();
  
  try {
    await runner.runAllTests();
    console.log('\n🎉 所有测试执行完成！');
  } catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main();
}

module.exports = TestRunner;
