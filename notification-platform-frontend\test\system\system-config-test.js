/**
 * T246-T250: 系统配置管理功能测试
 * 基于需求文档中的系统配置管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class SystemConfigTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T246: 系统配置页面加载测试
   */
  async testT246_SystemConfigPageLoad() {
    const testId = 'T246';
    console.log(`\n🧪 执行测试 ${testId}: 系统配置页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到系统配置页面
      await this.testHelper.navigateTo('/system/config');
      await this.testHelper.waitForPageLoad(selectors.systemConfig.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isConfigTabsVisible = await this.testHelper.verifyElementVisibility(selectors.systemConfig.configTabs);
      const isBasicConfigVisible = await this.testHelper.verifyElementVisibility(selectors.systemConfig.basicConfig);
      const isNotificationConfigVisible = await this.testHelper.verifyElementVisibility(selectors.systemConfig.notificationConfig);
      const isSecurityConfigVisible = await this.testHelper.verifyElementVisibility(selectors.systemConfig.securityConfig);
      const isSaveButtonVisible = await this.testHelper.verifyElementVisibility(selectors.systemConfig.saveButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '系统配置页面加载测试',
        testContent: '验证系统配置页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保管理员能够看到完整的系统配置界面',
        testInput: '访问系统配置页面URL: /system/config',
        expectedOutput: '页面正常加载，显示配置选项卡、基础配置、通知配置、安全配置和保存按钮',
        actualOutput: `配置选项卡: ${isConfigTabsVisible ? '✅显示' : '❌隐藏'}, 基础配置: ${isBasicConfigVisible ? '✅显示' : '❌隐藏'}, 通知配置: ${isNotificationConfigVisible ? '✅显示' : '❌隐藏'}, 安全配置: ${isSecurityConfigVisible ? '✅显示' : '❌隐藏'}, 保存按钮: ${isSaveButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isConfigTabsVisible || isBasicConfigVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '系统配置页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T247: 基础配置修改测试
   */
  async testT247_BasicConfigModification() {
    const testId = 'T247';
    console.log(`\n🧪 执行测试 ${testId}: 基础配置修改测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统配置页面
      await this.testHelper.navigateTo('/system/config');
      await this.testHelper.waitForPageLoad(selectors.systemConfig.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 修改基础配置
      try {
        // 切换到基础配置选项卡
        await this.testHelper.page.click(selectors.systemConfig.basicConfigTab);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('基础配置选项卡');
        
        // 修改系统名称
        const newSystemName = `通知平台_${this.testHelper.generateRandomString(4)}`;
        await this.testHelper.page.fill(selectors.systemConfig.systemNameInput, newSystemName);
        
        // 修改系统描述
        const newSystemDesc = `系统描述更新_${this.testHelper.generateRandomString(4)}`;
        await this.testHelper.page.fill(selectors.systemConfig.systemDescInput, newSystemDesc);
        
        // 修改超时设置
        await this.testHelper.page.fill(selectors.systemConfig.timeoutInput, '60000');
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存配置
        await this.testHelper.page.click(selectors.systemConfig.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '基础配置修改测试',
          testContent: '修改系统基础配置信息',
          testPurpose: '验证基础配置修改功能能够正常工作',
          testInput: `系统名称: ${newSystemName}, 系统描述: ${newSystemDesc}, 超时时间: 60000ms`,
          expectedOutput: '基础配置修改成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (configError) {
        // 如果没有基础配置功能，标记为跳过
        const result = {
          testId: testId,
          testName: '基础配置修改测试',
          testContent: '修改系统基础配置信息',
          testPurpose: '验证基础配置修改功能能够正常工作',
          testInput: '查找基础配置修改功能',
          expectedOutput: '找到基础配置并成功修改',
          actualOutput: '未找到基础配置修改功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到基础配置修改功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '基础配置修改测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T248: 通知配置管理测试
   */
  async testT248_NotificationConfigManagement() {
    const testId = 'T248';
    console.log(`\n🧪 执行测试 ${testId}: 通知配置管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统配置页面
      await this.testHelper.navigateTo('/system/config');
      await this.testHelper.waitForPageLoad(selectors.systemConfig.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 配置通知设置
      try {
        // 切换到通知配置选项卡
        await this.testHelper.page.click(selectors.systemConfig.notificationConfigTab);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('通知配置选项卡');
        
        // 配置短信设置
        await this.testHelper.page.fill(selectors.systemConfig.smsRateLimitInput, '100');
        await this.testHelper.page.fill(selectors.systemConfig.smsDailyLimitInput, '10000');
        
        // 配置邮件设置
        await this.testHelper.page.fill(selectors.systemConfig.emailRateLimitInput, '50');
        await this.testHelper.page.fill(selectors.systemConfig.emailDailyLimitInput, '5000');
        
        // 启用/禁用通知类型
        try {
          await this.testHelper.page.check(selectors.systemConfig.enableSmsCheckbox);
          await this.testHelper.page.check(selectors.systemConfig.enableEmailCheckbox);
        } catch (checkboxError) {
          // 如果没有复选框，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存配置
        await this.testHelper.page.click(selectors.systemConfig.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '通知配置管理测试',
          testContent: '配置系统通知相关设置',
          testPurpose: '验证通知配置管理功能能够正常工作',
          testInput: '短信限制: 100/分钟, 10000/天; 邮件限制: 50/分钟, 5000/天',
          expectedOutput: '通知配置保存成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (notificationError) {
        // 如果没有通知配置功能，标记为跳过
        const result = {
          testId: testId,
          testName: '通知配置管理测试',
          testContent: '配置系统通知相关设置',
          testPurpose: '验证通知配置管理功能能够正常工作',
          testInput: '查找通知配置管理功能',
          expectedOutput: '找到通知配置并成功设置',
          actualOutput: '未找到通知配置管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到通知配置管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '通知配置管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T249: 安全配置管理测试
   */
  async testT249_SecurityConfigManagement() {
    const testId = 'T249';
    console.log(`\n🧪 执行测试 ${testId}: 安全配置管理测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统配置页面
      await this.testHelper.navigateTo('/system/config');
      await this.testHelper.waitForPageLoad(selectors.systemConfig.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 配置安全设置
      try {
        // 切换到安全配置选项卡
        await this.testHelper.page.click(selectors.systemConfig.securityConfigTab);
        await this.testHelper.wait(testData.timeouts.short);
        await this.screenshotHelper.takeCustomScreenshot('安全配置选项卡');
        
        // 配置密码策略
        await this.testHelper.page.fill(selectors.systemConfig.passwordMinLengthInput, '8');
        await this.testHelper.page.fill(selectors.systemConfig.passwordMaxLengthInput, '20');
        
        // 配置登录限制
        await this.testHelper.page.fill(selectors.systemConfig.loginMaxAttemptsInput, '5');
        await this.testHelper.page.fill(selectors.systemConfig.lockoutDurationInput, '30');
        
        // 配置会话超时
        await this.testHelper.page.fill(selectors.systemConfig.sessionTimeoutInput, '3600');
        
        // 启用安全选项
        try {
          await this.testHelper.page.check(selectors.systemConfig.enablePasswordComplexityCheckbox);
          await this.testHelper.page.check(selectors.systemConfig.enableTwoFactorCheckbox);
        } catch (checkboxError) {
          // 如果没有复选框，跳过
        }
        
        await this.screenshotHelper.takeFormFilledScreenshot();
        
        // 保存配置
        await this.testHelper.page.click(selectors.systemConfig.saveButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeAfterSubmitScreenshot();
        
        // 获取成功消息
        const successMessage = await this.testHelper.getSuccessMessage();
        
        const result = {
          testId: testId,
          testName: '安全配置管理测试',
          testContent: '配置系统安全相关设置',
          testPurpose: '验证安全配置管理功能能够正常工作',
          testInput: '密码长度: 8-20位, 登录尝试: 5次, 锁定时间: 30分钟, 会话超时: 3600秒',
          expectedOutput: '安全配置保存成功，显示成功提示信息',
          actualOutput: `成功消息: ${successMessage || '无'}`,
          result: successMessage ? 'PASSED' : 'FAILED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
        
      } catch (securityError) {
        // 如果没有安全配置功能，标记为跳过
        const result = {
          testId: testId,
          testName: '安全配置管理测试',
          testContent: '配置系统安全相关设置',
          testPurpose: '验证安全配置管理功能能够正常工作',
          testInput: '查找安全配置管理功能',
          expectedOutput: '找到安全配置并成功设置',
          actualOutput: '未找到安全配置管理功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到安全配置管理功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '安全配置管理测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T250: 配置备份与恢复测试
   */
  async testT250_ConfigBackupRestore() {
    const testId = 'T250';
    console.log(`\n🧪 执行测试 ${testId}: 配置备份与恢复测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到系统配置页面
      await this.testHelper.navigateTo('/system/config');
      await this.testHelper.waitForPageLoad(selectors.systemConfig.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 测试配置备份
      try {
        await this.testHelper.page.click(selectors.systemConfig.backupButton);
        await this.testHelper.wait(testData.timeouts.medium);
        await this.screenshotHelper.takeCustomScreenshot('配置备份执行');
        
        // 检查备份结果
        const backupSuccessMessage = await this.testHelper.getSuccessMessage();
        
        // 测试配置恢复
        try {
          await this.testHelper.page.click(selectors.systemConfig.restoreButton);
          await this.testHelper.wait(testData.timeouts.short);
          await this.screenshotHelper.takeCustomScreenshot('配置恢复对话框');
          
          // 选择备份文件（模拟）
          try {
            await this.testHelper.page.click('.backup-file-item:first-child');
          } catch (fileError) {
            // 如果没有备份文件列表，跳过
          }
          
          // 确认恢复
          await this.testHelper.page.click(selectors.common.confirmButton);
          await this.testHelper.wait(testData.timeouts.medium);
          await this.screenshotHelper.takeAfterSubmitScreenshot();
          
          // 检查恢复结果
          const restoreSuccessMessage = await this.testHelper.getSuccessMessage();
          
          const result = {
            testId: testId,
            testName: '配置备份与恢复测试',
            testContent: '执行系统配置的备份和恢复操作',
            testPurpose: '验证配置备份与恢复功能能够正常工作',
            testInput: '执行配置备份，然后选择备份文件进行恢复',
            expectedOutput: '配置备份和恢复操作成功完成',
            actualOutput: `备份结果: ${backupSuccessMessage || '无'}, 恢复结果: ${restoreSuccessMessage || '无'}`,
            result: backupSuccessMessage || restoreSuccessMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
          
        } catch (restoreError) {
          // 如果恢复失败，只测试备份
          const result = {
            testId: testId,
            testName: '配置备份与恢复测试',
            testContent: '执行系统配置的备份操作',
            testPurpose: '验证配置备份功能能够正常工作',
            testInput: '执行配置备份',
            expectedOutput: '配置备份操作成功完成',
            actualOutput: `备份结果: ${backupSuccessMessage || '无'}`,
            result: backupSuccessMessage ? 'PASSED' : 'FAILED',
            screenshots: this.screenshotHelper.getScreenshotStats(),
            duration: Date.now()
          };
          
          this.testResults.push(result);
          console.log(`✅ 测试 ${testId} 完成: ${result.result} (仅备份)`);
        }
        
      } catch (backupError) {
        // 如果没有备份恢复功能，标记为跳过
        const result = {
          testId: testId,
          testName: '配置备份与恢复测试',
          testContent: '执行系统配置的备份和恢复操作',
          testPurpose: '验证配置备份与恢复功能能够正常工作',
          testInput: '查找配置备份恢复功能',
          expectedOutput: '找到备份恢复功能并成功执行',
          actualOutput: '未找到配置备份恢复功能',
          result: 'SKIPPED',
          screenshots: this.screenshotHelper.getScreenshotStats(),
          duration: Date.now()
        };
        
        this.testResults.push(result);
        console.log(`⏭️ 测试 ${testId} 跳过: 未找到配置备份恢复功能`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '配置备份与恢复测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有系统配置管理测试
   */
  async runAllTests() {
    console.log('🚀 开始执行系统配置管理功能测试套件 (T246-T250)');
    
    const startTime = Date.now();
    
    await this.testT246_SystemConfigPageLoad();
    await this.testT247_BasicConfigModification();
    await this.testT248_NotificationConfigManagement();
    await this.testT249_SecurityConfigManagement();
    await this.testT250_ConfigBackupRestore();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    const skippedTests = this.testResults.filter(r => r.result === 'SKIPPED').length;
    
    console.log('\n📊 系统配置管理功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏭️ 跳过: ${skippedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const systemConfigTest = new SystemConfigTest();
  systemConfigTest.runAllTests().catch(console.error);
}

module.exports = SystemConfigTest;
