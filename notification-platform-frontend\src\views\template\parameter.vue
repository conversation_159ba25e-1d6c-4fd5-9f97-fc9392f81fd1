<template>
  <div class="parameter-manage-container">
    <!-- 查询表单 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="参数代号">
          <el-input v-model="searchForm.paramCode" placeholder="请输入参数代号" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="参数名称">
          <el-input v-model="searchForm.paramName" placeholder="请输入参数名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="handleAdd">参数新增</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="paramCode" label="参数代号" width="150" />
        <el-table-column prop="paramName" label="参数名称" width="150" />
        <el-table-column prop="paramType" label="参数类型" width="100">
          <template #default="{ row }">
            <el-tag>{{ getParameterTypeText(row.paramType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="参数分类" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)">{{ getCategoryText(row.category) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="validationRule" label="是否必填" width="100">
          <template #default="{ row }">
            <el-tag :type="row.validationRule ? 'danger' : 'info'">
              {{ row.validationRule ? '必填' : '可选' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paramDescription" label="备注" show-overflow-tooltip />
        <el-table-column prop="updateTime" label="修改时间" width="180" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleEdit(row)">修改</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="600px" @close="handleDialogClose">
      <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
        <el-form-item label="参数代号" prop="parameterCode">
          <el-input v-model="formData.parameterCode" placeholder="请输入参数代号，如#receive_name#" />
        </el-form-item>
        <el-form-item label="参数名称" prop="parameterName">
          <el-input v-model="formData.parameterName" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数类型" prop="parameterType">
          <el-select v-model="formData.parameterType" placeholder="请选择参数类型">
            <el-option label="字符串" value="STRING" />
            <el-option label="数字" value="NUMBER" />
            <el-option label="日期" value="DATE" />
            <el-option label="布尔" value="BOOLEAN" />
          </el-select>
        </el-form-item>
        <el-form-item label="参数分类" prop="category">
          <el-select v-model="formData.category" placeholder="请选择参数分类">
            <el-option label="基础参数" value="BASIC" />
            <el-option label="业务参数" value="BUSINESS" />
            <el-option label="系统参数" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否必填" prop="requiredFlag">
          <el-radio-group v-model="formData.requiredFlag">
            <el-radio :label="1">必填</el-radio>
            <el-radio :label="0">可选</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="formData.defaultValue" placeholder="请输入默认值" />
        </el-form-item>
        <el-form-item label="验证规则" prop="validationRule">
          <el-input v-model="formData.validationRule" placeholder="请输入验证规则（正则表达式）" />
        </el-form-item>
        <el-form-item label="排序号" prop="sortOrder">
          <el-input-number v-model="formData.sortOrder" :min="1" :max="999" />
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getParameterPage, createParameter, updateParameter, deleteParameter } from '@/api/parameter'

const loading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const formRef = ref()

const searchForm = reactive({
  paramCode: '',
  paramName: ''
})

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

const tableData = ref([])

const formData = reactive({
  id: null,
  parameterCode: '',
  parameterName: '',
  parameterType: 'STRING',
  category: 'BASIC',
  requiredFlag: 0,
  defaultValue: '',
  validationRule: '',
  sortOrder: 1,
  description: ''
})

const formRules = {
  parameterCode: [{ required: true, message: '请输入参数代号', trigger: 'blur' }],
  parameterName: [{ required: true, message: '请输入参数名称', trigger: 'blur' }],
  parameterType: [{ required: true, message: '请选择参数类型', trigger: 'change' }],
  category: [{ required: true, message: '请选择参数分类', trigger: 'change' }],
  requiredFlag: [{ required: true, message: '请选择是否必填', trigger: 'change' }]
}

const getParameterTypeText = (type) => {
  const typeMap = {
    'STRING': '字符串',
    'NUMBER': '数字',
    'DATE': '日期',
    'BOOLEAN': '布尔'
  }
  return typeMap[type] || type
}

const getCategoryText = (category) => {
  const categoryMap = {
    'BASIC': '基础参数',
    'BUSINESS': '业务参数',
    'SYSTEM': '系统参数'
  }
  return categoryMap[category] || category
}

const getCategoryType = (category) => {
  const typeMap = {
    'BASIC': 'primary',
    'BUSINESS': 'success',
    'SYSTEM': 'warning'
  }
  return typeMap[category] || 'info'
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      current: pagination.current,
      size: pagination.size,
      ...searchForm
    }
    const response = await getParameterPage(params)
    if (response.code === 200) {
      tableData.value = response.data.records || []
      pagination.total = response.data.total || 0
    }
  } catch (error) {
    ElMessage.error('查询失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    paramCode: '',
    paramName: ''
  })
  handleSearch()
}

const handleAdd = () => {
  dialogTitle.value = '参数新增'
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '参数修改'
  isEdit.value = true
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除该参数吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const response = await deleteParameter(row.id)
    if (response.code === 200) {
      ElMessage.success({
        message: '✓ 删除成功',
        showClose: false
      })
      loadData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const apiCall = isEdit.value ? updateParameter : createParameter
    const params = isEdit.value ? [formData.id, formData] : [formData]
    const response = await apiCall(...params)
    
    if (response.code === 200) {
      ElMessage.success({
        message: isEdit.value ? '✓ 修改成功' : '✓ 新增成功',
        showClose: false
      })
      dialogVisible.value = false
      loadData()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败', error)
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
  resetForm()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    parameterCode: '',
    parameterName: '',
    parameterType: 'STRING',
    category: 'BASIC',
    requiredFlag: 0,
    defaultValue: '',
    validationRule: '',
    sortOrder: 1,
    description: ''
  })
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadData()
}

const handleCurrentChange = (current) => {
  pagination.current = current
  loadData()
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.parameter-manage-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}
</style>