// 按钮权限映射配置
export const buttonPermissions = {
  // 系统管理 - 用户管理
  'system:user': {
    view: 'system:user:view',
    add: 'system:user:add',
    edit: 'system:user:edit',
    delete: 'system:user:delete'
  },
  // 系统管理 - 角色管理
  'system:role': {
    view: 'system:role:view',
    add: 'system:role:add',
    edit: 'system:role:edit',
    delete: 'system:role:delete'
  },
  // 系统管理 - 机构管理
  'system:organization': {
    view: 'system:org:view',
    add: 'system:org:add',
    edit: 'system:org:edit',
    delete: 'system:org:delete'
  },
  // 模板管理 - 模板列表
  'template:list': {
    view: 'template:view',
    add: 'template:add',
    edit: 'template:edit',
    delete: 'template:delete',
    audit: 'template:audit'
  },
  // 渠道管理 - 接入渠道
  'channel:access': {
    view: 'channel:access:view',
    add: 'channel:access:add',
    edit: 'channel:access:edit',
    delete: 'channel:access:delete'
  },
  // 渠道管理 - 发送渠道
  'channel:send': {
    view: 'channel:send:view',
    add: 'channel:send:add',
    edit: 'channel:send:edit',
    delete: 'channel:send:delete'
  },
  // 统计监控 - 模板统计
  'statistics:template': {
    view: 'statistics:template:view'
  },
  // 统计监控 - 短信统计
  'statistics:sms': {
    view: 'statistics:message:view'
  },
  // 统计监控 - 邮件统计
  'statistics:email': {
    view: 'statistics:channel:view'
  },
  // 安全管理 - 黑名单管理
  'security:blacklist': {
    view: 'security:blacklist:view',
    add: 'security:blacklist:add',
    edit: 'security:blacklist:edit',
    delete: 'security:blacklist:delete'
  },
  // 安全管理 - 白名单管理
  'security:whitelist': {
    view: 'security:whitelist:view',
    add: 'security:whitelist:add',
    edit: 'security:whitelist:edit',
    delete: 'security:whitelist:delete'
  },
  // 安全管理 - 关键字管理
  'security:keyword': {
    view: 'security:keyword:view',
    add: 'security:keyword:add',
    edit: 'security:keyword:edit',
    delete: 'security:keyword:delete'
  },
  // 日志查看 - 操作日志
  'log:operation': {
    view: 'log:operation:view'
  },
  // 日志查看 - 登录日志
  'log:login': {
    view: 'log:login:view'
  }
};

// ESB接口权限
export const esbPermissions = {
  sendSms: 'esb:send:sms',
  sendEmail: 'esb:send:email',
  sendBatch: 'esb:send:batch',
  templateList: 'esb:template:list',
  channelList: 'esb:channel:list'
};

/**
 * 检查按钮权限
 * @param {string} pagePermission 页面权限码
 * @param {string} action 操作类型 (view, add, edit, delete, audit)
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasButtonPermission(pagePermission, action, userPermissions = []) {
  if (!pagePermission || !action) return false;
  
  const buttonConfig = buttonPermissions[pagePermission];
  if (!buttonConfig) return false;
  
  const requiredPermission = buttonConfig[action];
  if (!requiredPermission) return false;
  
  return userPermissions.includes(requiredPermission);
}

/**
 * 检查ESB接口权限
 * @param {string} esbAction ESB操作类型
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限
 */
export function hasEsbPermission(esbAction, userPermissions = []) {
  const requiredPermission = esbPermissions[esbAction];
  if (!requiredPermission) return false;
  
  return userPermissions.includes(requiredPermission);
}

/**
 * 获取页面所有按钮权限状态
 * @param {string} pagePermission 页面权限码
 * @param {Array} userPermissions 用户权限列表
 * @returns {Object} 按钮权限状态对象
 */
export function getPageButtonPermissions(pagePermission, userPermissions = []) {
  const buttonConfig = buttonPermissions[pagePermission];
  if (!buttonConfig) return {};
  
  const permissions = {};
  Object.keys(buttonConfig).forEach(action => {
    permissions[action] = hasButtonPermission(pagePermission, action, userPermissions);
  });
  
  return permissions;
}