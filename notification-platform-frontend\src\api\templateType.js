import request from '@/utils/request'

// 分页查询模板类型
export function getTemplateTypePage(params) {
  return request({
    url: '/api/template-types/page',
    method: 'get',
    params
  })
}

// 获取所有启用的模板类型
export function getAllEnabledTemplateTypes() {
  return request({
    url: '/api/template-types/all',
    method: 'get'
  })
}

// 根据ID获取模板类型
export function getTemplateTypeById(id) {
  return request({
    url: `/api/template-types/${id}`,
    method: 'get'
  })
}

// 创建模板类型
export function createTemplateType(data) {
  return request({
    url: '/api/template-types',
    method: 'post',
    data
  })
}

// 更新模板类型
export function updateTemplateType(id, data) {
  return request({
    url: `/api/template-types/${id}`,
    method: 'put',
    data
  })
}

// 删除模板类型
export function deleteTemplateType(id) {
  return request({
    url: `/api/template-types/${id}`,
    method: 'delete'
  })
}

// 批量删除模板类型
export function batchDeleteTemplateTypes(ids) {
  return request({
    url: '/api/template-types/batch',
    method: 'delete',
    data: ids
  })
}

// 更新模板类型状态
export function updateTemplateTypeStatus(id, status) {
  return request({
    url: `/api/template-types/${id}/status`,
    method: 'put',
    params: { status }
  })
}