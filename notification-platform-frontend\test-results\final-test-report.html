
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知平台前端测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #333; }
        .summary-card .value { font-size: 2em; font-weight: bold; }
        .success { color: #28a745; }
        .danger { color: #dc3545; }
        .info { color: #17a2b8; }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .results-table th { background-color: #f8f9fa; font-weight: bold; }
        .status-success { color: #28a745; font-weight: bold; }
        .status-failed { color: #dc3545; font-weight: bold; }
        .progress-bar { width: 100%; height: 20px; background-color: #e9ecef; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background-color: #28a745; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 通知平台前端测试报告</h1>
            <p>生成时间: 2025/7/29 14:55:53</p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试模块</h3>
                <div class="value info">34</div>
            </div>
            <div class="summary-card">
                <h3>成功模块</h3>
                <div class="value success">34</div>
            </div>
            <div class="summary-card">
                <h3>失败模块</h3>
                <div class="value danger">0</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value success">100%</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 100%"></div>
                </div>
            </div>
        </div>
        
        <h2>📋 详细测试结果</h2>
        <table class="results-table">
            <thead>
                <tr>
                    <th>#</th>
                    <th>测试模块</th>
                    <th>状态</th>
                    <th>脚本路径</th>
                    <th>退出码</th>
                </tr>
            </thead>
            <tbody>
                
                    <tr>
                        <td>1</td>
                        <td>用户登录测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/auth/login-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>2</td>
                        <td>用户管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/auth/user-management-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>3</td>
                        <td>角色管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/auth/role-management-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>4</td>
                        <td>渠道配置测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/auth/channel-config-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>5</td>
                        <td>API接口测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/api/api-interface-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>6</td>
                        <td>短信单发测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/sms-single-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>7</td>
                        <td>短信批量测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/sms-batch-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>8</td>
                        <td>邮件单发测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/email-single-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>9</td>
                        <td>邮件批量测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/email-batch-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>10</td>
                        <td>营销邮件测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/email-marketing-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>11</td>
                        <td>休眠账户通知测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/message/dormant-account-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>12</td>
                        <td>数据备份测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/system/data-backup-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>13</td>
                        <td>模板管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/template/template-manage-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>14</td>
                        <td>模板参数测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/template/template-parameter-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>15</td>
                        <td>模板类型测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/template/template-type-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>16</td>
                        <td>模板版本管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/template/template-version-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>17</td>
                        <td>国际化支持测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/template/i18n-support-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>18</td>
                        <td>接入渠道测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/channel/access-channel-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>19</td>
                        <td>发送渠道测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/channel/send-channel-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>20</td>
                        <td>ESB接口测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/channel/esb-interface-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>21</td>
                        <td>消息队列管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/channel/message-queue-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>22</td>
                        <td>仪表板统计测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/statistics/dashboard-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>23</td>
                        <td>发送详情统计测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/statistics/send-detail-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>24</td>
                        <td>模板发送统计测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/statistics/template-statistics-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>25</td>
                        <td>高级搜索测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/statistics/advanced-search-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>26</td>
                        <td>实时监控测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/statistics/real-time-monitor-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>27</td>
                        <td>黑名单管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/security/blacklist-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>28</td>
                        <td>白名单管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/security/whitelist-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>29</td>
                        <td>关键字过滤测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/security/keyword-filter-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>30</td>
                        <td>安全审计测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/security/security-audit-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>31</td>
                        <td>密码修改测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/system/password-change-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>32</td>
                        <td>系统日志测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/system/system-log-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>33</td>
                        <td>权限测试功能</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/system/permission-test.js</code></td>
                        <td>0</td>
                    </tr>
                
                    <tr>
                        <td>34</td>
                        <td>系统配置管理测试</td>
                        <td class="status-success">
                            ✅ 成功
                        </td>
                        <td><code>test/system/system-config-test.js</code></td>
                        <td>0</td>
                    </tr>
                
            </tbody>
        </table>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p>📊 测试执行耗时: 1985秒</p>
            <p>🔧 通知平台前端自动化测试套件 v1.0</p>
        </div>
    </div>
</body>
</html>