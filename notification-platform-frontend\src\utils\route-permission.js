import { alignedRoutes } from '@/router/routes-aligned';

/**
 * 根据用户权限过滤路由
 * @param {Array} routes 路由配置
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 过滤后的路由
 */
export function filterRoutesByPermissions(routes, userPermissions = []) {
  return routes.filter(route => {
    // 公共路由直接通过
    if (route.meta?.isPublic) {
      return true;
    }
    
    // 没有权限要求的路由直接通过
    if (!route.meta?.permission) {
      return true;
    }
    
    // 检查是否有对应权限
    const hasPermission = userPermissions.includes(route.meta.permission);
    
    // 如果有子路由，递归过滤
    if (route.children && route.children.length > 0) {
      route.children = filterRoutesByPermissions(route.children, userPermissions);
      // 如果子路由被全部过滤掉，且当前路由需要权限，则过滤掉当前路由
      return hasPermission || route.children.length > 0;
    }
    
    return hasPermission;
  });
}

/**
 * 生成侧边栏菜单数据
 * @param {Array} routes 路由配置
 * @param {Array} userPermissions 用户权限列表
 * @returns {Array} 菜单数据
 */
export function generateMenuData(routes, userPermissions = []) {
  const filteredRoutes = filterRoutesByPermissions(routes, userPermissions);
  
  return filteredRoutes
    .filter(route => {
      // 过滤掉不需要在菜单中显示的路由
      return !route.meta?.hideInMenu && 
             route.path !== '/login' && 
             route.path !== '/403' && 
             !route.path.includes('*');
    })
    .map(route => {
      const menuItem = {
        path: route.path,
        name: route.name,
        title: route.meta?.title,
        icon: route.meta?.icon,
        permission: route.meta?.permission
      };
      
      // 处理子菜单
      if (route.children && route.children.length > 0) {
        menuItem.children = generateMenuData(route.children, userPermissions);
      }
      
      return menuItem;
    });
}

/**
 * 检查路由权限
 * @param {Object} route 路由对象
 * @param {Array} userPermissions 用户权限列表
 * @returns {boolean} 是否有权限访问
 */
export function checkRoutePermission(route, userPermissions = []) {
  // 公共路由直接通过
  if (route.meta?.isPublic) {
    return true;
  }
  
  // 没有权限要求的路由直接通过
  if (!route.meta?.permission) {
    return true;
  }
  
  return userPermissions.includes(route.meta.permission);
}

/**
 * 根据权限数据生成路由配置
 * @param {Array} permissionData 后端权限数据
 * @returns {Array} 路由配置
 */
export function generateRoutesFromPermissions(permissionData) {
  const routes = [];
  
  // 递归处理权限数据
  function processPermissionNode(node, parentPath = '') {
    // resourceType: 1-菜单, 2-按钮, 3-接口
    if (node.resourceType === 1 && node.path) {
      const route = {
        path: node.path,
        name: generateRouteName(node.path),
        component: node.component === 'Layout' ? 
          () => import('@/layout/index.vue') : 
          () => import(`@/views/${node.component}.vue`),
        meta: {
          title: node.permissionName,
          icon: node.icon,
          permission: node.permissionCode,
          sortOrder: node.sortOrder
        }
      };
      
      // 处理子路由
      if (node.children && node.children.length > 0) {
        const children = [];
        node.children.forEach(child => {
          const childRoute = processPermissionNode(child, node.path);
          if (childRoute) {
            children.push(childRoute);
          }
        });
        
        if (children.length > 0) {
          route.children = children;
          // 设置重定向到第一个子路由
          if (children[0]) {
            route.redirect = children[0].path;
          }
        }
      }
      
      return route;
    }
    
    return null;
  }
  
  // 处理顶级权限节点
  permissionData.forEach(node => {
    const route = processPermissionNode(node);
    if (route) {
      routes.push(route);
    }
  });
  
  // 按sortOrder排序
  routes.sort((a, b) => (a.meta.sortOrder || 0) - (b.meta.sortOrder || 0));
  
  return routes;
}

/**
 * 根据路径生成路由名称
 * @param {string} path 路径
 * @returns {string} 路由名称
 */
function generateRouteName(path) {
  return path
    .split('/')
    .filter(segment => segment)
    .map(segment => segment.charAt(0).toUpperCase() + segment.slice(1))
    .join('');
}

/**
 * 获取面包屑导航数据
 * @param {Object} route 当前路由
 * @param {Array} routes 所有路由
 * @returns {Array} 面包屑数据
 */
export function getBreadcrumbData(route, routes) {
  const breadcrumbs = [];
  
  function findRouteInRoutes(targetPath, routeList, parentBreadcrumbs = []) {
    for (const routeItem of routeList) {
      const currentBreadcrumbs = [
        ...parentBreadcrumbs,
        {
          path: routeItem.path,
          title: routeItem.meta?.title || routeItem.name
        }
      ];
      
      if (routeItem.path === targetPath) {
        return currentBreadcrumbs;
      }
      
      if (routeItem.children) {
        const result = findRouteInRoutes(targetPath, routeItem.children, currentBreadcrumbs);
        if (result) {
          return result;
        }
      }
    }
    
    return null;
  }
  
  return findRouteInRoutes(route.path, routes) || [];
}

/**
 * 扁平化路由配置（用于动态添加路由）
 * @param {Array} routes 路由配置
 * @returns {Array} 扁平化的路由数组
 */
export function flattenRoutes(routes) {
  const flattened = [];
  
  function flatten(routeList, parentPath = '') {
    routeList.forEach(route => {
      const fullPath = parentPath + route.path;
      
      flattened.push({
        ...route,
        path: fullPath
      });
      
      if (route.children) {
        flatten(route.children, fullPath);
      }
    });
  }
  
  flatten(routes);
  return flattened;
}