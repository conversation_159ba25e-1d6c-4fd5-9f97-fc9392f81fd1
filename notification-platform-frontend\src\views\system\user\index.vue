<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="actions">
        <el-button 
          v-if="permissions.add" 
          type="primary" 
          @click="handleAdd"
          icon="Plus"
        >
          新增用户
        </el-button>
      </div>
    </div>

    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名">
          <el-input v-model="searchForm.userName" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table :data="tableData" v-loading="loading">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="loginName" label="登录名" width="100" />
        <el-table-column prop="userName" label="用户名" width="120" />
        <el-table-column prop="contact" label="联系方式" width="150" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '锁定' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lastLoginTime" label="最后登录" width="140" show-overflow-tooltip />
        <el-table-column prop="createdTime" label="创建时间" width="140" show-overflow-tooltip />
        <el-table-column label="操作" width="350" fixed="right">
          <template #default="{ row }">
            <el-button 
              v-if="permissions.view"
              type="primary" 
              size="small" 
              plain
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button 
              v-if="permissions.edit"
              type="success" 
              size="small" 
              plain
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button 
              type="warning" 
              size="small" 
              plain
              @click="handleAssignRoles(row)"
            >
              分配角色
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              plain
              @click="handleResetPassword(row)"
            >
              重置密码
            </el-button>
            <el-button 
              v-if="permissions.delete"
              type="danger" 
              size="small" 
              plain
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 用户详情/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
        :disabled="dialogMode === 'view'"
      >
        <el-form-item label="登录名" prop="loginName">
          <el-input v-model="userForm.loginName" :disabled="dialogMode === 'edit'" />
        </el-form-item>
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="userForm.userName" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="userForm.contact" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="userForm.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">锁定</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer v-if="dialogMode !== 'view'">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>

    <!-- 分配角色对话框 -->
    <el-dialog
      v-model="roleDialogVisible"
      title="分配角色"
      width="500px"
    >
      <el-form label-width="80px">
        <el-form-item label="用户">
          <span>{{ currentUser.userName }}（{{ currentUser.loginName }}）</span>
        </el-form-item>
        <el-form-item label="角色">
          <el-checkbox-group v-model="selectedRoles">
            <el-checkbox 
              v-for="role in allRoles" 
              :key="role.id" 
              :label="role.id"
            >
              {{ role.roleName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="roleDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveRoles">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getUserInfo } from '@/utils/auth';
import { getPageButtonPermissions } from '@/utils/permission-buttons';
import { 
  getUserList, 
  createUser, 
  updateUser, 
  deleteUser, 
  resetPassword,
  assignRoles,
  getUserRoles
} from '@/api/user';
import { getRoleList } from '@/api/role';

// 权限控制
const userInfo = getUserInfo();
const permissions = computed(() => {
  return getPageButtonPermissions('system:user', userInfo?.permissions || []);
});

// 数据定义
const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const roleDialogVisible = ref(false);
const dialogMode = ref('add'); // add, edit, view
const userFormRef = ref();
const currentUser = ref({});
const allRoles = ref([]);
const selectedRoles = ref([]);

// 搜索表单
const searchForm = reactive({
  userName: ''
});

// 分页
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
});

// 用户表单
const userForm = reactive({
  id: null,
  loginName: '',
  userName: '',
  contact: '',
  orgId: 1,
  status: 1
});

// 表单验证规则
const userFormRules = {
  loginName: [
    { required: true, message: '请输入登录名', trigger: 'blur' },
    { max: 10, message: '登录名不能超过10个字符', trigger: 'blur' }
  ],
  userName: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { max: 10, message: '用户名不能超过10个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系方式', trigger: 'blur' },
    { max: 30, message: '联系方式不能超过30个字符', trigger: 'blur' }
  ]
};

// 计算属性
const dialogTitle = computed(() => {
  const titles = {
    add: '新增用户',
    edit: '编辑用户',
    view: '查看用户'
  };
  return titles[dialogMode.value];
});

// 方法定义
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getUserList({
      ...searchForm,
      page: pagination.current,
      size: pagination.size
    });
    tableData.value = response.data.records;
    pagination.total = response.data.total;
  } catch (error) {
    ElMessage.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

const loadRoles = async () => {
  try {
    const response = await getRoleList({ page: 1, size: 1000 });
    allRoles.value = response.data.records || response.data;
  } catch (error) {
    ElMessage.error('加载角色列表失败');
  }
};

const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    userName: ''
  });
  handleSearch();
};

const handleAdd = () => {
  dialogMode.value = 'add';
  resetUserForm();
  dialogVisible.value = true;
};

const handleView = (row) => {
  dialogMode.value = 'view';
  Object.assign(userForm, row);
  dialogVisible.value = true;
};

const handleEdit = (row) => {
  dialogMode.value = 'edit';
  Object.assign(userForm, row);
  dialogVisible.value = true;
};

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.userName}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await deleteUser(row.id);
    ElMessage.success('删除成功');
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要重置用户"${row.userName}"的密码吗？重置后密码为：abcd1234`,
      '确认重置密码',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    await resetPassword(row.id);
    ElMessage.success('密码重置成功，新密码为：abcd1234');
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('重置密码失败');
    }
  }
};

const handleAssignRoles = async (row) => {
  currentUser.value = row;
  try {
    const response = await getUserRoles(row.id);
    selectedRoles.value = response.data;
    roleDialogVisible.value = true;
  } catch (error) {
    ElMessage.error('获取用户角色失败');
  }
};

const handleSaveRoles = async () => {
  try {
    await assignRoles(currentUser.value.id, selectedRoles.value);
    ElMessage.success('角色分配成功');
    roleDialogVisible.value = false;
  } catch (error) {
    ElMessage.error('角色分配失败');
  }
};

const handleSubmit = async () => {
  try {
    await userFormRef.value.validate();
    
    if (dialogMode.value === 'add') {
      await createUser(userForm);
      ElMessage.success('新增成功');
    } else {
      await updateUser(userForm.id, userForm);
      ElMessage.success('更新成功');
    }
    
    dialogVisible.value = false;
    loadData();
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

const handleDialogClose = () => {
  resetUserForm();
};

const resetUserForm = () => {
  Object.assign(userForm, {
    id: null,
    loginName: '',
    userName: '',
    contact: '',
    orgId: 1,
    status: 1
  });
  userFormRef.value?.clearValidate();
};

const handleSizeChange = (size) => {
  pagination.size = size;
  loadData();
};

const handleCurrentChange = (current) => {
  pagination.current = current;
  loadData();
};

// 生命周期
onMounted(() => {
  loadData();
  loadRoles();
});
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  background: white;
  padding: 20px;
  border-radius: 4px;
}

.el-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.el-button + .el-button {
  margin-left: 6px;
}

.el-table .el-button {
  padding: 5px 8px;
  font-size: 12px;
}
</style>