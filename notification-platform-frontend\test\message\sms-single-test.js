/**
 * T041-T050: 短信单发功能测试
 * 基于需求文档中的消息发送管理功能
 */

const TestHelper = require('../utils/test-helper');
const ScreenshotHelper = require('../utils/screenshot-helper');
const testData = require('../config/test-data');
const selectors = require('../config/selectors');

class SmsSingleTest {
  constructor() {
    this.testHelper = new TestHelper();
    this.screenshotHelper = null;
    this.testResults = [];
  }

  /**
   * T041: 短信单发页面加载测试
   */
  async testT041_SmsPageLoad() {
    const testId = 'T041';
    console.log(`\n🧪 执行测试 ${testId}: 短信单发页面加载测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      await this.screenshotHelper.takeAfterLoginScreenshot();
      
      // 导航到短信单发页面
      await this.testHelper.navigateTo('/message/sms/single');
      await this.testHelper.waitForPageLoad(selectors.sms.container);
      
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 验证页面元素
      const isPhoneInputVisible = await this.testHelper.verifyElementVisibility(selectors.sms.phoneInput);
      const isContentTextareaVisible = await this.testHelper.verifyElementVisibility(selectors.sms.contentTextarea);
      const isSendButtonVisible = await this.testHelper.verifyElementVisibility(selectors.sms.sendButton);
      
      await this.screenshotHelper.takeCustomScreenshot('页面元素验证完成');
      
      const result = {
        testId: testId,
        testName: '短信单发页面加载测试',
        testContent: '验证短信单发页面能够正常加载并显示所有必要的UI元素',
        testPurpose: '确保用户能够看到完整的短信发送界面',
        testInput: '访问短信单发页面URL: /message/sms/single',
        expectedOutput: '页面正常加载，显示手机号输入框、短信内容输入框和发送按钮',
        actualOutput: `手机号输入框: ${isPhoneInputVisible ? '✅显示' : '❌隐藏'}, 内容输入框: ${isContentTextareaVisible ? '✅显示' : '❌隐藏'}, 发送按钮: ${isSendButtonVisible ? '✅显示' : '❌隐藏'}`,
        result: isPhoneInputVisible && isContentTextareaVisible && isSendButtonVisible ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '短信单发页面加载测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T042: 有效短信发送测试
   */
  async testT042_ValidSmsSend() {
    const testId = 'T042';
    console.log(`\n🧪 执行测试 ${testId}: 有效短信发送测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录并获取发送前统计
      await this.testHelper.login();
      const beforeStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('发送前仪表板统计');
      
      // 导航到短信单发页面
      await this.testHelper.navigateTo('/message/sms/single');
      await this.testHelper.waitForPageLoad(selectors.sms.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写短信信息
      await this.testHelper.page.fill(selectors.sms.phoneInput, testData.sms.validPhone);
      await this.testHelper.page.fill(selectors.sms.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 发送短信
      await this.testHelper.page.click(selectors.sms.sendButton);
      await this.testHelper.wait(testData.timeouts.medium);
      await this.screenshotHelper.takeAfterSubmitScreenshot();
      
      // 获取成功消息
      const successMessage = await this.testHelper.getSuccessMessage();
      
      // 返回仪表板查看统计变化
      await this.testHelper.navigateTo('/dashboard');
      const afterStats = await this.testHelper.getDashboardStats();
      await this.screenshotHelper.takeCustomScreenshot('发送后仪表板统计');
      
      const statsIncreased = afterStats.smsCount > beforeStats.smsCount;
      
      const result = {
        testId: testId,
        testName: '有效短信发送测试',
        testContent: '使用有效的手机号码和短信内容进行短信发送',
        testPurpose: '验证短信发送功能能够正常工作',
        testInput: `手机号: ${testData.sms.validPhone}, 内容: ${testData.sms.content}`,
        expectedOutput: '短信发送成功，显示成功提示，发送量统计增加',
        actualOutput: `成功消息: ${successMessage || '无'}, 发送量变化: ${beforeStats.smsCount} → ${afterStats.smsCount}`,
        result: successMessage && statsIncreased ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '有效短信发送测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T043: 无效手机号码测试
   */
  async testT043_InvalidPhoneNumber() {
    const testId = 'T043';
    console.log(`\n🧪 执行测试 ${testId}: 无效手机号码测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信单发页面
      await this.testHelper.navigateTo('/message/sms/single');
      await this.testHelper.waitForPageLoad(selectors.sms.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写无效手机号码
      await this.testHelper.page.fill(selectors.sms.phoneInput, testData.sms.invalidPhone);
      await this.testHelper.page.fill(selectors.sms.contentTextarea, testData.sms.content);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送短信
      await this.testHelper.page.click(selectors.sms.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '无效手机号码测试',
        testContent: '使用无效的手机号码进行短信发送',
        testPurpose: '验证系统对无效手机号码的验证机制',
        testInput: `手机号: ${testData.sms.invalidPhone}, 内容: ${testData.sms.content}`,
        expectedOutput: '发送失败，显示手机号格式错误的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '无效手机号码测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T044: 空短信内容测试
   */
  async testT044_EmptyContent() {
    const testId = 'T044';
    console.log(`\n🧪 执行测试 ${testId}: 空短信内容测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信单发页面
      await this.testHelper.navigateTo('/message/sms/single');
      await this.testHelper.waitForPageLoad(selectors.sms.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 只填写手机号码，不填内容
      await this.testHelper.page.fill(selectors.sms.phoneInput, testData.sms.validPhone);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送短信
      await this.testHelper.page.click(selectors.sms.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息
      const errorMessage = await this.testHelper.getErrorMessage();
      
      const result = {
        testId: testId,
        testName: '空短信内容测试',
        testContent: '在短信内容为空的情况下尝试发送短信',
        testPurpose: '验证系统对空短信内容的验证机制',
        testInput: `手机号: ${testData.sms.validPhone}, 内容: (空)`,
        expectedOutput: '发送失败，显示短信内容不能为空的提示',
        actualOutput: `错误消息: ${errorMessage || '无'}`,
        result: errorMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '空短信内容测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * T045: 超长短信内容测试
   */
  async testT045_LongContent() {
    const testId = 'T045';
    console.log(`\n🧪 执行测试 ${testId}: 超长短信内容测试`);
    
    try {
      await this.testHelper.init();
      this.screenshotHelper = new ScreenshotHelper(this.testHelper.page, testId);
      
      // 登录系统
      await this.testHelper.login();
      
      // 导航到短信单发页面
      await this.testHelper.navigateTo('/message/sms/single');
      await this.testHelper.waitForPageLoad(selectors.sms.container);
      await this.screenshotHelper.takeInitialScreenshot();
      
      // 填写超长内容
      await this.testHelper.page.fill(selectors.sms.phoneInput, testData.sms.validPhone);
      await this.testHelper.page.fill(selectors.sms.contentTextarea, testData.sms.longContent);
      await this.screenshotHelper.takeFormFilledScreenshot();
      
      // 尝试发送短信
      await this.testHelper.page.click(selectors.sms.sendButton);
      await this.testHelper.wait(testData.timeouts.short);
      await this.screenshotHelper.takeValidationScreenshot();
      
      // 获取错误消息或成功消息
      const errorMessage = await this.testHelper.getErrorMessage();
      const successMessage = await this.testHelper.getSuccessMessage();
      
      const result = {
        testId: testId,
        testName: '超长短信内容测试',
        testContent: '使用超过字符限制的短信内容进行发送',
        testPurpose: '验证系统对短信内容长度的限制机制',
        testInput: `手机号: ${testData.sms.validPhone}, 内容长度: ${testData.sms.longContent.length}字符`,
        expectedOutput: '根据系统设置，可能发送失败并提示字符超限，或者自动截断发送',
        actualOutput: `错误消息: ${errorMessage || '无'}, 成功消息: ${successMessage || '无'}`,
        result: errorMessage || successMessage ? 'PASSED' : 'FAILED',
        screenshots: this.screenshotHelper.getScreenshotStats(),
        duration: Date.now()
      };
      
      this.testResults.push(result);
      console.log(`✅ 测试 ${testId} 完成: ${result.result}`);
      
    } catch (error) {
      console.error(`❌ 测试 ${testId} 失败:`, error.message);
      await this.screenshotHelper.takeErrorScreenshot();
      
      this.testResults.push({
        testId: testId,
        testName: '超长短信内容测试',
        result: 'FAILED',
        error: error.message,
        screenshots: this.screenshotHelper.getScreenshotStats()
      });
    } finally {
      await this.testHelper.close();
    }
  }

  /**
   * 运行所有短信单发测试
   */
  async runAllTests() {
    console.log('🚀 开始执行短信单发功能测试套件 (T041-T045)');
    
    const startTime = Date.now();
    
    await this.testT041_SmsPageLoad();
    await this.testT042_ValidSmsSend();
    await this.testT043_InvalidPhoneNumber();
    await this.testT044_EmptyContent();
    await this.testT045_LongContent();
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // 生成测试报告
    this.generateReport(duration);
  }

  /**
   * 生成测试报告
   */
  generateReport(duration) {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.result === 'PASSED').length;
    const failedTests = this.testResults.filter(r => r.result === 'FAILED').length;
    
    console.log('\n📊 短信单发功能测试报告');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`✅ 通过: ${passedTests}`);
    console.log(`❌ 失败: ${failedTests}`);
    console.log(`⏱️ 总耗时: ${Math.round(duration / 1000)}秒`);
    console.log('='.repeat(50));
    
    // 详细结果
    this.testResults.forEach(result => {
      console.log(`${result.testId}: ${result.testName} - ${result.result}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const smsTest = new SmsSingleTest();
  smsTest.runAllTests().catch(console.error);
}

module.exports = SmsSingleTest;
